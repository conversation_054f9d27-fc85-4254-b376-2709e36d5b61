import { useState } from 'react';
import { Check } from 'lucide-react';
import { CHANNELS } from '../utils/channels';
import { StepChannelsProps } from '../utils/types'; 
import { useEffect } from 'react';
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
const StepChannels = ({ data, setData }: StepChannelsProps) => {
  const [selected, setSelected] = useState<string[]>(data.selectedChannels || []);

  useEffect(() => {
    if (data.selectedChannels) {
      setSelected(data.selectedChannels);
    }
  }, [data.selectedChannels]);

  const toggleChannel = (channel: string) => {
  const isAlreadySelected = selected.includes(channel);
  let updatedChannels: string[];

  if (isAlreadySelected) {
   
    updatedChannels = selected.filter((c) => c !== channel);

  
    const updatedConfigs = { ...data.channelConfigs };
    delete updatedConfigs[channel]; 

    setData({
      ...data,
      selectedChannels: updatedChannels,
      channelConfigs: updatedConfigs, 
    });
  } else {
  
    updatedChannels = [...selected, channel];

    setData({
      ...data,
      selectedChannels: updatedChannels,
    });
  }

  // Update local UI state
  setSelected(updatedChannels);
};

  
  const handlePersonaChange = (value: string) => {
    
    setData({
      ...data,
      agentPersona: value, // 👈 store it in form data
    });
  };

  console.log('selected', selected);

  return (
    <div className="w-full">
    
      <div className="mb-2 text-lg font-semibold text-charcoal">
        Select Channels
      </div>
      <div className="mb-6 text-sm text-steel">
        Choose the platforms you want to configure channel-specific KPIs for.
      </div>

    
      <div className="flex flex-wrap gap-6">
        {CHANNELS.map((ch) => {
          const isSelected = selected.includes(ch.id);
          return (
            <div
              key={ch.id}
              onClick={() => toggleChannel(ch.id)}
              className={`relative flex flex-col items-center justify-center w-44 h-48 rounded-xl cursor-pointer transition-all duration-200 bg-white
                ${
                  isSelected
                    ? 'border-2 border-purple-600 shadow-lg ring-2 ring-purple-100'
                    : 'border-2 border-gray-200 hover:border-purple-400 hover:shadow-md'
                }`}
              style={isSelected ? { borderColor: '#7F56D9' } : {}}
            >
             
              {isSelected && (
                <div className="absolute top-3 right-3 w-5 h-5 rounded-full bg-[#7F56D9] flex items-center justify-center shadow-sm">
                  <Check size={12} className="text-white" strokeWidth={3} />
                </div>
              )}

              
              <img
                src={ch.icon}
                alt={ch.name}
                className="w-14 h-14 mb-4 object-contain"
              />

              
              <div
                className={`font-medium text-center text-sm ${
                  isSelected ? 'text-purple-600' : 'text-charcoal'
                }`}
                style={isSelected ? { color: '#7F56D9' } : {}}
              >
                {ch.name}
              </div>
            </div>
          );
        })}
      </div>
      <div className="mt-6">
        <Label
          htmlFor="persona"
          className="text-sm font-medium text-gray-700 mb-2 block"
        >
          Customize your AI CMO’s overall behavior
        </Label>
        <Textarea
          id="persona"
          placeholder="e.g. Focus on ROI optimization, concise reporting, and data-driven recommendations."
          value={data.agentPersona || ""}
          onChange={(e) => handlePersonaChange(e.target.value)}
          className="min-h-[140px] resize-none focus-visible:ring-[#7F56D9]"
        />
        <p className="text-xs text-gray-500 mt-2">
          Describe how you want your AI CMO to respond — tone, focus, and
          personality. This helps tailor your insights.
        </p>
      </div>
    </div>
  );
};

export default StepChannels;