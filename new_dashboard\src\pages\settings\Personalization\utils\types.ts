export interface Channel {
   id: string;
   name: string;
   icon: string;
}

export interface KPIBenchmark {
   month: string;
   [key: string]: string | number;
}

export interface ChannelKPIConfig {
   primaryKPI: string;
   secondaryKPI: string;
   tertiaryKPI: string;
   weighting: {
      primary: number;
      secondary: number;
      tertiary: number;
   };
   benchmarks: KPIBenchmark[];
   channelPersona?: string;
}

export interface PersonalizationFormData {
   selectedChannels: string[];
   channelConfigs: {
      [channelId: string]: ChannelKPIConfig;
   };
   agentPersona?: string;
}

export interface PersonalizationRawFormData {
   agent_persona: string;
   selected_channels: string[];
   channel_configs: {
      [channelId: string]: ChannelKPIConfig;
   };
}

export interface StepChannelsProps {
   data: PersonalizationFormData;
   setData: (data: PersonalizationFormData) => void;
}
