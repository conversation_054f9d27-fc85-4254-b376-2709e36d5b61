{"version": 3, "sources": ["../../domelementtype/lib/index.js", "../../domhandler/lib/node.js", "../../domhandler/lib/index.js", "../../html-dom-parser/src/client/constants.ts", "../../html-dom-parser/src/client/utilities.ts", "../../html-dom-parser/src/client/domparser.ts", "../../html-dom-parser/src/client/html-to-dom.ts", "../../react-property/lib/possibleStandardNamesOptimized.js", "../../react-property/lib/index.js", "../../html-react-parser/src/utilities.ts", "../../html-react-parser/src/attributes-to-props.ts", "../../html-react-parser/src/dom-to-react.ts", "../../html-react-parser/src/index.ts", "../../html-react-parser/esm/index.mjs"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Doctype = exports.CDATA = exports.Tag = exports.Style = exports.Script = exports.Comment = exports.Directive = exports.Text = exports.Root = exports.isTag = exports.ElementType = void 0;\n/** Types of elements found in htmlparser2's DOM */\nvar ElementType;\n(function (ElementType) {\n    /** Type for the root element of a document */\n    ElementType[\"Root\"] = \"root\";\n    /** Type for Text */\n    ElementType[\"Text\"] = \"text\";\n    /** Type for <? ... ?> */\n    ElementType[\"Directive\"] = \"directive\";\n    /** Type for <!-- ... --> */\n    ElementType[\"Comment\"] = \"comment\";\n    /** Type for <script> tags */\n    ElementType[\"Script\"] = \"script\";\n    /** Type for <style> tags */\n    ElementType[\"Style\"] = \"style\";\n    /** Type for Any tag */\n    ElementType[\"Tag\"] = \"tag\";\n    /** Type for <![CDATA[ ... ]]> */\n    ElementType[\"CDATA\"] = \"cdata\";\n    /** Type for <!doctype ...> */\n    ElementType[\"Doctype\"] = \"doctype\";\n})(ElementType = exports.ElementType || (exports.ElementType = {}));\n/**\n * Tests whether an element is a tag or not.\n *\n * @param elem Element to test\n */\nfunction isTag(elem) {\n    return (elem.type === ElementType.Tag ||\n        elem.type === ElementType.Script ||\n        elem.type === ElementType.Style);\n}\nexports.isTag = isTag;\n// Exports for backwards compatibility\n/** Type for the root element of a document */\nexports.Root = ElementType.Root;\n/** Type for Text */\nexports.Text = ElementType.Text;\n/** Type for <? ... ?> */\nexports.Directive = ElementType.Directive;\n/** Type for <!-- ... --> */\nexports.Comment = ElementType.Comment;\n/** Type for <script> tags */\nexports.Script = ElementType.Script;\n/** Type for <style> tags */\nexports.Style = ElementType.Style;\n/** Type for Any tag */\nexports.Tag = ElementType.Tag;\n/** Type for <![CDATA[ ... ]]> */\nexports.CDATA = ElementType.CDATA;\n/** Type for <!doctype ...> */\nexports.Doctype = ElementType.Doctype;\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cloneNode = exports.hasChildren = exports.isDocument = exports.isDirective = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = exports.Element = exports.Document = exports.CDATA = exports.NodeWithChildren = exports.ProcessingInstruction = exports.Comment = exports.Text = exports.DataNode = exports.Node = void 0;\nvar domelementtype_1 = require(\"domelementtype\");\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nvar Node = /** @class */ (function () {\n    function Node() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    Object.defineProperty(Node.prototype, \"parentNode\", {\n        // Read-write aliases for properties\n        /**\n         * Same as {@link parent}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.parent;\n        },\n        set: function (parent) {\n            this.parent = parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"previousSibling\", {\n        /**\n         * Same as {@link prev}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.prev;\n        },\n        set: function (prev) {\n            this.prev = prev;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"nextSibling\", {\n        /**\n         * Same as {@link next}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.next;\n        },\n        set: function (next) {\n            this.next = next;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    Node.prototype.cloneNode = function (recursive) {\n        if (recursive === void 0) { recursive = false; }\n        return cloneNode(this, recursive);\n    };\n    return Node;\n}());\nexports.Node = Node;\n/**\n * A node that contains some data.\n */\nvar DataNode = /** @class */ (function (_super) {\n    __extends(DataNode, _super);\n    /**\n     * @param data The content of the data node\n     */\n    function DataNode(data) {\n        var _this = _super.call(this) || this;\n        _this.data = data;\n        return _this;\n    }\n    Object.defineProperty(DataNode.prototype, \"nodeValue\", {\n        /**\n         * Same as {@link data}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.data;\n        },\n        set: function (data) {\n            this.data = data;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return DataNode;\n}(Node));\nexports.DataNode = DataNode;\n/**\n * Text within the document.\n */\nvar Text = /** @class */ (function (_super) {\n    __extends(Text, _super);\n    function Text() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Text;\n        return _this;\n    }\n    Object.defineProperty(Text.prototype, \"nodeType\", {\n        get: function () {\n            return 3;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Text;\n}(DataNode));\nexports.Text = Text;\n/**\n * Comments within the document.\n */\nvar Comment = /** @class */ (function (_super) {\n    __extends(Comment, _super);\n    function Comment() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Comment;\n        return _this;\n    }\n    Object.defineProperty(Comment.prototype, \"nodeType\", {\n        get: function () {\n            return 8;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Comment;\n}(DataNode));\nexports.Comment = Comment;\n/**\n * Processing instructions, including doc types.\n */\nvar ProcessingInstruction = /** @class */ (function (_super) {\n    __extends(ProcessingInstruction, _super);\n    function ProcessingInstruction(name, data) {\n        var _this = _super.call(this, data) || this;\n        _this.name = name;\n        _this.type = domelementtype_1.ElementType.Directive;\n        return _this;\n    }\n    Object.defineProperty(ProcessingInstruction.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ProcessingInstruction;\n}(DataNode));\nexports.ProcessingInstruction = ProcessingInstruction;\n/**\n * A `Node` that can have children.\n */\nvar NodeWithChildren = /** @class */ (function (_super) {\n    __extends(NodeWithChildren, _super);\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    function NodeWithChildren(children) {\n        var _this = _super.call(this) || this;\n        _this.children = children;\n        return _this;\n    }\n    Object.defineProperty(NodeWithChildren.prototype, \"firstChild\", {\n        // Aliases\n        /** First child of the node. */\n        get: function () {\n            var _a;\n            return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"lastChild\", {\n        /** Last child of the node. */\n        get: function () {\n            return this.children.length > 0\n                ? this.children[this.children.length - 1]\n                : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"childNodes\", {\n        /**\n         * Same as {@link children}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.children;\n        },\n        set: function (children) {\n            this.children = children;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return NodeWithChildren;\n}(Node));\nexports.NodeWithChildren = NodeWithChildren;\nvar CDATA = /** @class */ (function (_super) {\n    __extends(CDATA, _super);\n    function CDATA() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.CDATA;\n        return _this;\n    }\n    Object.defineProperty(CDATA.prototype, \"nodeType\", {\n        get: function () {\n            return 4;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CDATA;\n}(NodeWithChildren));\nexports.CDATA = CDATA;\n/**\n * The root node of the document.\n */\nvar Document = /** @class */ (function (_super) {\n    __extends(Document, _super);\n    function Document() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Root;\n        return _this;\n    }\n    Object.defineProperty(Document.prototype, \"nodeType\", {\n        get: function () {\n            return 9;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Document;\n}(NodeWithChildren));\nexports.Document = Document;\n/**\n * An element within the DOM.\n */\nvar Element = /** @class */ (function (_super) {\n    __extends(Element, _super);\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    function Element(name, attribs, children, type) {\n        if (children === void 0) { children = []; }\n        if (type === void 0) { type = name === \"script\"\n            ? domelementtype_1.ElementType.Script\n            : name === \"style\"\n                ? domelementtype_1.ElementType.Style\n                : domelementtype_1.ElementType.Tag; }\n        var _this = _super.call(this, children) || this;\n        _this.name = name;\n        _this.attribs = attribs;\n        _this.type = type;\n        return _this;\n    }\n    Object.defineProperty(Element.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"tagName\", {\n        // DOM Level 1 aliases\n        /**\n         * Same as {@link name}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.name;\n        },\n        set: function (name) {\n            this.name = name;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"attributes\", {\n        get: function () {\n            var _this = this;\n            return Object.keys(this.attribs).map(function (name) {\n                var _a, _b;\n                return ({\n                    name: name,\n                    value: _this.attribs[name],\n                    namespace: (_a = _this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                    prefix: (_b = _this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n                });\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Element;\n}(NodeWithChildren));\nexports.Element = Element;\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0, domelementtype_1.isTag)(node);\n}\nexports.isTag = isTag;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype_1.ElementType.CDATA;\n}\nexports.isCDATA = isCDATA;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype_1.ElementType.Text;\n}\nexports.isText = isText;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype_1.ElementType.Comment;\n}\nexports.isComment = isComment;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype_1.ElementType.Directive;\n}\nexports.isDirective = isDirective;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype_1.ElementType.Root;\n}\nexports.isDocument = isDocument;\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\nexports.hasChildren = hasChildren;\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive) {\n    if (recursive === void 0) { recursive = false; }\n    var result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_1 = new Element(node.name, __assign({}, node.attribs), children);\n        children.forEach(function (child) { return (child.parent = clone_1); });\n        if (node.namespace != null) {\n            clone_1.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone_1[\"x-attribsNamespace\"] = __assign({}, node[\"x-attribsNamespace\"]);\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone_1[\"x-attribsPrefix\"] = __assign({}, node[\"x-attribsPrefix\"]);\n        }\n        result = clone_1;\n    }\n    else if (isCDATA(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_2 = new CDATA(children);\n        children.forEach(function (child) { return (child.parent = clone_2); });\n        result = clone_2;\n    }\n    else if (isDocument(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_3 = new Document(children);\n        children.forEach(function (child) { return (child.parent = clone_3); });\n        if (node[\"x-mode\"]) {\n            clone_3[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone_3;\n    }\n    else if (isDirective(node)) {\n        var instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(\"Not implemented yet: \".concat(node.type));\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nexports.cloneNode = cloneNode;\nfunction cloneChildren(childs) {\n    var children = childs.map(function (child) { return cloneNode(child, true); });\n    for (var i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DomHandler = void 0;\nvar domelementtype_1 = require(\"domelementtype\");\nvar node_js_1 = require(\"./node.js\");\n__exportStar(require(\"./node.js\"), exports);\n// Default options\nvar defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nvar DomHandler = /** @class */ (function () {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    function DomHandler(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new node_js_1.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    DomHandler.prototype.onparserinit = function (parser) {\n        this.parser = parser;\n    };\n    // Resets the handler back to starting state\n    DomHandler.prototype.onreset = function () {\n        this.dom = [];\n        this.root = new node_js_1.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    };\n    // Signals the handler that parsing is done\n    DomHandler.prototype.onend = function () {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    };\n    DomHandler.prototype.onerror = function (error) {\n        this.handleCallback(error);\n    };\n    DomHandler.prototype.onclosetag = function () {\n        this.lastNode = null;\n        var elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    };\n    DomHandler.prototype.onopentag = function (name, attribs) {\n        var type = this.options.xmlMode ? domelementtype_1.ElementType.Tag : undefined;\n        var element = new node_js_1.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    };\n    DomHandler.prototype.ontext = function (data) {\n        var lastNode = this.lastNode;\n        if (lastNode && lastNode.type === domelementtype_1.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            var node = new node_js_1.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    };\n    DomHandler.prototype.oncomment = function (data) {\n        if (this.lastNode && this.lastNode.type === domelementtype_1.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        var node = new node_js_1.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    };\n    DomHandler.prototype.oncommentend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.oncdatastart = function () {\n        var text = new node_js_1.Text(\"\");\n        var node = new node_js_1.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    };\n    DomHandler.prototype.oncdataend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.onprocessinginstruction = function (name, data) {\n        var node = new node_js_1.ProcessingInstruction(name, data);\n        this.addNode(node);\n    };\n    DomHandler.prototype.handleCallback = function (error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    };\n    DomHandler.prototype.addNode = function (node) {\n        var parent = this.tagStack[this.tagStack.length - 1];\n        var previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    };\n    return DomHandler;\n}());\nexports.DomHandler = DomHandler;\nexports.default = DomHandler;\n", "/**\n * SVG elements are case-sensitive.\n *\n * @see https://developer.mozilla.org/docs/Web/SVG/Element#svg_elements_a_to_z\n */\nexport const CASE_SENSITIVE_TAG_NAMES = [\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'linearGradient',\n  'radialGradient',\n  'textPath',\n] as const;\n\nexport const CASE_SENSITIVE_TAG_NAMES_MAP = CASE_SENSITIVE_TAG_NAMES.reduce(\n  (accumulator, tagName) => {\n    accumulator[tagName.toLowerCase()] = tagName;\n    return accumulator;\n  },\n  {} as Record<string, string>,\n);\n\nexport const CARRIAGE_RETURN = '\\r';\nexport const CARRIAGE_RETURN_REGEX = new RegExp(CARRIAGE_RETURN, 'g');\nexport const CARRIAGE_RETURN_PLACEHOLDER = `__HTML_DOM_PARSER_CARRIAGE_RETURN_PLACEHOLDER_${Date.now()}__`;\nexport const CARRIAGE_RETURN_PLACEHOLDER_REGEX = new RegExp(\n  CARRIAGE_RETURN_PLACEHOLDER,\n  'g',\n);\n", "import { Comment, Element, ProcessingInstruction, Text } from 'domhandler';\n\nimport type { DOMNode } from '../types';\nimport {\n  CARRIAGE_RETURN,\n  CARRIAGE_RETURN_PLACEHOLDER,\n  CARRIAGE_RETURN_PLACEHOLDER_REGEX,\n  CARRIAGE_RETURN_REGEX,\n  CASE_SENSITIVE_TAG_NAMES_MAP,\n} from './constants';\n\n/**\n * Gets case-sensitive tag name.\n *\n * @param tagName - Tag name in lowercase.\n * @returns - Case-sensitive tag name.\n */\nfunction getCaseSensitiveTagName(tagName: string): string | undefined {\n  return CASE_SENSITIVE_TAG_NAMES_MAP[tagName];\n}\n\n/**\n * Formats DOM attributes to a hash map.\n *\n * @param attributes - List of attributes.\n * @returns - Map of attribute name to value.\n */\nexport function formatAttributes(attributes: NamedNodeMap) {\n  const map: Record<string, string> = {};\n  let index = 0;\n  const attributesLength = attributes.length;\n\n  // `NamedNodeMap` is array-like\n  for (; index < attributesLength; index++) {\n    const attribute = attributes[index];\n    map[attribute.name] = attribute.value;\n  }\n\n  return map;\n}\n\n/**\n * Corrects the tag name if it is case-sensitive (SVG).\n * Otherwise, returns the lowercase tag name (HTML).\n *\n * @param tagName - Lowercase tag name.\n * @returns - Formatted tag name.\n */\nfunction formatTagName(tagName: string): string {\n  tagName = tagName.toLowerCase();\n  const caseSensitiveTagName = getCaseSensitiveTagName(tagName);\n\n  if (caseSensitiveTagName) {\n    return caseSensitiveTagName;\n  }\n\n  return tagName;\n}\n\n/**\n * Escapes special characters before parsing.\n *\n * @param html - The HTML string.\n * @returns - HTML string with escaped special characters.\n */\nexport function escapeSpecialCharacters(html: string): string {\n  return html.replace(CARRIAGE_RETURN_REGEX, CARRIAGE_RETURN_PLACEHOLDER);\n}\n\n/**\n * Reverts escaped special characters back to actual characters.\n *\n * @param text - The text with escaped characters.\n * @returns - Text with escaped characters reverted.\n */\nexport function revertEscapedCharacters(text: string): string {\n  return text.replace(CARRIAGE_RETURN_PLACEHOLDER_REGEX, CARRIAGE_RETURN);\n}\n\n/**\n * Transforms DOM nodes to `domhandler` nodes.\n *\n * @param nodes - DOM nodes.\n * @param parent - Parent node.\n * @param directive - Directive.\n * @returns - Nodes.\n */\nexport function formatDOM(\n  nodes: NodeList,\n  parent: DOMNode | null = null,\n  directive?: string,\n): DOMNode[] {\n  const domNodes = [];\n  let current;\n  let index = 0;\n  const nodesLength = nodes.length;\n\n  for (; index < nodesLength; index++) {\n    const node = nodes[index];\n\n    // set the node data given the type\n    switch (node.nodeType) {\n      case 1: {\n        const tagName = formatTagName(node.nodeName);\n\n        // script, style, or tag\n        current = new Element(\n          tagName,\n          formatAttributes((node as HTMLElement).attributes),\n        );\n\n        current.children = formatDOM(\n          // template children are on content\n          tagName === 'template'\n            ? (node as HTMLTemplateElement).content.childNodes\n            : node.childNodes,\n          current,\n        );\n\n        break;\n      }\n\n      case 3:\n        current = new Text(revertEscapedCharacters(node.nodeValue!));\n        break;\n\n      case 8:\n        current = new Comment(node.nodeValue!);\n        break;\n\n      default:\n        continue;\n    }\n\n    // set previous node next\n    const prev = domNodes[index - 1] || null;\n    if (prev) {\n      prev.next = current;\n    }\n\n    // set properties for current node\n    current.parent = parent as Element;\n    current.prev = prev;\n    current.next = null;\n\n    domNodes.push(current);\n  }\n\n  if (directive) {\n    current = new ProcessingInstruction(\n      directive.substring(0, directive.indexOf(' ')).toLowerCase(),\n      directive,\n    );\n\n    current.next = domNodes[0] || null;\n    current.parent = parent as Element;\n    domNodes.unshift(current);\n\n    if (domNodes[1]) {\n      domNodes[1].prev = domNodes[0];\n    }\n  }\n\n  return domNodes;\n}\n", "import { escapeSpecialCharacters } from './utilities';\n\n// constants\nconst HTML = 'html';\nconst HEAD = 'head';\nconst BODY = 'body';\nconst FIRST_TAG_REGEX = /<([a-zA-Z]+[0-9]?)/; // e.g., <h1>\n\n// match-all-characters in case of newlines (DOTALL)\nconst HEAD_TAG_REGEX = /<head[^]*>/i;\nconst BODY_TAG_REGEX = /<body[^]*>/i;\n\n// falls back to `parseFromString` if `createHTMLDocument` cannot be used\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nlet parseFromDocument = (html: string, tagName?: string): Document => {\n  /* istanbul ignore next */\n  throw new Error(\n    'This browser does not support `document.implementation.createHTMLDocument`',\n  );\n};\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nlet parseFromString = (html: string, tagName?: string): Document => {\n  /* istanbul ignore next */\n  throw new Error(\n    'This browser does not support `DOMParser.prototype.parseFromString`',\n  );\n};\n\nconst DOMParser = typeof window === 'object' && window.DOMParser;\n\n/**\n * DOMParser (performance: slow).\n *\n * @see https://developer.mozilla.org/docs/Web/API/DOMParser#Parsing_an_SVG_or_HTML_document\n */\nif (typeof DOMParser === 'function') {\n  const domParser = new DOMParser();\n  const mimeType = 'text/html';\n\n  /**\n   * Creates an HTML document using `DOMParser.parseFromString`.\n   *\n   * @param html - The HTML string.\n   * @param tagName - The element to render the HTML (with 'body' as fallback).\n   * @returns - Document.\n   */\n  parseFromString = (html: string, tagName?: string): Document => {\n    if (tagName) {\n      /* istanbul ignore next */\n      html = `<${tagName}>${html}</${tagName}>`;\n    }\n\n    return domParser.parseFromString(html, mimeType);\n  };\n\n  parseFromDocument = parseFromString;\n}\n\n/**\n * DOMImplementation (performance: fair).\n *\n * @see https://developer.mozilla.org/docs/Web/API/DOMImplementation/createHTMLDocument\n */\nif (typeof document === 'object' && document.implementation) {\n  const htmlDocument = document.implementation.createHTMLDocument();\n\n  /**\n   * Use HTML document created by `document.implementation.createHTMLDocument`.\n   *\n   * @param html - The HTML string.\n   * @param tagName - The element to render the HTML (with 'body' as fallback).\n   * @returns - Document\n   */\n  parseFromDocument = function (html: string, tagName?: string): Document {\n    if (tagName) {\n      const element = htmlDocument.documentElement.querySelector(tagName);\n\n      if (element) {\n        element.innerHTML = html;\n      }\n\n      return htmlDocument;\n    }\n\n    htmlDocument.documentElement.innerHTML = html;\n    return htmlDocument;\n  };\n}\n\n/**\n * Template (performance: fast).\n *\n * @see https://developer.mozilla.org/docs/Web/HTML/Element/template\n */\nconst template =\n  typeof document === 'object' && document.createElement('template');\n\nlet parseFromTemplate: (html: string) => NodeList;\n\nif (template && template.content) {\n  /**\n   * Uses a template element (content fragment) to parse HTML.\n   *\n   * @param html - HTML string.\n   * @returns - Nodes.\n   */\n  parseFromTemplate = (html: string): NodeList => {\n    template.innerHTML = html;\n    return template.content.childNodes;\n  };\n}\n\n/**\n * Parses HTML string to DOM nodes.\n *\n * @param html - HTML markup.\n * @returns - DOM nodes.\n */\nexport default function domparser(html: string): NodeList {\n  // Escape special characters before parsing\n  html = escapeSpecialCharacters(html);\n\n  const match = html.match(FIRST_TAG_REGEX);\n  const firstTagName = match && match[1] ? match[1].toLowerCase() : '';\n\n  switch (firstTagName) {\n    case HTML: {\n      const doc = parseFromString(html);\n\n      // the created document may come with filler head/body elements,\n      // so make sure to remove them if they don't actually exist\n      if (!HEAD_TAG_REGEX.test(html)) {\n        const element = doc.querySelector(HEAD);\n        element?.parentNode?.removeChild(element);\n      }\n\n      if (!BODY_TAG_REGEX.test(html)) {\n        const element = doc.querySelector(BODY);\n        element?.parentNode?.removeChild(element);\n      }\n\n      return doc.querySelectorAll(HTML);\n    }\n\n    case HEAD:\n    case BODY: {\n      const elements = parseFromDocument(html).querySelectorAll(firstTagName);\n\n      // if there's a sibling element, then return both elements\n      if (BODY_TAG_REGEX.test(html) && HEAD_TAG_REGEX.test(html)) {\n        return elements[0].parentNode!.childNodes;\n      }\n\n      return elements;\n    }\n\n    // low-level tag or text\n    default: {\n      if (parseFromTemplate) {\n        return parseFromTemplate(html);\n      }\n      const element = parseFromDocument(html, BODY).querySelector(BODY);\n      return element!.childNodes;\n    }\n  }\n}\n", "import domparser from './domparser';\nimport { formatDOM } from './utilities';\n\nconst DIRECTIVE_REGEX = /<(![a-zA-Z\\s]+)>/; // e.g., <!doctype html>\n\n/**\n * Parses HTML string to DOM nodes in browser.\n *\n * @param html - HTML markup.\n * @returns - DOM elements.\n */\nexport default function HTMLDOMParser(html: string) {\n  if (typeof html !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!html) {\n    return [];\n  }\n\n  // match directive\n  const match = html.match(DIRECTIVE_REGEX);\n  const directive = match ? match[1] : undefined;\n\n  return formatDOM(domparser(html), null, directive);\n}\n", "// An attribute in which the DOM/SVG standard name is the same as the React prop name (e.g., 'accept').\nvar SAME = 0;\nexports.SAME = SAME;\n\n// An attribute in which the React prop name is the camelcased version of the DOM/SVG standard name (e.g., 'acceptCharset').\nvar CAMELCASE = 1;\nexports.CAMELCASE = CAMELCASE;\n\nexports.possibleStandardNames = {\n  accept: 0,\n  acceptCharset: 1,\n  'accept-charset': 'acceptCharset',\n  accessKey: 1,\n  action: 0,\n  allowFullScreen: 1,\n  alt: 0,\n  as: 0,\n  async: 0,\n  autoCapitalize: 1,\n  autoComplete: 1,\n  autoCorrect: 1,\n  autoFocus: 1,\n  autoPlay: 1,\n  autoSave: 1,\n  capture: 0,\n  cellPadding: 1,\n  cellSpacing: 1,\n  challenge: 0,\n  charSet: 1,\n  checked: 0,\n  children: 0,\n  cite: 0,\n  class: 'className',\n  classID: 1,\n  className: 1,\n  cols: 0,\n  colSpan: 1,\n  content: 0,\n  contentEditable: 1,\n  contextMenu: 1,\n  controls: 0,\n  controlsList: 1,\n  coords: 0,\n  crossOrigin: 1,\n  dangerouslySetInnerHTML: 1,\n  data: 0,\n  dateTime: 1,\n  default: 0,\n  defaultChecked: 1,\n  defaultValue: 1,\n  defer: 0,\n  dir: 0,\n  disabled: 0,\n  disablePictureInPicture: 1,\n  disableRemotePlayback: 1,\n  download: 0,\n  draggable: 0,\n  encType: 1,\n  enterKeyHint: 1,\n  for: 'htmlFor',\n  form: 0,\n  formMethod: 1,\n  formAction: 1,\n  formEncType: 1,\n  formNoValidate: 1,\n  formTarget: 1,\n  frameBorder: 1,\n  headers: 0,\n  height: 0,\n  hidden: 0,\n  high: 0,\n  href: 0,\n  hrefLang: 1,\n  htmlFor: 1,\n  httpEquiv: 1,\n  'http-equiv': 'httpEquiv',\n  icon: 0,\n  id: 0,\n  innerHTML: 1,\n  inputMode: 1,\n  integrity: 0,\n  is: 0,\n  itemID: 1,\n  itemProp: 1,\n  itemRef: 1,\n  itemScope: 1,\n  itemType: 1,\n  keyParams: 1,\n  keyType: 1,\n  kind: 0,\n  label: 0,\n  lang: 0,\n  list: 0,\n  loop: 0,\n  low: 0,\n  manifest: 0,\n  marginWidth: 1,\n  marginHeight: 1,\n  max: 0,\n  maxLength: 1,\n  media: 0,\n  mediaGroup: 1,\n  method: 0,\n  min: 0,\n  minLength: 1,\n  multiple: 0,\n  muted: 0,\n  name: 0,\n  noModule: 1,\n  nonce: 0,\n  noValidate: 1,\n  open: 0,\n  optimum: 0,\n  pattern: 0,\n  placeholder: 0,\n  playsInline: 1,\n  poster: 0,\n  preload: 0,\n  profile: 0,\n  radioGroup: 1,\n  readOnly: 1,\n  referrerPolicy: 1,\n  rel: 0,\n  required: 0,\n  reversed: 0,\n  role: 0,\n  rows: 0,\n  rowSpan: 1,\n  sandbox: 0,\n  scope: 0,\n  scoped: 0,\n  scrolling: 0,\n  seamless: 0,\n  selected: 0,\n  shape: 0,\n  size: 0,\n  sizes: 0,\n  span: 0,\n  spellCheck: 1,\n  src: 0,\n  srcDoc: 1,\n  srcLang: 1,\n  srcSet: 1,\n  start: 0,\n  step: 0,\n  style: 0,\n  summary: 0,\n  tabIndex: 1,\n  target: 0,\n  title: 0,\n  type: 0,\n  useMap: 1,\n  value: 0,\n  width: 0,\n  wmode: 0,\n  wrap: 0,\n  about: 0,\n  accentHeight: 1,\n  'accent-height': 'accentHeight',\n  accumulate: 0,\n  additive: 0,\n  alignmentBaseline: 1,\n  'alignment-baseline': 'alignmentBaseline',\n  allowReorder: 1,\n  alphabetic: 0,\n  amplitude: 0,\n  arabicForm: 1,\n  'arabic-form': 'arabicForm',\n  ascent: 0,\n  attributeName: 1,\n  attributeType: 1,\n  autoReverse: 1,\n  azimuth: 0,\n  baseFrequency: 1,\n  baselineShift: 1,\n  'baseline-shift': 'baselineShift',\n  baseProfile: 1,\n  bbox: 0,\n  begin: 0,\n  bias: 0,\n  by: 0,\n  calcMode: 1,\n  capHeight: 1,\n  'cap-height': 'capHeight',\n  clip: 0,\n  clipPath: 1,\n  'clip-path': 'clipPath',\n  clipPathUnits: 1,\n  clipRule: 1,\n  'clip-rule': 'clipRule',\n  color: 0,\n  colorInterpolation: 1,\n  'color-interpolation': 'colorInterpolation',\n  colorInterpolationFilters: 1,\n  'color-interpolation-filters': 'colorInterpolationFilters',\n  colorProfile: 1,\n  'color-profile': 'colorProfile',\n  colorRendering: 1,\n  'color-rendering': 'colorRendering',\n  contentScriptType: 1,\n  contentStyleType: 1,\n  cursor: 0,\n  cx: 0,\n  cy: 0,\n  d: 0,\n  datatype: 0,\n  decelerate: 0,\n  descent: 0,\n  diffuseConstant: 1,\n  direction: 0,\n  display: 0,\n  divisor: 0,\n  dominantBaseline: 1,\n  'dominant-baseline': 'dominantBaseline',\n  dur: 0,\n  dx: 0,\n  dy: 0,\n  edgeMode: 1,\n  elevation: 0,\n  enableBackground: 1,\n  'enable-background': 'enableBackground',\n  end: 0,\n  exponent: 0,\n  externalResourcesRequired: 1,\n  fill: 0,\n  fillOpacity: 1,\n  'fill-opacity': 'fillOpacity',\n  fillRule: 1,\n  'fill-rule': 'fillRule',\n  filter: 0,\n  filterRes: 1,\n  filterUnits: 1,\n  floodOpacity: 1,\n  'flood-opacity': 'floodOpacity',\n  floodColor: 1,\n  'flood-color': 'floodColor',\n  focusable: 0,\n  fontFamily: 1,\n  'font-family': 'fontFamily',\n  fontSize: 1,\n  'font-size': 'fontSize',\n  fontSizeAdjust: 1,\n  'font-size-adjust': 'fontSizeAdjust',\n  fontStretch: 1,\n  'font-stretch': 'fontStretch',\n  fontStyle: 1,\n  'font-style': 'fontStyle',\n  fontVariant: 1,\n  'font-variant': 'fontVariant',\n  fontWeight: 1,\n  'font-weight': 'fontWeight',\n  format: 0,\n  from: 0,\n  fx: 0,\n  fy: 0,\n  g1: 0,\n  g2: 0,\n  glyphName: 1,\n  'glyph-name': 'glyphName',\n  glyphOrientationHorizontal: 1,\n  'glyph-orientation-horizontal': 'glyphOrientationHorizontal',\n  glyphOrientationVertical: 1,\n  'glyph-orientation-vertical': 'glyphOrientationVertical',\n  glyphRef: 1,\n  gradientTransform: 1,\n  gradientUnits: 1,\n  hanging: 0,\n  horizAdvX: 1,\n  'horiz-adv-x': 'horizAdvX',\n  horizOriginX: 1,\n  'horiz-origin-x': 'horizOriginX',\n  ideographic: 0,\n  imageRendering: 1,\n  'image-rendering': 'imageRendering',\n  in2: 0,\n  in: 0,\n  inlist: 0,\n  intercept: 0,\n  k1: 0,\n  k2: 0,\n  k3: 0,\n  k4: 0,\n  k: 0,\n  kernelMatrix: 1,\n  kernelUnitLength: 1,\n  kerning: 0,\n  keyPoints: 1,\n  keySplines: 1,\n  keyTimes: 1,\n  lengthAdjust: 1,\n  letterSpacing: 1,\n  'letter-spacing': 'letterSpacing',\n  lightingColor: 1,\n  'lighting-color': 'lightingColor',\n  limitingConeAngle: 1,\n  local: 0,\n  markerEnd: 1,\n  'marker-end': 'markerEnd',\n  markerHeight: 1,\n  markerMid: 1,\n  'marker-mid': 'markerMid',\n  markerStart: 1,\n  'marker-start': 'markerStart',\n  markerUnits: 1,\n  markerWidth: 1,\n  mask: 0,\n  maskContentUnits: 1,\n  maskUnits: 1,\n  mathematical: 0,\n  mode: 0,\n  numOctaves: 1,\n  offset: 0,\n  opacity: 0,\n  operator: 0,\n  order: 0,\n  orient: 0,\n  orientation: 0,\n  origin: 0,\n  overflow: 0,\n  overlinePosition: 1,\n  'overline-position': 'overlinePosition',\n  overlineThickness: 1,\n  'overline-thickness': 'overlineThickness',\n  paintOrder: 1,\n  'paint-order': 'paintOrder',\n  panose1: 0,\n  'panose-1': 'panose1',\n  pathLength: 1,\n  patternContentUnits: 1,\n  patternTransform: 1,\n  patternUnits: 1,\n  pointerEvents: 1,\n  'pointer-events': 'pointerEvents',\n  points: 0,\n  pointsAtX: 1,\n  pointsAtY: 1,\n  pointsAtZ: 1,\n  prefix: 0,\n  preserveAlpha: 1,\n  preserveAspectRatio: 1,\n  primitiveUnits: 1,\n  property: 0,\n  r: 0,\n  radius: 0,\n  refX: 1,\n  refY: 1,\n  renderingIntent: 1,\n  'rendering-intent': 'renderingIntent',\n  repeatCount: 1,\n  repeatDur: 1,\n  requiredExtensions: 1,\n  requiredFeatures: 1,\n  resource: 0,\n  restart: 0,\n  result: 0,\n  results: 0,\n  rotate: 0,\n  rx: 0,\n  ry: 0,\n  scale: 0,\n  security: 0,\n  seed: 0,\n  shapeRendering: 1,\n  'shape-rendering': 'shapeRendering',\n  slope: 0,\n  spacing: 0,\n  specularConstant: 1,\n  specularExponent: 1,\n  speed: 0,\n  spreadMethod: 1,\n  startOffset: 1,\n  stdDeviation: 1,\n  stemh: 0,\n  stemv: 0,\n  stitchTiles: 1,\n  stopColor: 1,\n  'stop-color': 'stopColor',\n  stopOpacity: 1,\n  'stop-opacity': 'stopOpacity',\n  strikethroughPosition: 1,\n  'strikethrough-position': 'strikethroughPosition',\n  strikethroughThickness: 1,\n  'strikethrough-thickness': 'strikethroughThickness',\n  string: 0,\n  stroke: 0,\n  strokeDasharray: 1,\n  'stroke-dasharray': 'strokeDasharray',\n  strokeDashoffset: 1,\n  'stroke-dashoffset': 'strokeDashoffset',\n  strokeLinecap: 1,\n  'stroke-linecap': 'strokeLinecap',\n  strokeLinejoin: 1,\n  'stroke-linejoin': 'strokeLinejoin',\n  strokeMiterlimit: 1,\n  'stroke-miterlimit': 'strokeMiterlimit',\n  strokeWidth: 1,\n  'stroke-width': 'strokeWidth',\n  strokeOpacity: 1,\n  'stroke-opacity': 'strokeOpacity',\n  suppressContentEditableWarning: 1,\n  suppressHydrationWarning: 1,\n  surfaceScale: 1,\n  systemLanguage: 1,\n  tableValues: 1,\n  targetX: 1,\n  targetY: 1,\n  textAnchor: 1,\n  'text-anchor': 'textAnchor',\n  textDecoration: 1,\n  'text-decoration': 'textDecoration',\n  textLength: 1,\n  textRendering: 1,\n  'text-rendering': 'textRendering',\n  to: 0,\n  transform: 0,\n  typeof: 0,\n  u1: 0,\n  u2: 0,\n  underlinePosition: 1,\n  'underline-position': 'underlinePosition',\n  underlineThickness: 1,\n  'underline-thickness': 'underlineThickness',\n  unicode: 0,\n  unicodeBidi: 1,\n  'unicode-bidi': 'unicodeBidi',\n  unicodeRange: 1,\n  'unicode-range': 'unicodeRange',\n  unitsPerEm: 1,\n  'units-per-em': 'unitsPerEm',\n  unselectable: 0,\n  vAlphabetic: 1,\n  'v-alphabetic': 'vAlphabetic',\n  values: 0,\n  vectorEffect: 1,\n  'vector-effect': 'vectorEffect',\n  version: 0,\n  vertAdvY: 1,\n  'vert-adv-y': 'vertAdvY',\n  vertOriginX: 1,\n  'vert-origin-x': 'vertOriginX',\n  vertOriginY: 1,\n  'vert-origin-y': 'vertOriginY',\n  vHanging: 1,\n  'v-hanging': 'vHanging',\n  vIdeographic: 1,\n  'v-ideographic': 'vIdeographic',\n  viewBox: 1,\n  viewTarget: 1,\n  visibility: 0,\n  vMathematical: 1,\n  'v-mathematical': 'vMathematical',\n  vocab: 0,\n  widths: 0,\n  wordSpacing: 1,\n  'word-spacing': 'wordSpacing',\n  writingMode: 1,\n  'writing-mode': 'writingMode',\n  x1: 0,\n  x2: 0,\n  x: 0,\n  xChannelSelector: 1,\n  xHeight: 1,\n  'x-height': 'xHeight',\n  xlinkActuate: 1,\n  'xlink:actuate': 'xlinkActuate',\n  xlinkArcrole: 1,\n  'xlink:arcrole': 'xlinkArcrole',\n  xlinkHref: 1,\n  'xlink:href': 'xlinkHref',\n  xlinkRole: 1,\n  'xlink:role': 'xlinkRole',\n  xlinkShow: 1,\n  'xlink:show': 'xlinkShow',\n  xlinkTitle: 1,\n  'xlink:title': 'xlinkTitle',\n  xlinkType: 1,\n  'xlink:type': 'xlinkType',\n  xmlBase: 1,\n  'xml:base': 'xmlBase',\n  xmlLang: 1,\n  'xml:lang': 'xmlLang',\n  xmlns: 0,\n  'xml:space': 'xmlSpace',\n  xmlnsXlink: 1,\n  'xmlns:xlink': 'xmlnsXlink',\n  xmlSpace: 1,\n  y1: 0,\n  y2: 0,\n  y: 0,\n  yChannelSelector: 1,\n  z: 0,\n  zoomAndPan: 1\n};\n", "'use strict';\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n\n\n\n// A reserved attribute.\n// It is handled by React separately and shouldn't be written to the DOM.\nconst RESERVED = 0;\n\n// A simple string attribute.\n// Attributes that aren't in the filter are presumed to have this type.\nconst STRING = 1;\n\n// A string attribute that accepts booleans in React. In HTML, these are called\n// \"enumerated\" attributes with \"true\" and \"false\" as possible values.\n// When true, it should be set to a \"true\" string.\n// When false, it should be set to a \"false\" string.\nconst BOOLEANISH_STRING = 2;\n\n// A real boolean attribute.\n// When true, it should be present (set either to an empty string or its name).\n// When false, it should be omitted.\nconst BOOLEAN = 3;\n\n// An attribute that can be used as a flag as well as with a value.\n// When true, it should be present (set either to an empty string or its name).\n// When false, it should be omitted.\n// For any other value, should be present with that value.\nconst OVERLOADED_BOOLEAN = 4;\n\n// An attribute that must be numeric or parse as a numeric.\n// When falsy, it should be removed.\nconst NUMERIC = 5;\n\n// An attribute that must be positive numeric or parse as a positive numeric.\n// When falsy, it should be removed.\nconst POSITIVE_NUMERIC = 6;\n\nfunction getPropertyInfo(name) {\n  return properties.hasOwnProperty(name) ? properties[name] : null;\n}\n\nfunction PropertyInfoRecord(\n  name,\n  type,\n  mustUseProperty,\n  attributeName,\n  attributeNamespace,\n  sanitizeURL,\n  removeEmptyString,\n) {\n  this.acceptsBooleans =\n    type === BOOLEANISH_STRING ||\n    type === BOOLEAN ||\n    type === OVERLOADED_BOOLEAN;\n  this.attributeName = attributeName;\n  this.attributeNamespace = attributeNamespace;\n  this.mustUseProperty = mustUseProperty;\n  this.propertyName = name;\n  this.type = type;\n  this.sanitizeURL = sanitizeURL;\n  this.removeEmptyString = removeEmptyString;\n}\n\n// When adding attributes to this list, be sure to also add them to\n// the `possibleStandardNames` module to ensure casing and incorrect\n// name warnings.\nconst properties = {};\n\n// These props are reserved by React. They shouldn't be written to the DOM.\nconst reservedProps = [\n  'children',\n  'dangerouslySetInnerHTML',\n  // TODO: This prevents the assignment of defaultValue to regular\n  // elements (not just inputs). Now that ReactDOMInput assigns to the\n  // defaultValue property -- do we need this?\n  'defaultValue',\n  'defaultChecked',\n  'innerHTML',\n  'suppressContentEditableWarning',\n  'suppressHydrationWarning',\n  'style',\n];\n\nreservedProps.forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    RESERVED,\n    false, // mustUseProperty\n    name, // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// A few React string attributes have a different name.\n// This is a mapping from React prop names to the attribute names.\n[\n  ['acceptCharset', 'accept-charset'],\n  ['className', 'class'],\n  ['htmlFor', 'for'],\n  ['httpEquiv', 'http-equiv'],\n].forEach(([name, attributeName]) => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    STRING,\n    false, // mustUseProperty\n    attributeName, // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are \"enumerated\" HTML attributes that accept \"true\" and \"false\".\n// In React, we let users pass `true` and `false` even though technically\n// these aren't boolean attributes (they are coerced to strings).\n['contentEditable', 'draggable', 'spellCheck', 'value'].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    BOOLEANISH_STRING,\n    false, // mustUseProperty\n    name.toLowerCase(), // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are \"enumerated\" SVG attributes that accept \"true\" and \"false\".\n// In React, we let users pass `true` and `false` even though technically\n// these aren't boolean attributes (they are coerced to strings).\n// Since these are SVG attributes, their attribute names are case-sensitive.\n[\n  'autoReverse',\n  'externalResourcesRequired',\n  'focusable',\n  'preserveAlpha',\n].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    BOOLEANISH_STRING,\n    false, // mustUseProperty\n    name, // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are HTML boolean attributes.\n[\n  'allowFullScreen',\n  'async',\n  // Note: there is a special case that prevents it from being written to the DOM\n  // on the client side because the browsers are inconsistent. Instead we call focus().\n  'autoFocus',\n  'autoPlay',\n  'controls',\n  'default',\n  'defer',\n  'disabled',\n  'disablePictureInPicture',\n  'disableRemotePlayback',\n  'formNoValidate',\n  'hidden',\n  'loop',\n  'noModule',\n  'noValidate',\n  'open',\n  'playsInline',\n  'readOnly',\n  'required',\n  'reversed',\n  'scoped',\n  'seamless',\n  // Microdata\n  'itemScope',\n].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    BOOLEAN,\n    false, // mustUseProperty\n    name.toLowerCase(), // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are the few React props that we set as DOM properties\n// rather than attributes. These are all booleans.\n[\n  'checked',\n  // Note: `option.selected` is not updated if `select.multiple` is\n  // disabled with `removeAttribute`. We have special logic for handling this.\n  'multiple',\n  'muted',\n  'selected',\n\n  // NOTE: if you add a camelCased prop to this list,\n  // you'll need to set attributeName to name.toLowerCase()\n  // instead in the assignment below.\n].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    BOOLEAN,\n    true, // mustUseProperty\n    name, // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are HTML attributes that are \"overloaded booleans\": they behave like\n// booleans, but can also accept a string value.\n[\n  'capture',\n  'download',\n\n  // NOTE: if you add a camelCased prop to this list,\n  // you'll need to set attributeName to name.toLowerCase()\n  // instead in the assignment below.\n].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    OVERLOADED_BOOLEAN,\n    false, // mustUseProperty\n    name, // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are HTML attributes that must be positive numbers.\n[\n  'cols',\n  'rows',\n  'size',\n  'span',\n\n  // NOTE: if you add a camelCased prop to this list,\n  // you'll need to set attributeName to name.toLowerCase()\n  // instead in the assignment below.\n].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    POSITIVE_NUMERIC,\n    false, // mustUseProperty\n    name, // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These are HTML attributes that must be numbers.\n['rowSpan', 'start'].forEach(name => {\n  properties[name] = new PropertyInfoRecord(\n    name,\n    NUMERIC,\n    false, // mustUseProperty\n    name.toLowerCase(), // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\nconst CAMELIZE = /[\\-\\:]([a-z])/g;\nconst capitalize = token => token[1].toUpperCase();\n\n// This is a list of all SVG attributes that need special casing, namespacing,\n// or boolean value assignment. Regular attributes that just accept strings\n// and have the same names are omitted, just like in the HTML attribute filter.\n// Some of these attributes can be hard to find. This list was created by\n// scraping the MDN documentation.\n[\n  'accent-height',\n  'alignment-baseline',\n  'arabic-form',\n  'baseline-shift',\n  'cap-height',\n  'clip-path',\n  'clip-rule',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'dominant-baseline',\n  'enable-background',\n  'fill-opacity',\n  'fill-rule',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'glyph-name',\n  'glyph-orientation-horizontal',\n  'glyph-orientation-vertical',\n  'horiz-adv-x',\n  'horiz-origin-x',\n  'image-rendering',\n  'letter-spacing',\n  'lighting-color',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'overline-position',\n  'overline-thickness',\n  'paint-order',\n  'panose-1',\n  'pointer-events',\n  'rendering-intent',\n  'shape-rendering',\n  'stop-color',\n  'stop-opacity',\n  'strikethrough-position',\n  'strikethrough-thickness',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke-width',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'underline-position',\n  'underline-thickness',\n  'unicode-bidi',\n  'unicode-range',\n  'units-per-em',\n  'v-alphabetic',\n  'v-hanging',\n  'v-ideographic',\n  'v-mathematical',\n  'vector-effect',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'word-spacing',\n  'writing-mode',\n  'xmlns:xlink',\n  'x-height',\n\n  // NOTE: if you add a camelCased prop to this list,\n  // you'll need to set attributeName to name.toLowerCase()\n  // instead in the assignment below.\n].forEach(attributeName => {\n  const name = attributeName.replace(CAMELIZE, capitalize);\n  properties[name] = new PropertyInfoRecord(\n    name,\n    STRING,\n    false, // mustUseProperty\n    attributeName,\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// String SVG attributes with the xlink namespace.\n[\n  'xlink:actuate',\n  'xlink:arcrole',\n  'xlink:role',\n  'xlink:show',\n  'xlink:title',\n  'xlink:type',\n\n  // NOTE: if you add a camelCased prop to this list,\n  // you'll need to set attributeName to name.toLowerCase()\n  // instead in the assignment below.\n].forEach(attributeName => {\n  const name = attributeName.replace(CAMELIZE, capitalize);\n  properties[name] = new PropertyInfoRecord(\n    name,\n    STRING,\n    false, // mustUseProperty\n    attributeName,\n    'http://www.w3.org/1999/xlink',\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// String SVG attributes with the xml namespace.\n[\n  'xml:base',\n  'xml:lang',\n  'xml:space',\n\n  // NOTE: if you add a camelCased prop to this list,\n  // you'll need to set attributeName to name.toLowerCase()\n  // instead in the assignment below.\n].forEach(attributeName => {\n  const name = attributeName.replace(CAMELIZE, capitalize);\n  properties[name] = new PropertyInfoRecord(\n    name,\n    STRING,\n    false, // mustUseProperty\n    attributeName,\n    'http://www.w3.org/XML/1998/namespace',\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These attribute exists both in HTML and SVG.\n// The attribute name is case-sensitive in SVG so we can't just use\n// the React name like we do for attributes that exist only in HTML.\n['tabIndex', 'crossOrigin'].forEach(attributeName => {\n  properties[attributeName] = new PropertyInfoRecord(\n    attributeName,\n    STRING,\n    false, // mustUseProperty\n    attributeName.toLowerCase(), // attributeName\n    null, // attributeNamespace\n    false, // sanitizeURL\n    false, // removeEmptyString\n  );\n});\n\n// These attributes accept URLs. These must not allow javascript: URLS.\n// These will also need to accept Trusted Types object in the future.\nconst xlinkHref = 'xlinkHref';\nproperties[xlinkHref] = new PropertyInfoRecord(\n  'xlinkHref',\n  STRING,\n  false, // mustUseProperty\n  'xlink:href',\n  'http://www.w3.org/1999/xlink',\n  true, // sanitizeURL\n  false, // removeEmptyString\n);\n\n['src', 'href', 'action', 'formAction'].forEach(attributeName => {\n  properties[attributeName] = new PropertyInfoRecord(\n    attributeName,\n    STRING,\n    false, // mustUseProperty\n    attributeName.toLowerCase(), // attributeName\n    null, // attributeNamespace\n    true, // sanitizeURL\n    true, // removeEmptyString\n  );\n});\n\n// \nconst {\n  CAMELCASE,\n  SAME,\n  possibleStandardNames: possibleStandardNamesOptimized\n} = require('../lib/possibleStandardNamesOptimized');\n\nconst ATTRIBUTE_NAME_START_CHAR =\n  ':A-Z_a-z\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD';\n\nconst ATTRIBUTE_NAME_CHAR =\n  ATTRIBUTE_NAME_START_CHAR + '\\\\-.0-9\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040';\n\n/**\n * Checks whether a property name is a custom attribute.\n *\n * @see https://github.com/facebook/react/blob/15-stable/src/renderers/dom/shared/HTMLDOMPropertyConfig.js#L23-L25\n *\n * @type {(attribute: string) => boolean}\n */\nconst isCustomAttribute =\n  RegExp.prototype.test.bind(\n    // eslint-disable-next-line no-misleading-character-class\n    new RegExp('^(data|aria)-[' + ATTRIBUTE_NAME_CHAR + ']*$')\n  );\n\n/**\n * @type {Record<string, string>}\n */\nconst possibleStandardNames = Object.keys(\n  possibleStandardNamesOptimized\n).reduce((accumulator, standardName) => {\n  const propName = possibleStandardNamesOptimized[standardName];\n  if (propName === SAME) {\n    accumulator[standardName] = standardName;\n  } else if (propName === CAMELCASE) {\n    accumulator[standardName.toLowerCase()] = standardName;\n  } else {\n    accumulator[standardName] = propName;\n  }\n  return accumulator;\n}, {});\n\nexports.BOOLEAN = BOOLEAN;\nexports.BOOLEANISH_STRING = BOOLEANISH_STRING;\nexports.NUMERIC = NUMERIC;\nexports.OVERLOADED_BOOLEAN = OVERLOADED_BOOLEAN;\nexports.POSITIVE_NUMERIC = POSITIVE_NUMERIC;\nexports.RESERVED = RESERVED;\nexports.STRING = STRING;\nexports.getPropertyInfo = getPropertyInfo;\nexports.isCustomAttribute = isCustomAttribute;\nexports.possibleStandardNames = possibleStandardNames;\n", "import type { Element } from 'html-dom-parser';\nimport { version } from 'react';\nimport StyleToJS from 'style-to-js';\n\nimport type { Props } from './attributes-to-props';\n\nconst RESERVED_SVG_MATHML_ELEMENTS = new Set([\n  'annotation-xml',\n  'color-profile',\n  'font-face',\n  'font-face-src',\n  'font-face-uri',\n  'font-face-format',\n  'font-face-name',\n  'missing-glyph',\n] as const);\n\ntype ReservedSvgMathmlElements =\n  typeof RESERVED_SVG_MATHML_ELEMENTS extends Set<infer T> ? T : never;\n\n/**\n * Check if a tag is a custom component.\n *\n * @see {@link https://github.com/facebook/react/blob/v16.6.3/packages/react-dom/src/shared/isCustomComponent.js}\n *\n * @param tagName - Tag name.\n * @param props - Props passed to the element.\n * @returns - Whether the tag is custom component.\n */\nexport function isCustomComponent(\n  tagName: string,\n  props?: Record<PropertyKey, string>,\n): boolean {\n  if (!tagName.includes('-')) {\n    return Boolean(props && typeof props.is === 'string');\n  }\n\n  // These are reserved SVG and MathML elements.\n  // We don't mind this whitelist too much because we expect it to never grow.\n  // The alternative is to track the namespace in a few places which is convoluted.\n  // https://w3c.github.io/webcomponents/spec/custom/#custom-elements-core-concepts\n  if (RESERVED_SVG_MATHML_ELEMENTS.has(tagName as ReservedSvgMathmlElements)) {\n    return false;\n  }\n\n  return true;\n}\n\nconst styleOptions = {\n  reactCompat: true,\n} as const;\n\n/**\n * Sets style prop.\n *\n * @param style - Inline style.\n * @param props - Props object.\n */\nexport function setStyleProp(style: string, props: Props): void {\n  if (typeof style !== 'string') {\n    return;\n  }\n\n  if (!style.trim()) {\n    props.style = {};\n    return;\n  }\n\n  try {\n    props.style = StyleToJS(style, styleOptions);\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  } catch (error) {\n    props.style = {};\n  }\n}\n\n/**\n * @see https://reactjs.org/blog/2017/09/08/dom-attributes-in-react-16.html\n */\nexport const PRESERVE_CUSTOM_ATTRIBUTES = Number(version.split('.')[0]) >= 16;\n\n/**\n * @see https://github.com/facebook/react/blob/cae635054e17a6f107a39d328649137b83f25972/packages/react-dom/src/client/validateDOMNesting.js#L213\n */\nexport const ELEMENTS_WITH_NO_TEXT_CHILDREN = new Set([\n  'tr',\n  'tbody',\n  'thead',\n  'tfoot',\n  'colgroup',\n  'table',\n  'head',\n  'html',\n  'frameset',\n] as const);\n\ntype ElementsWithNoTextChildren =\n  typeof ELEMENTS_WITH_NO_TEXT_CHILDREN extends Set<infer T> ? T : never;\n\n/**\n * Checks if the given node can contain text nodes\n *\n * @param node - Element node.\n * @returns - Whether the node can contain text nodes.\n */\nexport const canTextBeChildOfNode = (node: Element) =>\n  !ELEMENTS_WITH_NO_TEXT_CHILDREN.has(node.name as ElementsWithNoTextChildren);\n\n/**\n * Returns the first argument as is.\n *\n * @param arg - The argument to be returned.\n * @returns - The input argument `arg`.\n */\nexport const returnFirstArg = (arg: any) => arg;\n", "import {\n  BO<PERSON>EAN,\n  getPropertyInfo,\n  isCustomAttribute,\n  OVERLOADED_BOOLEAN,\n  possibleStandardNames,\n} from 'react-property';\n\nimport { PRESERVE_CUSTOM_ATTRIBUTES, setStyleProp } from './utilities';\n\n// https://react.dev/learn/sharing-state-between-components#controlled-and-uncontrolled-components\n// https://developer.mozilla.org/docs/Web/HTML/Attributes\nconst UNCONTROLLED_COMPONENT_ATTRIBUTES = ['checked', 'value'] as const;\nconst UNCONTROLLED_COMPONENT_NAMES = ['input', 'select', 'textarea'] as const;\n\ntype UncontrolledComponentAttributes =\n  (typeof UNCONTROLLED_COMPONENT_ATTRIBUTES)[number];\n\ntype UncontrolledComponentNames = (typeof UNCONTROLLED_COMPONENT_NAMES)[number];\n\nconst valueOnlyInputs = {\n  reset: true,\n  submit: true,\n} as const;\n\nexport type ValueOnlyInputsKeys = keyof typeof valueOnlyInputs;\n\nexport type Attributes = Record<PropertyKey, string>;\n\nexport type Props = Record<PropertyKey, string | boolean> & {\n  dangerouslySetInnerHTML?: {\n    __html: string;\n  };\n  key?: string | number;\n  style?: Record<PropertyKey, string>;\n};\n\n/**\n * Converts HTML/SVG DOM attributes to React props.\n *\n * @param attributes - HTML/SVG DOM attributes.\n * @param nodeName - DOM node name.\n * @returns - React props.\n */\nexport default function attributesToProps(\n  attributes: Attributes = {},\n  nodeName?: string,\n): Props {\n  const props: Props = {};\n\n  const isInputValueOnly = Boolean(\n    attributes.type && valueOnlyInputs[attributes.type as ValueOnlyInputsKeys],\n  );\n\n  for (const attributeName in attributes) {\n    const attributeValue = attributes[attributeName];\n\n    // ARIA (aria-*) or custom data (data-*) attribute\n    if (isCustomAttribute(attributeName)) {\n      props[attributeName] = attributeValue;\n      continue;\n    }\n\n    // convert HTML/SVG attribute to React prop\n    const attributeNameLowerCased = attributeName.toLowerCase();\n    let propName = getPropName(attributeNameLowerCased);\n\n    if (propName) {\n      const propertyInfo = getPropertyInfo(propName);\n\n      // convert attribute to uncontrolled component prop (e.g., `value` to `defaultValue`)\n      if (\n        UNCONTROLLED_COMPONENT_ATTRIBUTES.includes(\n          propName as UncontrolledComponentAttributes,\n        ) &&\n        UNCONTROLLED_COMPONENT_NAMES.includes(\n          nodeName! as UncontrolledComponentNames,\n        ) &&\n        !isInputValueOnly\n      ) {\n        propName = getPropName('default' + attributeNameLowerCased);\n      }\n\n      props[propName] = attributeValue;\n\n      switch (propertyInfo && propertyInfo.type) {\n        case BOOLEAN:\n          props[propName] = true;\n          break;\n        case OVERLOADED_BOOLEAN:\n          if (attributeValue === '') {\n            props[propName] = true;\n          }\n          break;\n      }\n      continue;\n    }\n\n    // preserve custom attribute if React >=16\n    if (PRESERVE_CUSTOM_ATTRIBUTES) {\n      props[attributeName] = attributeValue;\n    }\n  }\n\n  // transform inline style to object\n  setStyleProp(attributes.style, props);\n\n  return props;\n}\n\n/**\n * Gets prop name from lowercased attribute name.\n *\n * @param attributeName - Lowercased attribute name.\n * @returns - Prop name.\n */\nfunction getPropName(attributeName: string): string {\n  return possibleStandardNames[attributeName];\n}\n", "import type { DOMN<PERSON>, Element, Text } from 'html-dom-parser';\nimport type { JSX } from 'react';\nimport { cloneElement, createElement, isValidElement } from 'react';\n\nimport type { Props } from './attributes-to-props';\nimport attributesToProps from './attributes-to-props';\nimport type { HTMLReactParserOptions } from './types';\nimport {\n  canTextBeChildOfNode,\n  isCustomComponent,\n  PRESERVE_CUSTOM_ATTRIBUTES,\n  returnFirstArg,\n  setStyleProp,\n} from './utilities';\n\nconst React = {\n  cloneElement,\n  createElement,\n  isValidElement,\n} as const;\n\n/**\n * Converts DOM nodes to JSX element(s).\n *\n * @param nodes - DOM nodes.\n * @param options - Options.\n * @returns - String or JSX element(s).\n */\nexport default function domToReact(\n  nodes: DOMNode[],\n  options: HTMLReactParserOptions = {},\n): string | JSX.Element | JSX.Element[] {\n  const reactElements = [];\n\n  const hasReplace = typeof options.replace === 'function';\n  const transform = options.transform || returnFirstArg;\n  const { cloneElement, createElement, isValidElement } =\n    options.library || React;\n\n  const nodesLength = nodes.length;\n\n  for (let index = 0; index < nodesLength; index++) {\n    const node = nodes[index];\n\n    // replace with custom React element (if present)\n    if (hasReplace) {\n      let replaceElement = options.replace!(node, index) as JSX.Element;\n\n      if (isValidElement(replaceElement)) {\n        // set \"key\" prop for sibling elements\n        // https://react.dev/learn/rendering-lists#rules-of-keys\n        if (nodesLength > 1) {\n          replaceElement = cloneElement(replaceElement, {\n            key: replaceElement.key || index,\n          });\n        }\n\n        reactElements.push(transform(replaceElement, node, index));\n        continue;\n      }\n    }\n\n    if (node.type === 'text') {\n      const isWhitespace = !node.data.trim().length;\n\n      // We have a whitespace node that can't be nested in its parent\n      // so skip it\n      if (\n        isWhitespace &&\n        node.parent &&\n        !canTextBeChildOfNode(node.parent as Element)\n      ) {\n        continue;\n      }\n\n      // Trim is enabled and we have a whitespace node\n      // so skip it\n      if (options.trim && isWhitespace) {\n        continue;\n      }\n\n      // We have a text node that's not whitespace and it can be nested\n      // in its parent so add it to the results\n      reactElements.push(transform(node.data, node, index));\n      continue;\n    }\n\n    const element = node as Element;\n    let props: Props = {};\n\n    if (skipAttributesToProps(element)) {\n      setStyleProp(element.attribs.style, element.attribs);\n      props = element.attribs;\n    } else if (element.attribs) {\n      props = attributesToProps(element.attribs, element.name);\n    }\n\n    let children: ReturnType<typeof domToReact> | undefined;\n\n    switch (node.type) {\n      case 'script':\n      case 'style':\n        // prevent text in <script> or <style> from being escaped\n        // https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html\n        if (node.children[0]) {\n          props.dangerouslySetInnerHTML = {\n            __html: (node.children[0] as Text).data,\n          };\n        }\n        break;\n\n      case 'tag':\n        // setting textarea value in children is an antipattern in React\n        // https://react.dev/reference/react-dom/components/textarea#caveats\n        if (node.name === 'textarea' && node.children[0]) {\n          props.defaultValue = (node.children[0] as Text).data;\n        } else if (node.children && node.children.length) {\n          // continue recursion of creating React elements (if applicable)\n          children = domToReact(node.children as Text[], options);\n        }\n        break;\n\n      // skip all other cases (e.g., comment)\n      default:\n        continue;\n    }\n\n    // set \"key\" prop for sibling elements\n    // https://react.dev/learn/rendering-lists#rules-of-keys\n    if (nodesLength > 1) {\n      props.key = index;\n    }\n\n    reactElements.push(\n      transform(createElement(node.name, props, children), node, index),\n    );\n  }\n\n  return reactElements.length === 1 ? reactElements[0] : reactElements;\n}\n\n/**\n * Determines whether DOM element attributes should be transformed to props.\n * Web Components should not have their attributes transformed except for `style`.\n *\n * @param node - Element node.\n * @returns - Whether the node attributes should be converted to props.\n */\nfunction skipAttributesToProps(node: Element): boolean {\n  return (\n    PRESERVE_CUSTOM_ATTRIBUTES &&\n    node.type === 'tag' &&\n    isCustomComponent(node.name, node.attribs)\n  );\n}\n", "import htmlToDOM from 'html-dom-parser';\n\nimport attributesToProps from './attributes-to-props';\nimport domToReact from './dom-to-react';\nimport type { HTMLReactParserOptions } from './types';\n\nexport { Comment, Element, ProcessingInstruction, Text } from 'domhandler';\nexport type { DOMNode } from 'html-dom-parser';\n\nexport type { HTMLReactParserOptions };\nexport { attributesToProps, domToReact, htmlToDOM };\n\nconst domParserOptions = { lowerCaseAttributeNames: false } as const;\n\n/**\n * Converts HTML string to React elements.\n *\n * @param html - HTML string.\n * @param options - Parser options.\n * @returns - React element(s), empty array, or string.\n */\nexport default function HTMLReactParser(\n  html: string,\n  options?: HTMLReactParserOptions,\n): ReturnType<typeof domToReact> {\n  if (typeof html !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!html) {\n    return [];\n  }\n\n  return domToReact(\n    htmlToDOM(html, options?.htmlparser2 || domParserOptions),\n    options,\n  );\n}\n", "import HTMLReactParser from '../lib/index.js';\n\nexport {\n  attributesToProps,\n  Comment,\n  domToReact,\n  Element,\n  htmlToDOM,\n  ProcessingInstruction,\n  Text,\n} from '../lib/index.js';\n\nexport default HTMLReactParser.default || HTMLReactParser;\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU,QAAQ,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,UAAU,QAAQ,YAAY,QAAQ,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,cAAc;AAE3L,QAAI;AACJ,KAAC,SAAUA,cAAa;AAEpB,MAAAA,aAAY,MAAM,IAAI;AAEtB,MAAAA,aAAY,MAAM,IAAI;AAEtB,MAAAA,aAAY,WAAW,IAAI;AAE3B,MAAAA,aAAY,SAAS,IAAI;AAEzB,MAAAA,aAAY,QAAQ,IAAI;AAExB,MAAAA,aAAY,OAAO,IAAI;AAEvB,MAAAA,aAAY,KAAK,IAAI;AAErB,MAAAA,aAAY,OAAO,IAAI;AAEvB,MAAAA,aAAY,SAAS,IAAI;AAAA,IAC7B,GAAG,cAAc,QAAQ,gBAAgB,QAAQ,cAAc,CAAC,EAAE;AAMlE,aAAS,MAAM,MAAM;AACjB,aAAQ,KAAK,SAAS,YAAY,OAC9B,KAAK,SAAS,YAAY,UAC1B,KAAK,SAAS,YAAY;AAAA,IAClC;AACA,YAAQ,QAAQ;AAGhB,YAAQ,OAAO,YAAY;AAE3B,YAAQ,OAAO,YAAY;AAE3B,YAAQ,YAAY,YAAY;AAEhC,YAAQ,UAAU,YAAY;AAE9B,YAAQ,SAAS,YAAY;AAE7B,YAAQ,QAAQ,YAAY;AAE5B,YAAQ,MAAM,YAAY;AAE1B,YAAQ,QAAQ,YAAY;AAE5B,YAAQ,UAAU,YAAY;AAAA;AAAA;;;ACtD9B;AAAA;AAAA;AACA,QAAI,YAAa,WAAQ,QAAK,aAAe,2BAAY;AACrD,UAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,wBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,UAAAD,GAAE,YAAYC;AAAA,QAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,mBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,QAAG;AACpG,eAAO,cAAc,GAAG,CAAC;AAAA,MAC7B;AACA,aAAO,SAAU,GAAG,GAAG;AACnB,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAc;AAAA,QAAG;AACtC,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,MACtF;AAAA,IACJ,EAAG;AACH,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,YAAY,QAAQ,cAAc,QAAQ,aAAa,QAAQ,cAAc,QAAQ,YAAY,QAAQ,SAAS,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,UAAU,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,wBAAwB,QAAQ,UAAU,QAAQ,OAAO,QAAQ,WAAW,QAAQ,OAAO;AAC/U,QAAI,mBAAmB;AAKvB,QAAI;AAAA;AAAA,MAAsB,WAAY;AAClC,iBAASC,QAAO;AAEZ,eAAK,SAAS;AAEd,eAAK,OAAO;AAEZ,eAAK,OAAO;AAEZ,eAAK,aAAa;AAElB,eAAK,WAAW;AAAA,QACpB;AACA,eAAO,eAAeA,MAAK,WAAW,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMhD,KAAK,WAAY;AACb,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,SAAU,QAAQ;AACnB,iBAAK,SAAS;AAAA,UAClB;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAO,eAAeA,MAAK,WAAW,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,UAKrD,KAAK,WAAY;AACb,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,SAAU,MAAM;AACjB,iBAAK,OAAO;AAAA,UAChB;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAO,eAAeA,MAAK,WAAW,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,UAKjD,KAAK,WAAY;AACb,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,SAAU,MAAM;AACjB,iBAAK,OAAO;AAAA,UAChB;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AAOD,QAAAA,MAAK,UAAU,YAAY,SAAU,WAAW;AAC5C,cAAI,cAAc,QAAQ;AAAE,wBAAY;AAAA,UAAO;AAC/C,iBAAO,UAAU,MAAM,SAAS;AAAA,QACpC;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,OAAO;AAIf,QAAI;AAAA;AAAA,MAA0B,SAAU,QAAQ;AAC5C,kBAAUC,WAAU,MAAM;AAI1B,iBAASA,UAAS,MAAM;AACpB,cAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,gBAAM,OAAO;AACb,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,UAAS,WAAW,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,UAKnD,KAAK,WAAY;AACb,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,SAAU,MAAM;AACjB,iBAAK,OAAO;AAAA,UAChB;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,IAAI;AAAA;AACN,YAAQ,WAAW;AAInB,QAAIC;AAAA;AAAA,MAAsB,SAAU,QAAQ;AACxC,kBAAUA,OAAM,MAAM;AACtB,iBAASA,QAAO;AACZ,cAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,gBAAM,OAAO,iBAAiB,YAAY;AAC1C,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,MAAK,WAAW,YAAY;AAAA,UAC9C,KAAK,WAAY;AACb,mBAAO;AAAA,UACX;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,QAAQ;AAAA;AACV,YAAQ,OAAOA;AAIf,QAAIC;AAAA;AAAA,MAAyB,SAAU,QAAQ;AAC3C,kBAAUA,UAAS,MAAM;AACzB,iBAASA,WAAU;AACf,cAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,gBAAM,OAAO,iBAAiB,YAAY;AAC1C,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,SAAQ,WAAW,YAAY;AAAA,UACjD,KAAK,WAAY;AACb,mBAAO;AAAA,UACX;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,QAAQ;AAAA;AACV,YAAQ,UAAUA;AAIlB,QAAIC;AAAA;AAAA,MAAuC,SAAU,QAAQ;AACzD,kBAAUA,wBAAuB,MAAM;AACvC,iBAASA,uBAAsB,MAAM,MAAM;AACvC,cAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,KAAK;AACvC,gBAAM,OAAO;AACb,gBAAM,OAAO,iBAAiB,YAAY;AAC1C,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,uBAAsB,WAAW,YAAY;AAAA,UAC/D,KAAK,WAAY;AACb,mBAAO;AAAA,UACX;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,QAAQ;AAAA;AACV,YAAQ,wBAAwBA;AAIhC,QAAI;AAAA;AAAA,MAAkC,SAAU,QAAQ;AACpD,kBAAUC,mBAAkB,MAAM;AAIlC,iBAASA,kBAAiB,UAAU;AAChC,cAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,gBAAM,WAAW;AACjB,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,kBAAiB,WAAW,cAAc;AAAA;AAAA;AAAA,UAG5D,KAAK,WAAY;AACb,gBAAI;AACJ,oBAAQ,KAAK,KAAK,SAAS,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,UACpE;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAO,eAAeA,kBAAiB,WAAW,aAAa;AAAA;AAAA,UAE3D,KAAK,WAAY;AACb,mBAAO,KAAK,SAAS,SAAS,IACxB,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,IACtC;AAAA,UACV;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAO,eAAeA,kBAAiB,WAAW,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,UAK5D,KAAK,WAAY;AACb,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,SAAU,UAAU;AACrB,iBAAK,WAAW;AAAA,UACpB;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,IAAI;AAAA;AACN,YAAQ,mBAAmB;AAC3B,QAAI;AAAA;AAAA,MAAuB,SAAU,QAAQ;AACzC,kBAAUC,QAAO,MAAM;AACvB,iBAASA,SAAQ;AACb,cAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,gBAAM,OAAO,iBAAiB,YAAY;AAC1C,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,OAAM,WAAW,YAAY;AAAA,UAC/C,KAAK,WAAY;AACb,mBAAO;AAAA,UACX;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,gBAAgB;AAAA;AAClB,YAAQ,QAAQ;AAIhB,QAAI;AAAA;AAAA,MAA0B,SAAU,QAAQ;AAC5C,kBAAUC,WAAU,MAAM;AAC1B,iBAASA,YAAW;AAChB,cAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,gBAAM,OAAO,iBAAiB,YAAY;AAC1C,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,UAAS,WAAW,YAAY;AAAA,UAClD,KAAK,WAAY;AACb,mBAAO;AAAA,UACX;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,gBAAgB;AAAA;AAClB,YAAQ,WAAW;AAInB,QAAIC;AAAA;AAAA,MAAyB,SAAU,QAAQ;AAC3C,kBAAUA,UAAS,MAAM;AAMzB,iBAASA,SAAQ,MAAM,SAAS,UAAU,MAAM;AAC5C,cAAI,aAAa,QAAQ;AAAE,uBAAW,CAAC;AAAA,UAAG;AAC1C,cAAI,SAAS,QAAQ;AAAE,mBAAO,SAAS,WACjC,iBAAiB,YAAY,SAC7B,SAAS,UACL,iBAAiB,YAAY,QAC7B,iBAAiB,YAAY;AAAA,UAAK;AAC5C,cAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ,KAAK;AAC3C,gBAAM,OAAO;AACb,gBAAM,UAAU;AAChB,gBAAM,OAAO;AACb,iBAAO;AAAA,QACX;AACA,eAAO,eAAeA,SAAQ,WAAW,YAAY;AAAA,UACjD,KAAK,WAAY;AACb,mBAAO;AAAA,UACX;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAO,eAAeA,SAAQ,WAAW,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMhD,KAAK,WAAY;AACb,mBAAO,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,SAAU,MAAM;AACjB,iBAAK,OAAO;AAAA,UAChB;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAO,eAAeA,SAAQ,WAAW,cAAc;AAAA,UACnD,KAAK,WAAY;AACb,gBAAI,QAAQ;AACZ,mBAAO,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,SAAU,MAAM;AACjD,kBAAI,IAAI;AACR,qBAAQ;AAAA,gBACJ;AAAA,gBACA,OAAO,MAAM,QAAQ,IAAI;AAAA,gBACzB,YAAY,KAAK,MAAM,oBAAoB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI;AAAA,gBAC1F,SAAS,KAAK,MAAM,iBAAiB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI;AAAA,cACxF;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,eAAOA;AAAA,MACX,EAAE,gBAAgB;AAAA;AAClB,YAAQ,UAAUA;AAKlB,aAAS,MAAM,MAAM;AACjB,cAAQ,GAAG,iBAAiB,OAAO,IAAI;AAAA,IAC3C;AACA,YAAQ,QAAQ;AAKhB,aAAS,QAAQ,MAAM;AACnB,aAAO,KAAK,SAAS,iBAAiB,YAAY;AAAA,IACtD;AACA,YAAQ,UAAU;AAKlB,aAAS,OAAO,MAAM;AAClB,aAAO,KAAK,SAAS,iBAAiB,YAAY;AAAA,IACtD;AACA,YAAQ,SAAS;AAKjB,aAAS,UAAU,MAAM;AACrB,aAAO,KAAK,SAAS,iBAAiB,YAAY;AAAA,IACtD;AACA,YAAQ,YAAY;AAKpB,aAAS,YAAY,MAAM;AACvB,aAAO,KAAK,SAAS,iBAAiB,YAAY;AAAA,IACtD;AACA,YAAQ,cAAc;AAKtB,aAAS,WAAW,MAAM;AACtB,aAAO,KAAK,SAAS,iBAAiB,YAAY;AAAA,IACtD;AACA,YAAQ,aAAa;AAKrB,aAAS,YAAY,MAAM;AACvB,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,UAAU;AAAA,IAChE;AACA,YAAQ,cAAc;AAOtB,aAAS,UAAU,MAAM,WAAW;AAChC,UAAI,cAAc,QAAQ;AAAE,oBAAY;AAAA,MAAO;AAC/C,UAAI;AACJ,UAAI,OAAO,IAAI,GAAG;AACd,iBAAS,IAAIN,MAAK,KAAK,IAAI;AAAA,MAC/B,WACS,UAAU,IAAI,GAAG;AACtB,iBAAS,IAAIC,SAAQ,KAAK,IAAI;AAAA,MAClC,WACS,MAAM,IAAI,GAAG;AAClB,YAAI,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,CAAC;AAC3D,YAAI,UAAU,IAAIK,SAAQ,KAAK,MAAM,SAAS,CAAC,GAAG,KAAK,OAAO,GAAG,QAAQ;AACzE,iBAAS,QAAQ,SAAU,OAAO;AAAE,iBAAQ,MAAM,SAAS;AAAA,QAAU,CAAC;AACtE,YAAI,KAAK,aAAa,MAAM;AACxB,kBAAQ,YAAY,KAAK;AAAA,QAC7B;AACA,YAAI,KAAK,oBAAoB,GAAG;AAC5B,kBAAQ,oBAAoB,IAAI,SAAS,CAAC,GAAG,KAAK,oBAAoB,CAAC;AAAA,QAC3E;AACA,YAAI,KAAK,iBAAiB,GAAG;AACzB,kBAAQ,iBAAiB,IAAI,SAAS,CAAC,GAAG,KAAK,iBAAiB,CAAC;AAAA,QACrE;AACA,iBAAS;AAAA,MACb,WACS,QAAQ,IAAI,GAAG;AACpB,YAAI,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,CAAC;AAC3D,YAAI,UAAU,IAAI,MAAM,QAAQ;AAChC,iBAAS,QAAQ,SAAU,OAAO;AAAE,iBAAQ,MAAM,SAAS;AAAA,QAAU,CAAC;AACtE,iBAAS;AAAA,MACb,WACS,WAAW,IAAI,GAAG;AACvB,YAAI,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,CAAC;AAC3D,YAAI,UAAU,IAAI,SAAS,QAAQ;AACnC,iBAAS,QAAQ,SAAU,OAAO;AAAE,iBAAQ,MAAM,SAAS;AAAA,QAAU,CAAC;AACtE,YAAI,KAAK,QAAQ,GAAG;AAChB,kBAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,QACrC;AACA,iBAAS;AAAA,MACb,WACS,YAAY,IAAI,GAAG;AACxB,YAAI,cAAc,IAAIJ,uBAAsB,KAAK,MAAM,KAAK,IAAI;AAChE,YAAI,KAAK,QAAQ,KAAK,MAAM;AACxB,sBAAY,QAAQ,IAAI,KAAK,QAAQ;AACrC,sBAAY,YAAY,IAAI,KAAK,YAAY;AAC7C,sBAAY,YAAY,IAAI,KAAK,YAAY;AAAA,QACjD;AACA,iBAAS;AAAA,MACb,OACK;AACD,cAAM,IAAI,MAAM,wBAAwB,OAAO,KAAK,IAAI,CAAC;AAAA,MAC7D;AACA,aAAO,aAAa,KAAK;AACzB,aAAO,WAAW,KAAK;AACvB,UAAI,KAAK,sBAAsB,MAAM;AACjC,eAAO,qBAAqB,KAAK;AAAA,MACrC;AACA,aAAO;AAAA,IACX;AACA,YAAQ,YAAY;AACpB,aAAS,cAAc,QAAQ;AAC3B,UAAI,WAAW,OAAO,IAAI,SAAU,OAAO;AAAE,eAAO,UAAU,OAAO,IAAI;AAAA,MAAG,CAAC;AAC7E,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,iBAAS,CAAC,EAAE,OAAO,SAAS,IAAI,CAAC;AACjC,iBAAS,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC;AAAA,MACrC;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACzdA,IAAAK,eAAA;AAAA;AAAA;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAS,GAAGC,UAAS;AACnE,eAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAKA,UAAS,CAAC,EAAG,iBAAgBA,UAAS,GAAG,CAAC;AAAA,IAC5H;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa;AACrB,QAAI,mBAAmB;AACvB,QAAI,YAAY;AAChB,iBAAa,gBAAsB,OAAO;AAE1C,QAAI,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,SAAS;AAAA,IACb;AACA,QAAI;AAAA;AAAA,MAA4B,WAAY;AAMxC,iBAASC,YAAW,UAAU,SAAS,WAAW;AAE9C,eAAK,MAAM,CAAC;AAEZ,eAAK,OAAO,IAAI,UAAU,SAAS,KAAK,GAAG;AAE3C,eAAK,OAAO;AAEZ,eAAK,WAAW,CAAC,KAAK,IAAI;AAE1B,eAAK,WAAW;AAEhB,eAAK,SAAS;AAEd,cAAI,OAAO,YAAY,YAAY;AAC/B,wBAAY;AACZ,sBAAU;AAAA,UACd;AACA,cAAI,OAAO,aAAa,UAAU;AAC9B,sBAAU;AACV,uBAAW;AAAA,UACf;AACA,eAAK,WAAW,aAAa,QAAQ,aAAa,SAAS,WAAW;AACtE,eAAK,UAAU,YAAY,QAAQ,YAAY,SAAS,UAAU;AAClE,eAAK,YAAY,cAAc,QAAQ,cAAc,SAAS,YAAY;AAAA,QAC9E;AACA,QAAAA,YAAW,UAAU,eAAe,SAAU,QAAQ;AAClD,eAAK,SAAS;AAAA,QAClB;AAEA,QAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,eAAK,MAAM,CAAC;AACZ,eAAK,OAAO,IAAI,UAAU,SAAS,KAAK,GAAG;AAC3C,eAAK,OAAO;AACZ,eAAK,WAAW,CAAC,KAAK,IAAI;AAC1B,eAAK,WAAW;AAChB,eAAK,SAAS;AAAA,QAClB;AAEA,QAAAA,YAAW,UAAU,QAAQ,WAAY;AACrC,cAAI,KAAK;AACL;AACJ,eAAK,OAAO;AACZ,eAAK,SAAS;AACd,eAAK,eAAe,IAAI;AAAA,QAC5B;AACA,QAAAA,YAAW,UAAU,UAAU,SAAU,OAAO;AAC5C,eAAK,eAAe,KAAK;AAAA,QAC7B;AACA,QAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,eAAK,WAAW;AAChB,cAAI,OAAO,KAAK,SAAS,IAAI;AAC7B,cAAI,KAAK,QAAQ,gBAAgB;AAC7B,iBAAK,WAAW,KAAK,OAAO;AAAA,UAChC;AACA,cAAI,KAAK;AACL,iBAAK,UAAU,IAAI;AAAA,QAC3B;AACA,QAAAA,YAAW,UAAU,YAAY,SAAU,MAAM,SAAS;AACtD,cAAI,OAAO,KAAK,QAAQ,UAAU,iBAAiB,YAAY,MAAM;AACrE,cAAI,UAAU,IAAI,UAAU,QAAQ,MAAM,SAAS,QAAW,IAAI;AAClE,eAAK,QAAQ,OAAO;AACpB,eAAK,SAAS,KAAK,OAAO;AAAA,QAC9B;AACA,QAAAA,YAAW,UAAU,SAAS,SAAU,MAAM;AAC1C,cAAI,WAAW,KAAK;AACpB,cAAI,YAAY,SAAS,SAAS,iBAAiB,YAAY,MAAM;AACjE,qBAAS,QAAQ;AACjB,gBAAI,KAAK,QAAQ,gBAAgB;AAC7B,uBAAS,WAAW,KAAK,OAAO;AAAA,YACpC;AAAA,UACJ,OACK;AACD,gBAAI,OAAO,IAAI,UAAU,KAAK,IAAI;AAClC,iBAAK,QAAQ,IAAI;AACjB,iBAAK,WAAW;AAAA,UACpB;AAAA,QACJ;AACA,QAAAA,YAAW,UAAU,YAAY,SAAU,MAAM;AAC7C,cAAI,KAAK,YAAY,KAAK,SAAS,SAAS,iBAAiB,YAAY,SAAS;AAC9E,iBAAK,SAAS,QAAQ;AACtB;AAAA,UACJ;AACA,cAAI,OAAO,IAAI,UAAU,QAAQ,IAAI;AACrC,eAAK,QAAQ,IAAI;AACjB,eAAK,WAAW;AAAA,QACpB;AACA,QAAAA,YAAW,UAAU,eAAe,WAAY;AAC5C,eAAK,WAAW;AAAA,QACpB;AACA,QAAAA,YAAW,UAAU,eAAe,WAAY;AAC5C,cAAI,OAAO,IAAI,UAAU,KAAK,EAAE;AAChC,cAAI,OAAO,IAAI,UAAU,MAAM,CAAC,IAAI,CAAC;AACrC,eAAK,QAAQ,IAAI;AACjB,eAAK,SAAS;AACd,eAAK,WAAW;AAAA,QACpB;AACA,QAAAA,YAAW,UAAU,aAAa,WAAY;AAC1C,eAAK,WAAW;AAAA,QACpB;AACA,QAAAA,YAAW,UAAU,0BAA0B,SAAU,MAAM,MAAM;AACjE,cAAI,OAAO,IAAI,UAAU,sBAAsB,MAAM,IAAI;AACzD,eAAK,QAAQ,IAAI;AAAA,QACrB;AACA,QAAAA,YAAW,UAAU,iBAAiB,SAAU,OAAO;AACnD,cAAI,OAAO,KAAK,aAAa,YAAY;AACrC,iBAAK,SAAS,OAAO,KAAK,GAAG;AAAA,UACjC,WACS,OAAO;AACZ,kBAAM;AAAA,UACV;AAAA,QACJ;AACA,QAAAA,YAAW,UAAU,UAAU,SAAU,MAAM;AAC3C,cAAI,SAAS,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AACnD,cAAI,kBAAkB,OAAO,SAAS,OAAO,SAAS,SAAS,CAAC;AAChE,cAAI,KAAK,QAAQ,kBAAkB;AAC/B,iBAAK,aAAa,KAAK,OAAO;AAAA,UAClC;AACA,cAAI,KAAK,QAAQ,gBAAgB;AAC7B,iBAAK,WAAW,KAAK,OAAO;AAAA,UAChC;AACA,iBAAO,SAAS,KAAK,IAAI;AACzB,cAAI,iBAAiB;AACjB,iBAAK,OAAO;AACZ,4BAAgB,OAAO;AAAA,UAC3B;AACA,eAAK,SAAS;AACd,eAAK,WAAW;AAAA,QACpB;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAAA;AAAA;;;;;;;;AC/JL,YAAA,2BAA2B;MACtC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;AAGW,YAAA,+BAA+B,QAAA,yBAAyB,OACnE,SAAC,aAAa,SAAO;AACnB,kBAAY,QAAQ,YAAW,CAAE,IAAI;AACrC,aAAO;IACT,GACA,CAAA,CAA4B;AAGjB,YAAA,kBAAkB;AAClB,YAAA,wBAAwB,IAAI,OAAO,QAAA,iBAAiB,GAAG;AACvD,YAAA,8BAA8B,iDAAA,OAAiD,KAAK,IAAG,GAAE,IAAA;AACzF,YAAA,oCAAoC,IAAI,OACnD,QAAA,6BACA,GAAG;;;;;;;;;ACzBL,YAAA,mBAAA;AAsCA,YAAA,0BAAA;AAUA,YAAA,0BAAA;AAYA,YAAA,YAAA;AAvFA,QAAA,eAAA;AAGA,QAAA,cAAA;AAcA,aAAS,wBAAwB,SAAe;AAC9C,aAAO,YAAA,6BAA6B,OAAO;IAC7C;AAQA,aAAgB,iBAAiB,YAAwB;AACvD,UAAM,MAA8B,CAAA;AACpC,UAAI,QAAQ;AACZ,UAAM,mBAAmB,WAAW;AAGpC,aAAO,QAAQ,kBAAkB,SAAS;AACxC,YAAM,YAAY,WAAW,KAAK;AAClC,YAAI,UAAU,IAAI,IAAI,UAAU;MAClC;AAEA,aAAO;IACT;AASA,aAAS,cAAc,SAAe;AACpC,gBAAU,QAAQ,YAAW;AAC7B,UAAM,uBAAuB,wBAAwB,OAAO;AAE5D,UAAI,sBAAsB;AACxB,eAAO;MACT;AAEA,aAAO;IACT;AAQA,aAAgB,wBAAwB,MAAY;AAClD,aAAO,KAAK,QAAQ,YAAA,uBAAuB,YAAA,2BAA2B;IACxE;AAQA,aAAgB,wBAAwB,MAAY;AAClD,aAAO,KAAK,QAAQ,YAAA,mCAAmC,YAAA,eAAe;IACxE;AAUA,aAAgB,UACd,OACA,QACA,WAAkB;AADlB,UAAA,WAAA,QAAA;AAAA,iBAAA;MAA6B;AAG7B,UAAM,WAAW,CAAA;AACjB,UAAI;AACJ,UAAI,QAAQ;AACZ,UAAM,cAAc,MAAM;AAE1B,aAAO,QAAQ,aAAa,SAAS;AACnC,YAAM,OAAO,MAAM,KAAK;AAGxB,gBAAQ,KAAK,UAAU;UACrB,KAAK,GAAG;AACN,gBAAM,UAAU,cAAc,KAAK,QAAQ;AAG3C,sBAAU,IAAI,aAAA,QACZ,SACA,iBAAkB,KAAqB,UAAU,CAAC;AAGpD,oBAAQ,WAAW;;cAEjB,YAAY,aACP,KAA6B,QAAQ,aACtC,KAAK;cACT;YAAO;AAGT;UACF;UAEA,KAAK;AACH,sBAAU,IAAI,aAAA,KAAK,wBAAwB,KAAK,SAAU,CAAC;AAC3D;UAEF,KAAK;AACH,sBAAU,IAAI,aAAA,QAAQ,KAAK,SAAU;AACrC;UAEF;AACE;QACJ;AAGA,YAAM,OAAO,SAAS,QAAQ,CAAC,KAAK;AACpC,YAAI,MAAM;AACR,eAAK,OAAO;QACd;AAGA,gBAAQ,SAAS;AACjB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AAEf,iBAAS,KAAK,OAAO;MACvB;AAEA,UAAI,WAAW;AACb,kBAAU,IAAI,aAAA,sBACZ,UAAU,UAAU,GAAG,UAAU,QAAQ,GAAG,CAAC,EAAE,YAAW,GAC1D,SAAS;AAGX,gBAAQ,OAAO,SAAS,CAAC,KAAK;AAC9B,gBAAQ,SAAS;AACjB,iBAAS,QAAQ,OAAO;AAExB,YAAI,SAAS,CAAC,GAAG;AACf,mBAAS,CAAC,EAAE,OAAO,SAAS,CAAC;QAC/B;MACF;AAEA,aAAO;IACT;;;;;;;;;AC7CA,YAAA,UAAA;AAvHA,QAAA,cAAA;AAGA,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,kBAAkB;AAGxB,QAAM,iBAAiB;AACvB,QAAM,iBAAiB;AAIvB,QAAI,oBAAoB,SAAC,MAAc,SAAgB;AAErD,YAAM,IAAI,MACR,4EAA4E;IAEhF;AAGA,QAAI,kBAAkB,SAAC,MAAc,SAAgB;AAEnD,YAAM,IAAI,MACR,qEAAqE;IAEzE;AAEA,QAAM,YAAY,OAAO,WAAW,YAAY,OAAO;AAOvD,QAAI,OAAO,cAAc,YAAY;AAC7B,oBAAY,IAAI,UAAS;AACzB,mBAAW;AASjB,wBAAkB,SAAC,MAAc,SAAgB;AAC/C,YAAI,SAAS;AAEX,iBAAO,IAAA,OAAI,SAAO,GAAA,EAAA,OAAI,MAAI,IAAA,EAAA,OAAK,SAAO,GAAA;QACxC;AAEA,eAAO,YAAU,gBAAgB,MAAM,UAAQ;MACjD;AAEA,0BAAoB;IACtB;AApBQ;AACA;AA0BR,QAAI,OAAO,aAAa,YAAY,SAAS,gBAAgB;AACrD,uBAAe,SAAS,eAAe,mBAAkB;AAS/D,0BAAoB,SAAU,MAAc,SAAgB;AAC1D,YAAI,SAAS;AACX,cAAM,UAAU,eAAa,gBAAgB,cAAc,OAAO;AAElE,cAAI,SAAS;AACX,oBAAQ,YAAY;UACtB;AAEA,iBAAO;QACT;AAEA,uBAAa,gBAAgB,YAAY;AACzC,eAAO;MACT;IACF;AAvBQ;AA8BR,QAAM,WACJ,OAAO,aAAa,YAAY,SAAS,cAAc,UAAU;AAEnE,QAAI;AAEJ,QAAI,YAAY,SAAS,SAAS;AAOhC,0BAAoB,SAAC,MAAY;AAC/B,iBAAS,YAAY;AACrB,eAAO,SAAS,QAAQ;MAC1B;IACF;AAQA,aAAwB,UAAU,MAAY;;AAE5C,cAAO,GAAA,YAAA,yBAAwB,IAAI;AAEnC,UAAM,QAAQ,KAAK,MAAM,eAAe;AACxC,UAAM,eAAe,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,YAAW,IAAK;AAElE,cAAQ,cAAc;QACpB,KAAK,MAAM;AACT,cAAM,MAAM,gBAAgB,IAAI;AAIhC,cAAI,CAAC,eAAe,KAAK,IAAI,GAAG;AAC9B,gBAAM,UAAU,IAAI,cAAc,IAAI;AACtC,aAAA,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,OAAO;UAC1C;AAEA,cAAI,CAAC,eAAe,KAAK,IAAI,GAAG;AAC9B,gBAAM,UAAU,IAAI,cAAc,IAAI;AACtC,aAAA,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,OAAO;UAC1C;AAEA,iBAAO,IAAI,iBAAiB,IAAI;QAClC;QAEA,KAAK;QACL,KAAK,MAAM;AACT,cAAM,WAAW,kBAAkB,IAAI,EAAE,iBAAiB,YAAY;AAGtE,cAAI,eAAe,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,GAAG;AAC1D,mBAAO,SAAS,CAAC,EAAE,WAAY;UACjC;AAEA,iBAAO;QACT;QAGA,SAAS;AACP,cAAI,mBAAmB;AACrB,mBAAO,kBAAkB,IAAI;UAC/B;AACA,cAAM,UAAU,kBAAkB,MAAM,IAAI,EAAE,cAAc,IAAI;AAChE,iBAAO,QAAS;QAClB;MACF;IACF;;;;;;;;;;;;AC3JA,YAAA,UAAA;AAXA,QAAA,cAAA,gBAAA,mBAAA;AACA,QAAA,cAAA;AAEA,QAAM,kBAAkB;AAQxB,aAAwB,cAAc,MAAY;AAChD,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,iCAAiC;MACvD;AAEA,UAAI,CAAC,MAAM;AACT,eAAO,CAAA;MACT;AAGA,UAAM,QAAQ,KAAK,MAAM,eAAe;AACxC,UAAM,YAAY,QAAQ,MAAM,CAAC,IAAI;AAErC,cAAO,GAAA,YAAA,YAAU,GAAA,YAAA,SAAU,IAAI,GAAG,MAAM,SAAS;IACnD;;;;;ACzBA;AAAA;AACA,QAAI,OAAO;AACX,YAAQ,OAAO;AAGf,QAAI,YAAY;AAChB,YAAQ,YAAY;AAEpB,YAAQ,wBAAwB;AAAA,MAC9B,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,aAAa;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,yBAAyB;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,MACV,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,MACT,cAAc;AAAA,MACd,KAAK;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,aAAa;AAAA,MACb,cAAc;AAAA,MACd,KAAK;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,KAAK;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,eAAe;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,MACT,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,MACb,eAAe;AAAA,MACf,UAAU;AAAA,MACV,aAAa;AAAA,MACb,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,uBAAuB;AAAA,MACvB,2BAA2B;AAAA,MAC3B,+BAA+B;AAAA,MAC/B,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,KAAK;AAAA,MACL,UAAU;AAAA,MACV,2BAA2B;AAAA,MAC3B,MAAM;AAAA,MACN,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,UAAU;AAAA,MACV,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,4BAA4B;AAAA,MAC5B,gCAAgC;AAAA,MAChC,0BAA0B;AAAA,MAC1B,8BAA8B;AAAA,MAC9B,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,SAAS;AAAA,MACT,WAAW;AAAA,MACX,eAAe;AAAA,MACf,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,MACd,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,MAAM;AAAA,MACN,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,wBAAwB;AAAA,MACxB,2BAA2B;AAAA,MAC3B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gCAAgC;AAAA,MAChC,0BAA0B;AAAA,MAC1B,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,IAAI;AAAA,MACJ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,MACpB,uBAAuB;AAAA,MACvB,SAAS;AAAA,MACT,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,kBAAkB;AAAA,MAClB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,kBAAkB;AAAA,MAClB,GAAG;AAAA,MACH,YAAY;AAAA,IACd;AAAA;AAAA;;;AC5eA,IAAAC,eAAA;AAAA;AAAA;AAgBA,QAAM,WAAW;AAIjB,QAAM,SAAS;AAMf,QAAM,oBAAoB;AAK1B,QAAM,UAAU;AAMhB,QAAM,qBAAqB;AAI3B,QAAM,UAAU;AAIhB,QAAM,mBAAmB;AAEzB,aAAS,gBAAgB,MAAM;AAC7B,aAAO,WAAW,eAAe,IAAI,IAAI,WAAW,IAAI,IAAI;AAAA,IAC9D;AAEA,aAAS,mBACP,MACA,MACA,iBACA,eACA,oBACA,aACA,mBACA;AACA,WAAK,kBACH,SAAS,qBACT,SAAS,WACT,SAAS;AACX,WAAK,gBAAgB;AACrB,WAAK,qBAAqB;AAC1B,WAAK,kBAAkB;AACvB,WAAK,eAAe;AACpB,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,oBAAoB;AAAA,IAC3B;AAKA,QAAM,aAAa,CAAC;AAGpB,QAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,kBAAc,QAAQ,UAAQ;AAC5B,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAID;AAAA,MACE,CAAC,iBAAiB,gBAAgB;AAAA,MAClC,CAAC,aAAa,OAAO;AAAA,MACrB,CAAC,WAAW,KAAK;AAAA,MACjB,CAAC,aAAa,YAAY;AAAA,IAC5B,EAAE,QAAQ,CAAC,CAAC,MAAM,aAAa,MAAM;AACnC,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAKD,KAAC,mBAAmB,aAAa,cAAc,OAAO,EAAE,QAAQ,UAAQ;AACtE,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA,KAAK,YAAY;AAAA;AAAA,QACjB;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAMD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,QAAQ,UAAQ;AAChB,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAGD;AAAA,MACE;AAAA,MACA;AAAA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,IACF,EAAE,QAAQ,UAAQ;AAChB,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA,KAAK,YAAY;AAAA;AAAA,QACjB;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAID;AAAA,MACE;AAAA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAKF,EAAE,QAAQ,UAAQ;AAChB,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAID;AAAA,MACE;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAKF,EAAE,QAAQ,UAAQ;AAChB,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAGD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAKF,EAAE,QAAQ,UAAQ;AAChB,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAGD,KAAC,WAAW,OAAO,EAAE,QAAQ,UAAQ;AACnC,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA,KAAK,YAAY;AAAA;AAAA,QACjB;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAM,WAAW;AACjB,QAAM,aAAa,WAAS,MAAM,CAAC,EAAE,YAAY;AAOjD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAKF,EAAE,QAAQ,mBAAiB;AACzB,YAAM,OAAO,cAAc,QAAQ,UAAU,UAAU;AACvD,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAGD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAKF,EAAE,QAAQ,mBAAiB;AACzB,YAAM,OAAO,cAAc,QAAQ,UAAU,UAAU;AACvD,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAGD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,IAKF,EAAE,QAAQ,mBAAiB;AACzB,YAAM,OAAO,cAAc,QAAQ,UAAU,UAAU;AACvD,iBAAW,IAAI,IAAI,IAAI;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAKD,KAAC,YAAY,aAAa,EAAE,QAAQ,mBAAiB;AACnD,iBAAW,aAAa,IAAI,IAAI;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA,cAAc,YAAY;AAAA;AAAA,QAC1B;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAID,QAAM,YAAY;AAClB,eAAW,SAAS,IAAI,IAAI;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,KAAC,OAAO,QAAQ,UAAU,YAAY,EAAE,QAAQ,mBAAiB;AAC/D,iBAAW,aAAa,IAAI,IAAI;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA,cAAc,YAAY;AAAA;AAAA,QAC1B;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAAA,IACF,CAAC;AAGD,QAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,uBAAuB;AAAA,IACzB,IAAI;AAEJ,QAAM,4BACJ;AAEF,QAAM,sBACJ,4BAA4B;AAS9B,QAAM,oBACJ,OAAO,UAAU,KAAK;AAAA;AAAA,MAEpB,IAAI,OAAO,mBAAmB,sBAAsB,KAAK;AAAA,IAC3D;AAKF,QAAM,wBAAwB,OAAO;AAAA,MACnC;AAAA,IACF,EAAE,OAAO,CAAC,aAAa,iBAAiB;AACtC,YAAM,WAAW,+BAA+B,YAAY;AAC5D,UAAI,aAAa,MAAM;AACrB,oBAAY,YAAY,IAAI;AAAA,MAC9B,WAAW,aAAa,WAAW;AACjC,oBAAY,aAAa,YAAY,CAAC,IAAI;AAAA,MAC5C,OAAO;AACL,oBAAY,YAAY,IAAI;AAAA,MAC9B;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAEL,YAAQ,UAAU;AAClB,YAAQ,oBAAoB;AAC5B,YAAQ,UAAU;AAClB,YAAQ,qBAAqB;AAC7B,YAAQ,mBAAmB;AAC3B,YAAQ,WAAW;AACnB,YAAQ,SAAS;AACjB,YAAQ,kBAAkB;AAC1B,YAAQ,oBAAoB;AAC5B,YAAQ,wBAAwB;AAAA;AAAA;;;;;;;;;;;AC1ehC,YAAA,oBAAA;AA6BA,YAAA,eAAA;AAzDA,QAAA,UAAA;AACA,QAAA,gBAAA,gBAAA,aAAA;AAIA,QAAM,+BAA+B,oBAAI,IAAI;MAC3C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACQ;AAcV,aAAgB,kBACd,SACA,OAAmC;AAEnC,UAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AAC1B,eAAO,QAAQ,SAAS,OAAO,MAAM,OAAO,QAAQ;MACtD;AAMA,UAAI,6BAA6B,IAAI,OAAoC,GAAG;AAC1E,eAAO;MACT;AAEA,aAAO;IACT;AAEA,QAAM,eAAe;MACnB,aAAa;;AASf,aAAgB,aAAa,OAAe,OAAY;AACtD,UAAI,OAAO,UAAU,UAAU;AAC7B;MACF;AAEA,UAAI,CAAC,MAAM,KAAI,GAAI;AACjB,cAAM,QAAQ,CAAA;AACd;MACF;AAEA,UAAI;AACF,cAAM,SAAQ,GAAA,cAAA,SAAU,OAAO,YAAY;MAE7C,SAAS,OAAO;AACd,cAAM,QAAQ,CAAA;MAChB;IACF;AAKa,YAAA,6BAA6B,OAAO,QAAA,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK;AAK9D,YAAA,iCAAiC,oBAAI,IAAI;MACpD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACQ;AAWH,QAAM,uBAAuB,SAAC,MAAa;AAChD,aAAA,CAAC,QAAA,+BAA+B,IAAI,KAAK,IAAkC;IAA3E;AADW,YAAA,uBAAoB;AAS1B,QAAM,iBAAiB,SAAC,KAAQ;AAAK,aAAA;IAAA;AAA/B,YAAA,iBAAc;;;;;;;;;ACtE3B,YAAA,UAAAC;AA5CA,QAAA,mBAAA;AAQA,QAAA,cAAA;AAIA,QAAM,oCAAoC,CAAC,WAAW,OAAO;AAC7D,QAAM,+BAA+B,CAAC,SAAS,UAAU,UAAU;AAOnE,QAAM,kBAAkB;MACtB,OAAO;MACP,QAAQ;;AAsBV,aAAwBA,mBACtB,YACA,UAAiB;AADjB,UAAA,eAAA,QAAA;AAAA,qBAAA,CAAA;MAA2B;AAG3B,UAAM,QAAe,CAAA;AAErB,UAAM,mBAAmB,QACvB,WAAW,QAAQ,gBAAgB,WAAW,IAA2B,CAAC;AAG5E,eAAW,iBAAiB,YAAY;AACtC,YAAM,iBAAiB,WAAW,aAAa;AAG/C,aAAI,GAAA,iBAAA,mBAAkB,aAAa,GAAG;AACpC,gBAAM,aAAa,IAAI;AACvB;QACF;AAGA,YAAM,0BAA0B,cAAc,YAAW;AACzD,YAAI,WAAW,YAAY,uBAAuB;AAElD,YAAI,UAAU;AACZ,cAAM,gBAAe,GAAA,iBAAA,iBAAgB,QAAQ;AAG7C,cACE,kCAAkC,SAChC,QAA2C,KAE7C,6BAA6B,SAC3B,QAAuC,KAEzC,CAAC,kBACD;AACA,uBAAW,YAAY,YAAY,uBAAuB;UAC5D;AAEA,gBAAM,QAAQ,IAAI;AAElB,kBAAQ,gBAAgB,aAAa,MAAM;YACzC,KAAK,iBAAA;AACH,oBAAM,QAAQ,IAAI;AAClB;YACF,KAAK,iBAAA;AACH,kBAAI,mBAAmB,IAAI;AACzB,sBAAM,QAAQ,IAAI;cACpB;AACA;UACJ;AACA;QACF;AAGA,YAAI,YAAA,4BAA4B;AAC9B,gBAAM,aAAa,IAAI;QACzB;MACF;AAGA,OAAA,GAAA,YAAA,cAAa,WAAW,OAAO,KAAK;AAEpC,aAAO;IACT;AAQA,aAAS,YAAY,eAAqB;AACxC,aAAO,iBAAA,sBAAsB,aAAa;IAC5C;;;;;;;;;;;;AC1FA,YAAA,UAAAC;AA1BA,QAAA,UAAA;AAGA,QAAA,wBAAA,gBAAA,6BAAA;AAEA,QAAA,cAAA;AAQA,QAAM,QAAQ;MACZ,cAAY,QAAA;MACZ,eAAa,QAAA;MACb,gBAAc,QAAA;;AAUhB,aAAwBA,YACtB,OACA,SAAoC;AAApC,UAAA,YAAA,QAAA;AAAA,kBAAA,CAAA;MAAoC;AAEpC,UAAM,gBAAgB,CAAA;AAEtB,UAAM,aAAa,OAAO,QAAQ,YAAY;AAC9C,UAAM,YAAY,QAAQ,aAAa,YAAA;AACjC,UAAA,KACJ,QAAQ,WAAW,OADb,eAAY,GAAA,cAAE,gBAAa,GAAA,eAAE,iBAAc,GAAA;AAGnD,UAAM,cAAc,MAAM;AAE1B,eAAS,QAAQ,GAAG,QAAQ,aAAa,SAAS;AAChD,YAAM,OAAO,MAAM,KAAK;AAGxB,YAAI,YAAY;AACd,cAAI,iBAAiB,QAAQ,QAAS,MAAM,KAAK;AAEjD,cAAI,eAAe,cAAc,GAAG;AAGlC,gBAAI,cAAc,GAAG;AACnB,+BAAiB,aAAa,gBAAgB;gBAC5C,KAAK,eAAe,OAAO;eAC5B;YACH;AAEA,0BAAc,KAAK,UAAU,gBAAgB,MAAM,KAAK,CAAC;AACzD;UACF;QACF;AAEA,YAAI,KAAK,SAAS,QAAQ;AACxB,cAAM,eAAe,CAAC,KAAK,KAAK,KAAI,EAAG;AAIvC,cACE,gBACA,KAAK,UACL,EAAC,GAAA,YAAA,sBAAqB,KAAK,MAAiB,GAC5C;AACA;UACF;AAIA,cAAI,QAAQ,QAAQ,cAAc;AAChC;UACF;AAIA,wBAAc,KAAK,UAAU,KAAK,MAAM,MAAM,KAAK,CAAC;AACpD;QACF;AAEA,YAAM,UAAU;AAChB,YAAI,QAAe,CAAA;AAEnB,YAAI,sBAAsB,OAAO,GAAG;AAClC,WAAA,GAAA,YAAA,cAAa,QAAQ,QAAQ,OAAO,QAAQ,OAAO;AACnD,kBAAQ,QAAQ;QAClB,WAAW,QAAQ,SAAS;AAC1B,mBAAQ,GAAA,sBAAA,SAAkB,QAAQ,SAAS,QAAQ,IAAI;QACzD;AAEA,YAAI,WAAQ;AAEZ,gBAAQ,KAAK,MAAM;UACjB,KAAK;UACL,KAAK;AAGH,gBAAI,KAAK,SAAS,CAAC,GAAG;AACpB,oBAAM,0BAA0B;gBAC9B,QAAS,KAAK,SAAS,CAAC,EAAW;;YAEvC;AACA;UAEF,KAAK;AAGH,gBAAI,KAAK,SAAS,cAAc,KAAK,SAAS,CAAC,GAAG;AAChD,oBAAM,eAAgB,KAAK,SAAS,CAAC,EAAW;YAClD,WAAW,KAAK,YAAY,KAAK,SAAS,QAAQ;AAEhD,yBAAWA,YAAW,KAAK,UAAoB,OAAO;YACxD;AACA;UAGF;AACE;QACJ;AAIA,YAAI,cAAc,GAAG;AACnB,gBAAM,MAAM;QACd;AAEA,sBAAc,KACZ,UAAU,cAAc,KAAK,MAAM,OAAO,QAAQ,GAAG,MAAM,KAAK,CAAC;MAErE;AAEA,aAAO,cAAc,WAAW,IAAI,cAAc,CAAC,IAAI;IACzD;AASA,aAAS,sBAAsB,MAAa;AAC1C,aACE,YAAA,8BACA,KAAK,SAAS,UACd,GAAA,YAAA,mBAAkB,KAAK,MAAM,KAAK,OAAO;IAE7C;;;;;;;;;;;;;ACrIA,YAAA,UAAAC;AArBA,QAAA,oBAAA,gBAAA,qBAAA;AAUwC,YAAA,YAVjC,kBAAA;AAEP,QAAA,wBAAA,gBAAA,6BAAA;AAQS,YAAA,oBARF,sBAAA;AACP,QAAA,iBAAA,gBAAA,sBAAA;AAO4B,YAAA,aAPrB,eAAA;AAGP,QAAA,eAAA;AAAS,WAAA,eAAA,SAAA,WAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,aAAA;IAAO,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,WAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,aAAA;IAAO,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,yBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,aAAA;IAAqB,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,QAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,aAAA;IAAI,EAAA,CAAA;AAMtD,QAAM,mBAAmB,EAAE,yBAAyB,MAAK;AASzD,aAAwBA,iBACtB,MACA,SAAgC;AAEhC,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,UAAU,iCAAiC;MACvD;AAEA,UAAI,CAAC,MAAM;AACT,eAAO,CAAA;MACT;AAEA,cAAO,GAAA,eAAA,UACL,GAAA,kBAAA,SAAU,OAAM,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAe,gBAAgB,GACxD,OAAO;IAEX;;;;;ACrCA,iBAA4B;AAE5B,IAAAC,cAQO;AAEP,IAAO,cAAQ,WAAAC,QAAgB,WAAW,WAAAA;", "names": ["ElementType", "d", "b", "Node", "DataNode", "Text", "Comment", "ProcessingInstruction", "NodeWithChildren", "CDATA", "Document", "Element", "require_lib", "exports", "<PERSON><PERSON><PERSON><PERSON>", "require_lib", "attributesToProps", "domToReact", "HTMLReactParser", "import_lib", "HTMLReactParser"]}