"use client";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Content,
  Root,
  Trigger,
  createCollapsibleScope
} from "./chunk-LSVM5YOW.js";
import "./chunk-HH52Z57G.js";
import "./chunk-EXIWMNQP.js";
import "./chunk-XOUBXFGS.js";
import "./chunk-25LYFW5S.js";
import "./chunk-RMZVUWWX.js";
import "./chunk-6IRB4OMX.js";
import "./chunk-S7EUPB6E.js";
import "./chunk-NFC5BX5N.js";
import "./chunk-7BUGFXDR.js";
import "./chunk-CMM6OKGN.js";
import "./chunk-OL46QLBJ.js";
export {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Content,
  Root,
  Trigger,
  createCollapsibleScope
};
//# sourceMappingURL=@radix-ui_react-collapsible.js.map
