import {
   PersonalizationFormData,
   PersonalizationRawFormData,
} from '@/pages/settings/Personalization/utils/types';
import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';
interface personalizationPayload {
   client_id: string;
   user_id: string;
}
interface fetchPersonalizationResponse {
   status: boolean;
   data: PersonalizationFormData;
}
export interface FetchPersonalizationRawResponse {
   status: boolean;
   data: PersonalizationRawFormData;
}
export interface savePersonalizationPayload {
   client_id: string;
   user_id: string;
   data: PersonalizationFormData;
}

interface savePersonalizationResponse {
   status: boolean;
   message: string;
}
interface Endpoints {
   fetchPersonalization: (
      payload: personalizationPayload,
   ) => PromiseAxios<FetchPersonalizationRawResponse>;

   savePersonalization: (
      payload: savePersonalizationPayload,
   ) => PromiseAxios<savePersonalizationResponse>;
}

const personalizationAPI: Endpoints = {
   fetchPersonalization: (payload) => {
      return dashboardApiAgent.get(
         `/personalization/${payload.client_id}/${payload.user_id}`,
      );
   },
   savePersonalization: (payload) => {
      return dashboardApiAgent.post(
         `/personalization/${payload.client_id}/${payload.user_id}`,
         payload,
      );
   },
};

export default personalizationAPI;
