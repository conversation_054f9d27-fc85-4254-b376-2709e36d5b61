import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import personalizationAPI from '@/api/service/personalization';
import {PersonalizationFormData }from '../utils/types';

export const useFetchPersonalization = () => {
  const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

  return useApiQuery({
    queryKey: ['user-personalization', client_id, user_id],
    queryFn: () => personalizationAPI.fetchPersonalization({ client_id, user_id }),
    enabled: !!client_id && !!user_id,   // only fetch if IDs exist
    refetchOnWindowFocus: false,
  });
};

export const  useSaveorUpdatePersonalization = () => {
     const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;
  return useApiMutation({
    queryKey: ['save-personalization'],
    mutationFn:(payload: PersonalizationFormData) =>
      personalizationAPI.savePersonalization({
        client_id,
        user_id,
        data: payload,
      }),
  });
};
