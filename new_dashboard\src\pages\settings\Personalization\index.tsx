import { useState, useRef, useEffect } from 'react';
import StepChannels from './components/StepChannels';
import StepKPISetup from '../Personalization/components/StepKPISetup/StepKPISetup';
import StepReview from './components/StepReview';
import { Box, Divider, Flex, Button } from '@chakra-ui/react';
import ProgressBar from './components/ProgressBar';
import { PersonalizationFormData } from './utils/types';
import { useFetchPersonalization } from "./apis/personalization-apis";
import { Spinner } from '@chakra-ui/react';
import PersonalizationSummary from './PersonalizationSummary';
const steps = ['Channels', 'KPI Setup', 'Review'];

const Personalization = () => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<PersonalizationFormData>({
    selectedChannels: [],
    channelConfigs: {},
    agentPersona: '',
  });
  const [isEditing, setIsEditing] = useState(false);
  const reviewRef = useRef<{ handleSave: () => void }>(null);
   const { data: existingData, isLoading } = useFetchPersonalization();
 useEffect(() => {
    if (existingData?.status && existingData?.data) {
       if (existingData) {
    const raw = existingData.data;

    const normalized = {
    agentPersona: raw.agent_persona || "",
      selectedChannels: raw.selected_channels || [],
      channelConfigs: raw.channel_configs || {},
    };

    setFormData(normalized);
  }
    }
  }, [existingData]);
  const nextStep = () => {
    if (step === steps.length) {
      reviewRef.current?.handleSave();
    } else {
      if (step === 1 && formData.selectedChannels.length === 0) return;
      setStep((s) => Math.min(s + 1, steps.length));
    }
  };

  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const isNextDisabled = () => {
    switch (step) {
      case 1:
        return formData.selectedChannels.length === 0;
      case 2:
        return formData.selectedChannels.some(
          (ch) =>
            !formData.channelConfigs[ch]?.primaryKPI ||
            formData.channelConfigs[ch]?.benchmarks.length === 0
        );
      default:
        return false;
    }
  };
   if (isLoading) {
    return (
      <Flex justify="center" align="center" h="80vh">
        <Spinner size="lg" color="purple.500" />
      </Flex>
    );
  }

  if (!isEditing && existingData?.status && existingData?.data) {
    return (
      <PersonalizationSummary
        data={formData}
        onEdit={() => {
          setIsEditing(true);
          setStep(1);
        }}
      />
    );
  }
  const renderStep = () => {
    switch (step) {
      case 1:
        return <StepChannels data={formData} setData={setFormData} />;
      case 2:
        return <StepKPISetup data={formData} setData={setFormData} />;
      case 3:
        return <StepReview ref={reviewRef} data={formData} />;
      default:
        return null;
    }
  };

  return (
    <Flex direction="column" minH="100vh" w="100%">
     
      <Box p={8} pb={0}>
        <Box mb={4} fontWeight="medium" fontFamily="Poppins, sans-serif" fontSize="xl">
          Customize Your AI CMO
        </Box>
        <ProgressBar steps={steps} currentStep={step} />
        <Divider mt={4} mb={6} borderColor="#D5D5D5" />
      </Box>

    
      <Box
         flex="1"
  overflowY="scroll"
  px={8}
  pb="90px"
  css={{
    scrollbarWidth: 'none',       
    msOverflowStyle: 'none',     
    '&::-webkit-scrollbar': {    
      display: 'none',
    },
  }}
      >
        {renderStep()}
      </Box>

      {/* Sticky footer */}
      <Box
        position="sticky"
        bottom="0"
        bg="white"
        borderTop="1px solid"
        borderColor="gray.200"
        p={4}
         zIndex={10}
         
      >
        <Flex justify="space-between" align="center">
          <Button variant="outline" onClick={prevStep}>
            Back
          </Button>
          <Flex gap={3}>
            <Button variant="ghost">Cancel</Button>
            <Button
              colorScheme="purple"
              onClick={nextStep}
              isDisabled={isNextDisabled()}
              opacity={isNextDisabled() ? 0.6 : 1}
              cursor={isNextDisabled() ? 'not-allowed' : 'pointer'}
            >
              {step === steps.length ? 'Save' : 'Next'}
            </Button>
          </Flex>
        </Flex>
      </Box>
    </Flex>
  );
};

export default Personalization;
