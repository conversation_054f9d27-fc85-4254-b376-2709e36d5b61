"use client";

import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface CustomChannelInstructionProps {
  value: string;
  onChange: (value: string) => void;
}

const CustomChannelInstruction = ({
  value,
  onChange,
}: CustomChannelInstructionProps) => {
  return (
    <div className="mt-8">
      <Label
        htmlFor="channelInstruction"
        className="text-sm font-medium text-gray-700 mb-2 block"
      >
        Customize this channel’s AI focus
      </Label>
      <Textarea
        id="channelInstruction"
        placeholder="e.g. For Google Ads, focus on optimizing CPC and keyword performance. For Meta Ads, prioritize CTR and ROAS insights."
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="min-h-[140px] resize-none focus-visible:ring-[#7F56D9]"
      />
      <p className="text-xs text-gray-500 mt-2">
        Describe how you want your AI CMO to interpret or prioritize data for
        this specific channel.
      </p>
    </div>
  );
};

export default CustomChannelInstruction;
