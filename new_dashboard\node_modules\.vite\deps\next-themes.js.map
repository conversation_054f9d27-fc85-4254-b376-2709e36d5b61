{"version": 3, "sources": ["../../next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "mappings": ";;;;;;;;;AAAa,QAAgB;AAAQ,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,MAAI;AAAC,MAAI,IAAE,SAAS,iBAAgB,IAAE,CAAC,SAAQ,MAAM;AAAE,WAAS,EAAE,GAAE;AAAC,KAAC,MAAM,QAAQ,CAAC,IAAE,IAAE,CAAC,CAAC,GAAG,QAAQ,OAAG;AAAC,UAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,IAAI,OAAG,EAAE,CAAC,KAAG,CAAC,IAAE;AAAE,WAAG,EAAE,UAAU,OAAO,GAAG,CAAC,GAAE,EAAE,UAAU,IAAI,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,KAAG,EAAE,aAAa,GAAE,CAAC;AAAA,IAAC,CAAC,GAAE,EAAE,CAAC;AAAA,EAAC;AAAC,WAAS,EAAE,GAAE;AAAC,SAAG,EAAE,SAAS,CAAC,MAAI,EAAE,MAAM,cAAY;AAAA,EAAE;AAAC,WAAS,IAAG;AAAC,WAAO,OAAO,WAAW,8BAA8B,EAAE,UAAQ,SAAO;AAAA,EAAO;AAAC,MAAG,EAAE,GAAE,CAAC;AAAA,MAAO,KAAG;AAAC,QAAI,IAAE,aAAa,QAAQ,CAAC,KAAG,GAAE,IAAE,KAAG,MAAI,WAAS,EAAE,IAAE;AAAE,MAAE,CAAC;AAAA,EAAC,SAAO,GAAE;AAAA,EAAC;AAAC;AAAE,IAAI,IAAE,CAAC,SAAQ,MAAM;AAArB,IAAuB,IAAE;AAAzB,IAAwD,IAAE,OAAO,UAAQ;AAAzE,IAAqF,IAAI,gBAAc,MAAM;AAA7G,IAA+G,IAAE,EAAC,UAAS,OAAG;AAAC,GAAE,QAAO,CAAC,EAAC;AAA1I,IAA4I,IAAE,MAAI;AAAC,MAAI;AAAE,UAAO,IAAI,aAAW,CAAC,MAAI,OAAK,IAAE;AAAC;AAA5L,IAA8L,IAAE,OAAK,aAAW,CAAC,IAAI,gBAAgB,YAAS,MAAK,EAAE,QAAQ,IAAI,gBAAc,GAAE,EAAC,GAAG,EAAC,CAAC;AAAvR,IAAyR,IAAE,CAAC,SAAQ,MAAM;AAA1S,IAA4S,IAAE,CAAC,EAAC,aAAY,GAAE,2BAA0B,IAAE,OAAG,cAAa,IAAE,MAAG,mBAAkB,IAAE,MAAG,YAAW,IAAE,SAAQ,QAAO,IAAE,GAAE,cAAa,IAAE,IAAE,WAAS,SAAQ,WAAU,IAAE,cAAa,OAAM,GAAE,UAAS,GAAE,OAAM,GAAE,aAAY,EAAC,MAAI;AAAC,MAAG,CAAC,GAAE,CAAC,IAAI,WAAS,MAAI,EAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAI,WAAS,MAAI,MAAI,WAAS,EAAE,IAAE,CAAC,GAAE,IAAE,IAAE,OAAO,OAAO,CAAC,IAAE,GAAE,IAAI,cAAY,OAAG;AAAC,QAAI,IAAE;AAAE,QAAG,CAAC,EAAE;AAAO,UAAI,YAAU,MAAI,IAAE,EAAE;AAAG,QAAI,IAAE,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,IAAE,EAAE,CAAC,IAAE,MAAK,IAAE,SAAS,iBAAgB,IAAE,OAAG;AAAC,YAAI,WAAS,EAAE,UAAU,OAAO,GAAG,CAAC,GAAE,KAAG,EAAE,UAAU,IAAI,CAAC,KAAG,EAAE,WAAW,OAAO,MAAI,IAAE,EAAE,aAAa,GAAE,CAAC,IAAE,EAAE,gBAAgB,CAAC;AAAA,IAAE;AAAE,QAAG,MAAM,QAAQ,CAAC,IAAE,EAAE,QAAQ,CAAC,IAAE,EAAE,CAAC,GAAE,GAAE;AAAC,UAAI,IAAE,EAAE,SAAS,CAAC,IAAE,IAAE,MAAK,IAAE,EAAE,SAAS,CAAC,IAAE,IAAE;AAAE,QAAE,MAAM,cAAY;AAAA,IAAC;AAAC,SAAG,QAAM,EAAE;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAI,cAAY,OAAG;AAAC,QAAI,IAAE,OAAO,KAAG,aAAW,EAAE,CAAC,IAAE;AAAE,MAAE,CAAC;AAAE,QAAG;AAAC,mBAAa,QAAQ,GAAE,CAAC;AAAA,IAAC,SAAO,GAAE;AAAA,IAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAI,cAAY,OAAG;AAAC,QAAI,IAAE,EAAE,CAAC;AAAE,MAAE,CAAC,GAAE,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE,QAAQ;AAAA,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,EAAE,YAAU,MAAI;AAAC,QAAI,IAAE,OAAO,WAAW,CAAC;AAAE,WAAO,EAAE,YAAY,CAAC,GAAE,EAAE,CAAC,GAAE,MAAI,EAAE,eAAe,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAI,YAAU,MAAI;AAAC,QAAI,IAAE,OAAG;AAAC,QAAE,QAAM,MAAI,EAAE,WAAS,EAAE,EAAE,QAAQ,IAAE,EAAE,CAAC;AAAA,IAAE;AAAE,WAAO,OAAO,iBAAiB,WAAU,CAAC,GAAE,MAAI,OAAO,oBAAoB,WAAU,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAI,YAAU,MAAI;AAAC,MAAE,KAAG,OAAK,IAAE,CAAC;AAAA,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,MAAI,IAAI,UAAQ,OAAK,EAAC,OAAM,GAAE,UAAS,GAAE,aAAY,GAAE,eAAc,MAAI,WAAS,IAAE,GAAE,QAAO,IAAE,CAAC,GAAG,GAAE,QAAQ,IAAE,GAAE,aAAY,IAAE,IAAE,OAAM,IAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,SAAS,gBAAc,EAAE,UAAS,EAAC,OAAM,EAAC,GAAI,gBAAc,GAAE,EAAC,aAAY,GAAE,YAAW,GAAE,WAAU,GAAE,cAAa,GAAE,mBAAkB,GAAE,cAAa,GAAE,OAAM,GAAE,QAAO,GAAE,OAAM,GAAE,aAAY,EAAC,CAAC,GAAE,CAAC;AAAC;AAAxzD,IAA0zD,IAAI,OAAK,CAAC,EAAC,aAAY,GAAE,YAAW,GAAE,WAAU,GAAE,cAAa,GAAE,mBAAkB,GAAE,cAAa,GAAE,OAAM,GAAE,QAAO,GAAE,OAAM,GAAE,aAAY,EAAC,MAAI;AAAC,MAAI,IAAE,KAAK,UAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,MAAM,GAAE,EAAE;AAAE,SAAS,gBAAc,UAAS,EAAC,GAAG,GAAE,0BAAyB,MAAG,OAAM,OAAO,UAAQ,cAAY,IAAE,IAAG,yBAAwB,EAAC,QAAO,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,IAAG,EAAC,CAAC;AAAC,CAAC;AAAlqE,IAAoqE,IAAE,CAAC,GAAE,MAAI;AAAC,MAAG,EAAE;AAAO,MAAI;AAAE,MAAG;AAAC,QAAE,aAAa,QAAQ,CAAC,KAAG;AAAA,EAAM,SAAO,GAAE;AAAA,EAAC;AAAC,SAAO,KAAG;AAAC;AAA3vE,IAA6vE,IAAE,OAAG;AAAC,MAAI,IAAE,SAAS,cAAc,OAAO;AAAE,SAAO,KAAG,EAAE,aAAa,SAAQ,CAAC,GAAE,EAAE,YAAY,SAAS,eAAe,6KAA6K,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC,GAAE,MAAI;AAAC,WAAO,iBAAiB,SAAS,IAAI,GAAE,WAAW,MAAI;AAAC,eAAS,KAAK,YAAY,CAAC;AAAA,IAAC,GAAE,CAAC;AAAA,EAAC;AAAC;AAA7pF,IAA+pF,IAAE,QAAI,MAAI,IAAE,OAAO,WAAW,CAAC,IAAG,EAAE,UAAQ,SAAO;", "names": []}