import { useState, useEffect } from 'react';
import { Box, Divider,Text } from '@chakra-ui/react';
import {
  PersonalizationFormData,
  ChannelKPIConfig,
} from '../../utils/types';
import ChannelTabs from '../StepKPISetup/ChannelTabs';
import KPISelector from '../StepKPISetup/KPISelector';
//import WeightingSlider from '../StepKPISetup/WeightingSlider';
import BenchmarksTable from './BenchMarkTable/BenchmarksTable';
import { KPIBenchmark } from '../../utils/types';
import { useAppSelector } from '@/store/store';
import { Keys, LocalStorageService } from "@/utils/local-storage";
import { useApiQuery } from '@/hooks/react-query-hooks';
import kpiService from '@/api/service/kpi/index';
import CustomChannelInstruction from './CustomChannelInstruction';
interface StepKPISetupProps {
  data: PersonalizationFormData;
  setData: (data: PersonalizationFormData) => void;
}

const StepKPISetup = ({ data, setData }: StepKPISetupProps) => {
  const selectedChannels = data.selectedChannels || [];
  
 // const channelConfigs = data.channelConfigs || {};
  
    const kpiMetaRedux = useAppSelector((state) => state.kpi.kpiMeta);
     const { data: kpiMetaFetched } = useApiQuery({
    queryKey: ["kpiMetaPersonalization"],
    queryFn: () =>
      kpiService.getKpiMeta(LocalStorageService.getItem(Keys.ClientId) as string),
    enabled: !kpiMetaRedux || kpiMetaRedux.length === 0, // only fetch if needed
    staleTime: Infinity,
    refetchOnWindowFocus: false,
  });
 const kpiMeta = kpiMetaRedux?.length ? kpiMetaRedux : kpiMetaFetched || [];
  const [activeChannel, setActiveChannel] = useState<string>(
    selectedChannels[0] || ''
  );
  console.log('activeChannel', activeChannel);


  useEffect(() => {
    if (selectedChannels.length > 0 && !activeChannel) {
      setActiveChannel(selectedChannels[0]);
    }
  }, [selectedChannels, activeChannel]);

 
  const updateChannelConfig = (
    channelId: string,
    updates: Partial<ChannelKPIConfig>
  ) => {
    if (!channelId) return;
    const existingConfig: ChannelKPIConfig = data.channelConfigs[channelId] || {
    primaryKPI: '',
    secondaryKPI: '',
    tertiaryKPI: '',
    weighting: { primary: 60, secondary: 30, tertiary: 10 },
     benchmarks: [] as KPIBenchmark[],
  };

  const newConfig: ChannelKPIConfig = {
    ...existingConfig,
    ...updates,
    weighting: {
      ...existingConfig.weighting,
      ...(updates.weighting || {}),
    },
    benchmarks: updates.benchmarks ?? existingConfig.benchmarks ?? [],
  };

  setData({
    ...data,
    channelConfigs: {
      ...data.channelConfigs,
      [channelId]: newConfig,
    },
  });
  };

  
  const activeConfig: ChannelKPIConfig = data.channelConfigs?.[activeChannel] || {
    primaryKPI: '',
    secondaryKPI: '',
    tertiaryKPI: '',
    weighting: { primary: 60, secondary: 30 ,tertiary: 10},
     benchmarks: [] as KPIBenchmark[],
  };

  
  if (!selectedChannels.length) {
    return (
      <Box mt={10} textAlign="center">
        <Text fontSize="md" color="gray.600">
          No channels selected yet. Please go back and choose at least one channel.
        </Text>
      </Box>
    );
  }

  return (
    <Box>
      
      <Text fontWeight="semibold" mb={2}>
        Channel KPI Setup
      </Text>
      <Text fontSize="sm" color="gray.600" mb={6}>
        Set KPIs, weights, and targets for each selected channel.
      </Text>

     
      <ChannelTabs
        channels={selectedChannels}
        activeChannel={activeChannel}
        onSelect={setActiveChannel}
      />

      <Divider my={6} />

     
      <KPISelector
        config={activeConfig}
        activeChannel={activeChannel}
        kpiMeta={kpiMeta}
        onChange={(update) => updateChannelConfig(activeChannel, update)}
      />

      {/*<WeightingSlider
        weighting={activeConfig.weighting}
        onChange={(update) =>
          updateChannelConfig(activeChannel, { weighting: update })
        }
      />*/}

     
      {activeChannel && (
     <BenchmarksTable
  channelId={activeChannel}
  kpiMeta={kpiMeta}
  benchmarks={activeConfig.benchmarks}
  onChange={(b: KPIBenchmark[]) =>
    updateChannelConfig(activeChannel, { benchmarks: b })
  }

/>

      )}
         <CustomChannelInstruction
      value={activeConfig.channelPersona || ""}
      onChange={(val) =>
        updateChannelConfig(activeChannel, { channelPersona: val })
      }
    />
    </Box>
  );
};

export default StepKPISetup;
