import { Flex, Button } from '@chakra-ui/react';
import { CHANNEL_MAP } from '../../utils/channels';
interface ChannelTabsProps {
  channels: string[];
  activeChannel: string;
  onSelect: (id: string) => void;
}


const ChannelTabs = ({ channels, activeChannel, onSelect }: ChannelTabsProps) => {
  return (
    <Flex gap={3} mb={4} flexWrap="wrap">
      {channels.map((ch) => {
        const channel = CHANNEL_MAP[ch]; 
        if (!channel) return null;

        return (
          <Button
            key={ch}
            onClick={() => onSelect(ch)}
            variant={activeChannel === ch ? "solid" : "outline"}
            colorScheme="purple"
            size="sm"
          
          >
            {channel.name}
          </Button>
        );
      })}
    </Flex>
  );
};

export default ChannelTabs;