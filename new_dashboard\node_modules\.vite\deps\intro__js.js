import "./chunk-OL46QLBJ.js";

// node_modules/intro.js/intro.module.js
var t = function(e2, n2) {
  return t = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(t2, e3) {
    t2.__proto__ = e3;
  } || function(t2, e3) {
    for (var n3 in e3) Object.prototype.hasOwnProperty.call(e3, n3) && (t2[n3] = e3[n3]);
  }, t(e2, n2);
};
function e(t2, e2, n2, o2) {
  return new (n2 || (n2 = Promise))(function(i2, r2) {
    function s2(t3) {
      try {
        l2(o2.next(t3));
      } catch (t4) {
        r2(t4);
      }
    }
    function a2(t3) {
      try {
        l2(o2.throw(t3));
      } catch (t4) {
        r2(t4);
      }
    }
    function l2(t3) {
      var e3;
      t3.done ? i2(t3.value) : (e3 = t3.value, e3 instanceof n2 ? e3 : new n2(function(t4) {
        t4(e3);
      })).then(s2, a2);
    }
    l2((o2 = o2.apply(t2, e2 || [])).next());
  });
}
function n(t2, e2) {
  var n2, o2, i2, r2, s2 = { label: 0, sent: function() {
    if (1 & i2[0]) throw i2[1];
    return i2[1];
  }, trys: [], ops: [] };
  return r2 = { next: a2(0), throw: a2(1), return: a2(2) }, "function" == typeof Symbol && (r2[Symbol.iterator] = function() {
    return this;
  }), r2;
  function a2(a3) {
    return function(l2) {
      return function(a4) {
        if (n2) throw new TypeError("Generator is already executing.");
        for (; r2 && (r2 = 0, a4[0] && (s2 = 0)), s2; ) try {
          if (n2 = 1, o2 && (i2 = 2 & a4[0] ? o2.return : a4[0] ? o2.throw || ((i2 = o2.return) && i2.call(o2), 0) : o2.next) && !(i2 = i2.call(o2, a4[1])).done) return i2;
          switch (o2 = 0, i2 && (a4 = [2 & a4[0], i2.value]), a4[0]) {
            case 0:
            case 1:
              i2 = a4;
              break;
            case 4:
              return s2.label++, { value: a4[1], done: false };
            case 5:
              s2.label++, o2 = a4[1], a4 = [0];
              continue;
            case 7:
              a4 = s2.ops.pop(), s2.trys.pop();
              continue;
            default:
              if (!(i2 = s2.trys, (i2 = i2.length > 0 && i2[i2.length - 1]) || 6 !== a4[0] && 2 !== a4[0])) {
                s2 = 0;
                continue;
              }
              if (3 === a4[0] && (!i2 || a4[1] > i2[0] && a4[1] < i2[3])) {
                s2.label = a4[1];
                break;
              }
              if (6 === a4[0] && s2.label < i2[1]) {
                s2.label = i2[1], i2 = a4;
                break;
              }
              if (i2 && s2.label < i2[2]) {
                s2.label = i2[2], s2.ops.push(a4);
                break;
              }
              i2[2] && s2.ops.pop(), s2.trys.pop();
              continue;
          }
          a4 = e2.call(t2, s2);
        } catch (t3) {
          a4 = [6, t3], o2 = 0;
        } finally {
          n2 = i2 = 0;
        }
        if (5 & a4[0]) throw a4[1];
        return { value: a4[0] ? a4[1] : void 0, done: true };
      }([a3, l2]);
    };
  }
}
function o(t2) {
  return o = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(t3) {
    return typeof t3;
  } : function(t3) {
    return t3 && "function" == typeof Symbol && t3.constructor === Symbol && t3 !== Symbol.prototype ? "symbol" : typeof t3;
  }, o(t2);
}
function i(t2) {
  if (null === t2 || "object" !== o(t2) || "nodeType" in t2) return t2;
  var e2 = {};
  for (var n2 in t2) "jQuery" in window && window.jQuery && t2[n2] instanceof window.jQuery ? e2[n2] = t2[n2] : e2[n2] = i(t2[n2]);
  return e2;
}
var r = function(t2, e2) {
  return (null != e2 ? e2 : document).querySelector(t2);
};
var s = function(t2, e2) {
  return (null != e2 ? e2 : document).querySelectorAll(t2);
};
var a = function(t2, e2) {
  return r(".".concat(t2), e2);
};
var l = function(t2, e2) {
  return s(".".concat(t2), e2);
};
var c = function(t2, e2) {
  var n2 = a(t2, e2);
  if (!n2) throw new Error("Element with class name ".concat(t2, " not found"));
  return n2;
};
function u(t2, e2, n2) {
  return t2[e2] = n2, t2;
}
function p(t2, e2) {
  for (var n2 = 0, o2 = Object.entries(e2); n2 < o2.length; n2++) {
    var i2 = o2[n2];
    t2 = u(t2, i2[0], i2[1]);
  }
  return t2;
}
var h = function(t2) {
  return "function" == typeof t2;
};
function d(t2, e2) {
  var n2 = "";
  return "currentStyle" in t2 ? n2 = t2.currentStyle[e2] : document.defaultView && document.defaultView.getComputedStyle && (n2 = document.defaultView.getComputedStyle(t2, null).getPropertyValue(e2)), n2 && n2.toLowerCase ? n2.toLowerCase() : n2;
}
function f(t2) {
  var e2 = t2.parentElement;
  return !(!e2 || "HTML" === e2.nodeName) && ("fixed" === d(t2, "position") || f(e2));
}
function g(t2, e2) {
  var n2 = document.body, o2 = document.documentElement, i2 = window.pageYOffset || o2.scrollTop || n2.scrollTop, r2 = window.pageXOffset || o2.scrollLeft || n2.scrollLeft;
  e2 = e2 || n2;
  var s2 = t2.getBoundingClientRect(), a2 = e2.getBoundingClientRect(), l2 = d(e2, "position"), c2 = { width: s2.width, height: s2.height };
  return "body" !== e2.tagName.toLowerCase() && "relative" === l2 || "sticky" === l2 ? Object.assign(c2, { top: s2.top - a2.top, left: s2.left - a2.left }) : f(t2) ? Object.assign(c2, { top: s2.top, left: s2.left }) : Object.assign(c2, { top: s2.top + i2, left: s2.left + r2 });
}
var v = function(t2, e2, n2) {
  if (void 0 !== n2) {
    var o2 = g(n2), i2 = 20, r2 = 20;
    switch (t2) {
      default:
      case "top-left":
        e2.style.left = "".concat(o2.left, "px"), e2.style.top = "".concat(o2.top, "px");
        break;
      case "top-right":
        e2.style.left = "".concat(o2.left + o2.width - i2, "px"), e2.style.top = "".concat(o2.top, "px");
        break;
      case "bottom-left":
        e2.style.left = "".concat(o2.left, "px"), e2.style.top = "".concat(o2.top + o2.height - r2, "px");
        break;
      case "bottom-right":
        e2.style.left = "".concat(o2.left + o2.width - i2, "px"), e2.style.top = "".concat(o2.top + o2.height - r2, "px");
        break;
      case "middle-left":
        e2.style.left = "".concat(o2.left, "px"), e2.style.top = "".concat(o2.top + (o2.height - r2) / 2, "px");
        break;
      case "middle-right":
        e2.style.left = "".concat(o2.left + o2.width - i2, "px"), e2.style.top = "".concat(o2.top + (o2.height - r2) / 2, "px");
        break;
      case "middle-middle":
        e2.style.left = "".concat(o2.left + (o2.width - i2) / 2, "px"), e2.style.top = "".concat(o2.top + (o2.height - r2) / 2, "px");
        break;
      case "bottom-middle":
        e2.style.left = "".concat(o2.left + (o2.width - i2) / 2, "px"), e2.style.top = "".concat(o2.top + o2.height - r2, "px");
        break;
      case "top-middle":
        e2.style.left = "".concat(o2.left + (o2.width - i2) / 2, "px"), e2.style.top = "".concat(o2.top, "px");
    }
  }
};
var b = new (function() {
  function t2() {
  }
  return t2.prototype.on = function(t3, e2, n2, o2) {
    "addEventListener" in t3 ? t3.addEventListener(e2, n2, o2) : "attachEvent" in t3 && t3.attachEvent("on".concat(e2), n2);
  }, t2.prototype.off = function(t3, e2, n2, o2) {
    "removeEventListener" in t3 ? t3.removeEventListener(e2, n2, o2) : "detachEvent" in t3 && t3.detachEvent("on".concat(e2), n2);
  }, t2;
}())();
var m = function(t2) {
  return t2 ? "string" == typeof t2 ? function(t3, e2) {
    var n2 = r(t3, e2);
    if (!n2) throw new Error("Element with selector ".concat(t3, " not found"));
    return n2;
  }(t2) : t2 : document.body;
};
function y(t2, e2) {
  var n2 = "";
  if (t2.style.cssText && (n2 += t2.style.cssText), "string" == typeof e2) n2 += e2;
  else for (var o2 in e2) n2 += "".concat(o2, ":").concat(e2[o2], ";");
  t2.style.cssText = n2;
}
function w(t2, e2) {
  var n2 = document.createElement(t2);
  e2 = e2 || {};
  var o2 = /^(?:role|data-|aria-)/;
  for (var i2 in e2) {
    var r2 = e2[i2];
    "style" === i2 && "function" != typeof r2 ? y(n2, r2) : "string" == typeof r2 && i2.match(o2) ? n2.setAttribute(i2, r2) : n2[i2] = r2;
  }
  return n2;
}
function C(t2) {
  t2.setAttribute("role", "button"), t2.tabIndex = 0;
}
var S = function(t2) {
  for (var e2 = [], n2 = 1; n2 < arguments.length; n2++) e2[n2 - 1] = arguments[n2];
  for (var o2 = 0, i2 = e2; o2 < i2.length; o2++) {
    var r2 = i2[o2];
    if (t2 instanceof SVGElement) {
      var s2 = t2.getAttribute("class") || "";
      s2.match(r2) || O(t2, s2, r2);
    } else void 0 !== t2.classList ? t2.classList.add(r2) : t2.className.match(r2) || O(t2, t2.className, r2);
  }
};
var O = function(t2) {
  for (var e2 = [], n2 = 1; n2 < arguments.length; n2++) e2[n2 - 1] = arguments[n2];
  var o2 = e2.filter(Boolean).join(" ");
  t2 instanceof SVGElement ? t2.setAttribute("class", o2) : void 0 !== t2.classList ? t2.classList.value = o2 : t2.className = o2;
};
var j = function(t2, e2) {
  if (t2 instanceof SVGElement) {
    var n2 = t2.getAttribute("class") || "";
    t2.setAttribute("class", n2.replace(e2, "").replace(/\s\s+/g, " ").trim());
  } else t2.className = t2.className.replace(e2, "").replace(/\s\s+/g, " ").trim();
};
var k = function() {
  return a("introjs-hints");
};
var x = function() {
  return l("introjs-hint", k());
};
var A = function(t2) {
  return l("".concat("introjs-hint", "[").concat("data-step", '="').concat(t2, '"]'), k())[0];
};
function E(t2, o2) {
  var i2;
  return e(this, void 0, void 0, function() {
    var e2;
    return n(this, function(n2) {
      return e2 = A(o2), M(), e2 && S(e2, "introjs-hidehint"), null === (i2 = t2.callback("hintClose")) || void 0 === i2 || i2.call(t2, o2), [2];
    });
  });
}
function T(t2) {
  return e(this, void 0, void 0, function() {
    var e2, o2, i2, r2, s2;
    return n(this, function(n2) {
      switch (n2.label) {
        case 0:
          e2 = x(), o2 = 0, i2 = Array.from(e2), n2.label = 1;
        case 1:
          return o2 < i2.length ? (r2 = i2[o2], (s2 = r2.getAttribute("data-step")) ? [4, E(t2, parseInt(s2, 10))] : [3, 3]) : [3, 4];
        case 2:
          n2.sent(), n2.label = 3;
        case 3:
          return o2++, [3, 1];
        case 4:
          return [2];
      }
    });
  });
}
var _ = function(t2, e2, n2, o2) {
  if (e2 && t2 && n2) {
    n2 instanceof Element && f(n2) ? S(e2, "introjs-fixedTooltip") : j(e2, "introjs-fixedTooltip");
    var i2 = g(n2, t2);
    y(e2, { width: "".concat(i2.width + o2, "px"), height: "".concat(i2.height + o2, "px"), top: "".concat(i2.top - o2 / 2, "px"), left: "".concat(i2.left - o2 / 2, "px") });
  }
};
function H() {
  if (void 0 !== window.innerWidth) return { width: window.innerWidth, height: window.innerHeight };
  var t2 = document.documentElement;
  return { width: t2.clientWidth, height: t2.clientHeight };
}
function N(t2, e2, n2, o2, i2) {
  return t2.left + e2 + n2.width > o2.width ? (i2.style.left = "".concat(o2.width - n2.width - t2.left, "px"), false) : (i2.style.left = "".concat(e2, "px"), true);
}
function L(t2, e2, n2, o2) {
  return t2.left + t2.width - e2 - n2.width < 0 ? (o2.style.left = "".concat(-t2.left, "px"), false) : (o2.style.right = "".concat(e2, "px"), true);
}
function P(t2, e2) {
  t2.includes(e2) && t2.splice(t2.indexOf(e2), 1);
}
function R(t2, e2, n2, o2) {
  var i2 = t2.slice(), r2 = H(), s2 = g(n2).height + 10, a2 = g(n2).width + 20, l2 = e2.getBoundingClientRect(), c2 = "floating";
  if (l2.bottom + s2 > r2.height && P(i2, "bottom"), l2.top - s2 < 0 && P(i2, "top"), l2.right + a2 > r2.width && P(i2, "right"), l2.left - a2 < 0 && P(i2, "left"), o2 && (o2 = o2.split("-")[0]), i2.length && (c2 = i2[0], i2.includes(o2) && (c2 = o2)), "top" === c2 || "bottom" === c2) {
    var u2 = void 0, p2 = [];
    "top" === c2 ? (u2 = "top-middle-aligned", p2 = ["top-left-aligned", "top-middle-aligned", "top-right-aligned"]) : (u2 = "bottom-middle-aligned", p2 = ["bottom-left-aligned", "bottom-middle-aligned", "bottom-right-aligned"]), c2 = function(t3, e3, n3, o3) {
      var i3 = e3 / 2, r3 = Math.min(n3, window.screen.width);
      return r3 - t3 < e3 && (P(o3, "top-left-aligned"), P(o3, "bottom-left-aligned")), (t3 < i3 || r3 - t3 < i3) && (P(o3, "top-middle-aligned"), P(o3, "bottom-middle-aligned")), t3 < e3 && (P(o3, "top-right-aligned"), P(o3, "bottom-right-aligned")), o3.length ? o3[0] : null;
    }(l2.left, a2, r2.width, p2) || u2;
  }
  return c2;
}
var B;
var I = function(t2, e2, n2, o2, i2, r2, s2, a2, l2) {
  var c2, u2, p2;
  void 0 === r2 && (r2 = false), void 0 === s2 && (s2 = true), void 0 === a2 && (a2 = ""), void 0 === l2 && (l2 = false), t2.style.top = "", t2.style.right = "", t2.style.bottom = "", t2.style.left = "", t2.style.marginLeft = "", t2.style.marginTop = "", e2.style.display = "inherit", O(t2, "introjs-tooltip", a2), t2.setAttribute("role", "dialog"), "floating" !== o2 && s2 && (o2 = R(i2, n2, t2, o2)), u2 = g(n2), c2 = g(t2), p2 = H(), S(t2, "introjs-".concat(o2));
  var h2 = u2.width / 2 - c2.width / 2;
  switch (o2) {
    case "top-right-aligned":
      O(e2, "introjs-arrow bottom-right");
      var d2 = 0;
      L(u2, d2, c2, t2), t2.style.bottom = "".concat(u2.height + 20, "px");
      break;
    case "top-middle-aligned":
      O(e2, "introjs-arrow bottom-middle"), l2 && (h2 += 5), L(u2, h2, c2, t2) && (t2.style.right = "", N(u2, h2, c2, p2, t2)), t2.style.bottom = "".concat(u2.height + 20, "px");
      break;
    case "top-left-aligned":
    case "top":
      O(e2, "introjs-arrow bottom"), N(u2, l2 ? 0 : 15, c2, p2, t2), t2.style.bottom = "".concat(u2.height + 20, "px");
      break;
    case "right":
      t2.style.left = "".concat(u2.width + 20, "px"), u2.top + c2.height > p2.height ? (O(e2, "introjs-arrow left-bottom"), t2.style.top = "-".concat(c2.height - u2.height - 20, "px")) : O(e2, "introjs-arrow left");
      break;
    case "left":
      l2 || true !== r2 || (t2.style.top = "15px"), u2.top + c2.height > p2.height ? (t2.style.top = "-".concat(c2.height - u2.height - 20, "px"), O(e2, "introjs-arrow right-bottom")) : O(e2, "introjs-arrow right"), t2.style.right = "".concat(u2.width + 20, "px");
      break;
    case "floating":
      e2.style.display = "none", t2.style.left = "50%", t2.style.top = "50%", t2.style.marginLeft = "-".concat(c2.width / 2, "px"), t2.style.marginTop = "-".concat(c2.height / 2, "px");
      break;
    case "bottom-right-aligned":
      O(e2, "introjs-arrow top-right"), L(u2, d2 = 0, c2, t2), t2.style.top = "".concat(u2.height + 20, "px");
      break;
    case "bottom-middle-aligned":
      O(e2, "introjs-arrow top-middle"), l2 && (h2 += 5), L(u2, h2, c2, t2) && (t2.style.right = "", N(u2, h2, c2, p2, t2)), t2.style.top = "".concat(u2.height + 20, "px");
      break;
    default:
      O(e2, "introjs-arrow top"), N(u2, 0, c2, p2, t2), t2.style.top = "".concat(u2.height + 20, "px");
  }
};
function M() {
  var t2 = a("introjs-hintReference");
  if (t2 && t2.parentNode) {
    var e2 = t2.getAttribute("data-step");
    if (!e2) return;
    return t2.parentNode.removeChild(t2), e2;
  }
}
function D(t2, o2) {
  var i2, s2;
  return e(this, void 0, void 0, function() {
    var e2, a2, l2, c2, u2, p2, h2, d2, f2, g2, v2, m2;
    return n(this, function(n2) {
      switch (n2.label) {
        case 0:
          return e2 = r(".".concat("introjs-hint", "[").concat("data-step", '="').concat(o2, '"]')), a2 = t2.getHint(o2), e2 && a2 ? [4, null === (i2 = t2.callback("hintClick")) || void 0 === i2 ? void 0 : i2.call(t2, e2, a2, o2)] : [2];
        case 1:
          return n2.sent(), void 0 !== (l2 = M()) && parseInt(l2, 10) === o2 ? [2] : (c2 = w("div", { className: "introjs-tooltip" }), u2 = w("div"), p2 = w("div"), h2 = w("div"), c2.onclick = function(t3) {
            t3.stopPropagation ? t3.stopPropagation() : t3.cancelBubble = true;
          }, O(u2, "introjs-tooltiptext"), (d2 = w("p")).innerHTML = a2.hint || "", u2.appendChild(d2), t2.getOption("hintShowButton") && ((f2 = w("a")).className = t2.getOption("buttonClass"), f2.setAttribute("role", "button"), f2.innerHTML = t2.getOption("hintButtonLabel"), f2.onclick = function() {
            return E(t2, o2);
          }, u2.appendChild(f2)), O(p2, "introjs-arrow"), c2.appendChild(p2), c2.appendChild(u2), g2 = e2.getAttribute("data-step") || "", (v2 = t2.getHint(parseInt(g2, 10))) ? (O(h2, "introjs-tooltipReferenceLayer", "introjs-hintReference"), h2.setAttribute("data-step", g2), m2 = t2.getOption("helperElementPadding"), _(t2.getTargetElement(), h2, v2.element, m2), h2.appendChild(c2), document.body.appendChild(h2), I(c2, p2, v2.element, v2.position, t2.getOption("positionPrecedence"), false, t2.getOption("autoPosition"), null !== (s2 = v2.tooltipClass) && void 0 !== s2 ? s2 : t2.getOption("tooltipClass")), B = function() {
            M(), b.off(document, "click", B, false);
          }, b.on(document, "click", B, false), [2]) : [2]);
      }
    });
  });
}
var z = function(t2, e2) {
  return function(n2) {
    var o2 = n2 || window.event;
    o2 && o2.stopPropagation && o2.stopPropagation(), o2 && null !== o2.cancelBubble && (o2.cancelBubble = true), D(t2, e2);
  };
};
function F(t2) {
  var o2;
  return e(this, void 0, void 0, function() {
    var e2, i2, s2, l2, c2, u2, p2;
    return n(this, function(n2) {
      for (null === (e2 = a("introjs-hints")) && (e2 = w("div", { className: "introjs-hints" })), i2 = t2.getHints(), s2 = 0; s2 < i2.length; s2++) {
        if (l2 = i2[s2], r(".".concat("introjs-hint", "[").concat("data-step", '="').concat(s2, '"]'))) return [2];
        C(c2 = w("a", { className: "introjs-hint" })), c2.onclick = z(t2, s2), l2.hintAnimation || S(c2, "introjs-hint-no-anim"), f(l2.element) && S(c2, "introjs-fixedhint"), u2 = w("div", { className: "introjs-hint-dot" }), p2 = w("div", { className: "introjs-hint-pulse" }), c2.appendChild(u2), c2.appendChild(p2), c2.setAttribute("data-step", s2.toString()), l2.hintTargetElement = l2.element, l2.element = c2, v(l2.hintPosition, c2, l2.hintTargetElement), e2.appendChild(c2);
      }
      return document.body.appendChild(e2), null === (o2 = t2.callback("hintsAdded")) || void 0 === o2 || o2.call(t2), t2.enableHintAutoRefresh(), [2];
    });
  });
}
function J(t2) {
  return e(this, void 0, void 0, function() {
    var e2, o2, i2, r2, s2;
    return n(this, function(n2) {
      switch (n2.label) {
        case 0:
          if (!(null == (e2 = x()) ? void 0 : e2.length)) return [3, 1];
          for (o2 = 0, i2 = Array.from(e2); o2 < i2.length; o2++) r2 = i2[o2], (s2 = r2.getAttribute("data-step")) && V(parseInt(s2, 10));
          return [3, 3];
        case 1:
          return [4, t2.render()];
        case 2:
          n2.sent(), n2.label = 3;
        case 3:
          return [2];
      }
    });
  });
}
function V(t2) {
  var e2 = A(t2);
  e2 && j(e2, new RegExp("introjs-hidehint", "g"));
}
function G(t2) {
  var e2 = A(t2);
  e2 && e2.parentNode && e2.parentNode.removeChild(e2);
}
var q = function() {
  function t2(t3, e2) {
    this._hints = [], this.callbacks = {}, this._targetElement = m(t3), this._options = e2 ? p(this._options, e2) : { hints: [], isActive: true, tooltipPosition: "bottom", tooltipClass: "", hintPosition: "top-middle", hintButtonLabel: "Got it", hintShowButton: true, hintAutoRefreshInterval: 10, hintAnimation: true, buttonClass: "introjs-button", helperElementPadding: 10, autoPosition: true, positionPrecedence: ["bottom", "top", "right", "left"] };
  }
  return t2.prototype.callback = function(t3) {
    var e2 = this.callbacks[t3];
    if (h(e2)) return e2;
  }, t2.prototype.getTargetElement = function() {
    return this._targetElement;
  }, t2.prototype.getHints = function() {
    return this._hints;
  }, t2.prototype.getHint = function(t3) {
    return this._hints[t3];
  }, t2.prototype.setHints = function(t3) {
    return this._hints = t3, this;
  }, t2.prototype.addHint = function(t3) {
    return this._hints.push(t3), this;
  }, t2.prototype.render = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        switch (t3.label) {
          case 0:
            return this.isActive() ? (function(t4) {
              t4.setHints([]);
              var e2 = t4.getTargetElement(), n2 = t4.getOption("hints");
              if (n2 && n2.length > 0) for (var o2 = 0, a2 = n2; o2 < a2.length; o2++) {
                var l2 = i(a2[o2]);
                "string" == typeof l2.element && (l2.element = r(l2.element)), l2.hintPosition = l2.hintPosition || t4.getOption("hintPosition"), l2.hintAnimation = l2.hintAnimation || t4.getOption("hintAnimation"), null !== l2.element && t4.addHint(l2);
              }
              else {
                var c2 = Array.from(s("*[".concat("data-hint", "]"), e2));
                if (!c2 || !c2.length) return false;
                for (var u2 = 0, p2 = c2; u2 < p2.length; u2++) {
                  var h2 = p2[u2], d2 = h2.getAttribute("data-hint-position"), f2 = t4.getOption("hintAnimation");
                  d2 && (f2 = "true" === d2), t4.addHint({ element: h2, hint: h2.getAttribute("data-hint") || "", hintPosition: h2.getAttribute("data-hint-position") || t4.getOption("hintPosition"), hintAnimation: f2, tooltipClass: h2.getAttribute("data-tooltip-class") || void 0, position: h2.getAttribute("data-position") || t4.getOption("tooltipPosition") });
                }
              }
            }(this), [4, F(this)]) : [2, this];
          case 1:
            return t3.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.addHints = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        return [2, this.render()];
      });
    });
  }, t2.prototype.hideHint = function(t3) {
    return e(this, void 0, void 0, function() {
      return n(this, function(e2) {
        switch (e2.label) {
          case 0:
            return [4, E(this, t3)];
          case 1:
            return e2.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.hideHints = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        switch (t3.label) {
          case 0:
            return [4, T(this)];
          case 1:
            return t3.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.showHint = function(t3) {
    return V(t3), this;
  }, t2.prototype.showHints = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        switch (t3.label) {
          case 0:
            return [4, J(this)];
          case 1:
            return t3.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.destroy = function() {
    return function(t3) {
      for (var e2 = x(), n2 = 0, o2 = Array.from(e2); n2 < o2.length; n2++) {
        var i2 = o2[n2].getAttribute("data-step");
        null !== i2 && G(parseInt(i2, 10));
      }
      t3.disableHintAutoRefresh();
    }(this), this;
  }, t2.prototype.removeHints = function() {
    return this.destroy(), this;
  }, t2.prototype.removeHint = function(t3) {
    return G(t3), this;
  }, t2.prototype.showHintDialog = function(t3) {
    return e(this, void 0, void 0, function() {
      return n(this, function(e2) {
        switch (e2.label) {
          case 0:
            return [4, D(this, t3)];
          case 1:
            return e2.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.enableHintAutoRefresh = function() {
    var t3, e2, n2, o2 = this, i2 = this.getOption("hintAutoRefreshInterval");
    return i2 >= 0 && (this._hintsAutoRefreshFunction = (t3 = function() {
      return function(t4) {
        for (var e3 = 0, n3 = t4.getHints(); e3 < n3.length; e3++) {
          var o3 = n3[e3], i3 = o3.hintTargetElement, r2 = o3.hintPosition, s2 = o3.element;
          v(r2, s2, i3);
        }
      }(o2);
    }, e2 = i2, function() {
      for (var o3 = [], i3 = 0; i3 < arguments.length; i3++) o3[i3] = arguments[i3];
      window.clearTimeout(n2), n2 = window.setTimeout(function() {
        t3(o3);
      }, e2);
    }), b.on(window, "scroll", this._hintsAutoRefreshFunction, true), b.on(window, "resize", this._hintsAutoRefreshFunction, true)), this;
  }, t2.prototype.disableHintAutoRefresh = function() {
    return this._hintsAutoRefreshFunction && (b.off(window, "scroll", this._hintsAutoRefreshFunction, true), b.on(window, "resize", this._hintsAutoRefreshFunction, true), this._hintsAutoRefreshFunction = void 0), this;
  }, t2.prototype.getOption = function(t3) {
    return this._options[t3];
  }, t2.prototype.setOptions = function(t3) {
    return this._options = p(this._options, t3), this;
  }, t2.prototype.setOption = function(t3, e2) {
    return this._options = u(this._options, t3, e2), this;
  }, t2.prototype.clone = function() {
    return new t2(this._targetElement, this._options);
  }, t2.prototype.isActive = function() {
    return this.getOption("isActive");
  }, t2.prototype.onHintsAdded = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onhintsadded was not a function.");
    return this.callbacks.hintsAdded = t3, this;
  }, t2.prototype.onhintsadded = function(t3) {
    this.onHintsAdded(t3);
  }, t2.prototype.onHintClick = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onhintclick was not a function.");
    return this.callbacks.hintClick = t3, this;
  }, t2.prototype.onhintclick = function(t3) {
    this.onHintClick(t3);
  }, t2.prototype.onHintClose = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onhintclose was not a function.");
    return this.callbacks.hintClose = t3, this;
  }, t2.prototype.onhintclose = function(t3) {
    this.onHintClose(t3);
  }, t2;
}();
function K(t2, e2) {
  if (t2) {
    var n2 = function(t3) {
      var e3 = window.getComputedStyle(t3), n3 = "absolute" === e3.position, o2 = /(auto|scroll)/;
      if ("fixed" === e3.position) return document.body;
      for (var i2 = t3; i2 = i2.parentElement; ) if (e3 = window.getComputedStyle(i2), (!n3 || "static" !== e3.position) && o2.test(e3.overflow + e3.overflowY + e3.overflowX)) return i2;
      return document.body;
    }(e2);
    n2 !== document.body && (n2.scrollTop = e2.offsetTop - n2.offsetTop);
  }
}
function W(t2, e2, n2, o2, i2) {
  var r2;
  if ("off" !== e2 && (t2 && (r2 = "tooltip" === e2 ? i2.getBoundingClientRect() : o2.getBoundingClientRect(), !function(t3) {
    var e3 = t3.getBoundingClientRect();
    return e3.top >= 0 && e3.left >= 0 && e3.bottom + 80 <= window.innerHeight && e3.right <= window.innerWidth;
  }(o2)))) {
    var s2 = H().height;
    r2.bottom - (r2.bottom - r2.top) < 0 || o2.clientHeight > s2 ? window.scrollBy(0, r2.top - (s2 / 2 - r2.height / 2) - n2) : window.scrollBy(0, r2.top - (s2 / 2 - r2.height / 2) + n2);
  }
}
function Q() {
  for (var t2 = 0, e2 = Array.from(l("introjs-showElement")); t2 < e2.length; t2++) {
    var n2 = e2[t2];
    j(n2, /introjs-[a-zA-Z]+/g);
  }
}
function X(t2, e2, n2) {
  if (void 0 === n2 && (n2 = false), n2) {
    var o2 = e2.style.opacity || "1";
    y(e2, { opacity: "0" }), window.setTimeout(function() {
      y(e2, { opacity: o2 });
    }, 10);
  }
  t2.appendChild(e2);
}
var Y;
var U = function(t2, e2, n2, o2) {
  _(t2, e2, n2.element, "floating" === n2.position ? 0 : o2);
};
var Z = function(t2, e2) {
  return (t2 + 1) / e2 * 100;
};
function $(t2, e2) {
  var n2 = w("div", { className: "introjs-bullets" });
  false === t2.getOption("showBullets") && (n2.style.display = "none");
  var o2 = w("ul");
  o2.setAttribute("role", "tablist");
  for (var i2 = function() {
    var e3 = this.getAttribute("data-step-number");
    null != e3 && t2.goToStep(parseInt(e3, 10));
  }, r2 = t2.getSteps(), s2 = 0; s2 < r2.length; s2++) {
    var a2 = r2[s2].step, l2 = w("li"), c2 = w("a");
    l2.setAttribute("role", "presentation"), c2.setAttribute("role", "tab"), c2.onclick = i2, s2 === e2.step - 1 && O(c2, "active"), C(c2), c2.innerHTML = "&nbsp;", c2.setAttribute("data-step-number", a2.toString()), l2.appendChild(c2), o2.appendChild(l2);
  }
  return n2.appendChild(o2), n2;
}
function tt(t2, e2, n2) {
  var o2 = r(".".concat("introjs-progress", " .").concat("introjs-progressbar"), t2);
  if (o2) {
    var i2 = Z(e2, n2);
    o2.style.cssText = "width:".concat(i2, "%;"), o2.setAttribute("aria-valuenow", i2.toString());
  }
}
function et(t2, o2) {
  var i2, s2, l2;
  return e(this, void 0, void 0, function() {
    var u2, p2, h2, f2, g2, v2, b2, m2, j2, k2, x2, A2, E2, T2, _2, H2, N2, L2, P2, R2, B2, M2, D2, z2, F2, J2 = this;
    return n(this, function(V2) {
      switch (V2.label) {
        case 0:
          return null === (i2 = t2.callback("change")) || void 0 === i2 || i2.call(t2, o2.element), u2 = a("introjs-helperLayer"), p2 = a("introjs-tooltipReferenceLayer"), h2 = "introjs-helperLayer", "string" == typeof o2.highlightClass && (h2 += " ".concat(o2.highlightClass)), "string" == typeof t2.getOption("highlightClass") && (h2 += " ".concat(t2.getOption("highlightClass"))), null !== u2 && null !== p2 ? (b2 = c("introjs-tooltiptext", p2), m2 = c("introjs-tooltip-title", p2), j2 = c("introjs-arrow", p2), k2 = c("introjs-tooltip", p2), v2 = c("introjs-skipbutton", p2), g2 = c("introjs-prevbutton", p2), f2 = c("introjs-nextbutton", p2), O(u2, h2), k2.style.opacity = "0", k2.style.display = "none", K(t2.getOption("scrollToElement"), o2.element), R2 = t2.getOption("helperElementPadding"), U(t2.getTargetElement(), u2, o2, R2), U(t2.getTargetElement(), p2, o2, R2), Q(), Y && window.clearTimeout(Y), x2 = a("introjs-helperNumberLayer", p2), Y = window.setTimeout(function() {
            var e2;
            null !== x2 && (x2.innerHTML = "".concat(o2.step, " ").concat(t2.getOption("stepNumbersOfLabel"), " ").concat(t2.getSteps().length)), b2.innerHTML = o2.intro || "", m2.innerHTML = o2.title || "", k2.style.display = "block", I(k2, j2, o2.element, o2.position, t2.getOption("positionPrecedence"), t2.getOption("showStepNumbers"), t2.getOption("autoPosition"), null !== (e2 = o2.tooltipClass) && void 0 !== e2 ? e2 : t2.getOption("tooltipClass")), function(t3, e3, n2) {
              if (t3) {
                var o3 = r(".".concat("introjs-bullets", " li > a.").concat("active"), e3), i3 = r(".".concat("introjs-bullets", " li > a[").concat("data-step-number", '="').concat(n2.step, '"]'), e3);
                o3 && i3 && (o3.className = "", O(i3, "active"));
              }
            }(t2.getOption("showBullets"), p2, o2), tt(p2, t2.getCurrentStep(), t2.getSteps().length), k2.style.opacity = "1", (f2 && new RegExp("introjs-donebutton", "gi").test(f2.className) || f2) && f2.focus(), W(t2.getOption("scrollToElement"), o2.scrollTo, t2.getOption("scrollPadding"), o2.element, b2);
          }, 350)) : (A2 = w("div", { className: h2 }), E2 = w("div", { className: "introjs-tooltipReferenceLayer" }), T2 = w("div", { className: "introjs-arrow" }), _2 = w("div", { className: "introjs-tooltip" }), H2 = w("div", { className: "introjs-tooltiptext" }), N2 = w("div", { className: "introjs-tooltip-header" }), L2 = w("h1", { className: "introjs-tooltip-title" }), P2 = w("div"), y(A2, { "box-shadow": "0 0 1px 2px rgba(33, 33, 33, 0.8), rgba(33, 33, 33, ".concat(t2.getOption("overlayOpacity").toString(), ") 0 0 0 5000px") }), K(t2.getOption("scrollToElement"), o2.element), R2 = t2.getOption("helperElementPadding"), U(t2.getTargetElement(), A2, o2, R2), U(t2.getTargetElement(), E2, o2, R2), X(t2.getTargetElement(), A2, true), X(t2.getTargetElement(), E2), H2.innerHTML = o2.intro, L2.innerHTML = o2.title, O(P2, "introjs-tooltipbuttons"), false === t2.getOption("showButtons") && (P2.style.display = "none"), N2.appendChild(L2), _2.appendChild(N2), _2.appendChild(H2), t2.getOption("dontShowAgain") && (B2 = w("div", { className: "introjs-dontShowAgain" }), (M2 = w("input", { type: "checkbox", id: "introjs-dontShowAgain", name: "introjs-dontShowAgain" })).onchange = function(e2) {
            t2.setDontShowAgain(e2.target.checked);
          }, (D2 = w("label", { htmlFor: "introjs-dontShowAgain" })).innerText = t2.getOption("dontShowAgainLabel"), B2.appendChild(M2), B2.appendChild(D2), _2.appendChild(B2)), _2.appendChild($(t2, o2)), _2.appendChild(function(t3) {
            var e2 = w("div");
            O(e2, "introjs-progress"), false === t3.getOption("showProgress") && (e2.style.display = "none");
            var n2 = w("div", { className: "introjs-progressbar" });
            t3.getOption("progressBarAdditionalClass") && S(n2, t3.getOption("progressBarAdditionalClass"));
            var o3 = Z(t3.getCurrentStep(), t3.getSteps().length);
            return n2.setAttribute("role", "progress"), n2.setAttribute("aria-valuemin", "0"), n2.setAttribute("aria-valuemax", "100"), n2.setAttribute("aria-valuenow", o3.toString()), n2.style.cssText = "width:".concat(o3, "%;"), e2.appendChild(n2), e2;
          }(t2)), z2 = w("div"), true === t2.getOption("showStepNumbers") && (O(z2, "introjs-helperNumberLayer"), z2.innerHTML = "".concat(o2.step, " ").concat(t2.getOption("stepNumbersOfLabel"), " ").concat(t2.getSteps().length), _2.appendChild(z2)), _2.appendChild(T2), E2.appendChild(_2), (f2 = w("a")).onclick = function() {
            return e(J2, void 0, void 0, function() {
              var e2;
              return n(this, function(n2) {
                switch (n2.label) {
                  case 0:
                    return t2.isLastStep() ? [3, 2] : [4, nt(t2)];
                  case 1:
                    return n2.sent(), [3, 5];
                  case 2:
                    return new RegExp("introjs-donebutton", "gi").test(f2.className) ? [4, null === (e2 = t2.callback("complete")) || void 0 === e2 ? void 0 : e2.call(t2, t2.getCurrentStep(), "done")] : [3, 5];
                  case 3:
                    return n2.sent(), [4, t2.exit()];
                  case 4:
                    n2.sent(), n2.label = 5;
                  case 5:
                    return [2];
                }
              });
            });
          }, C(f2), f2.innerHTML = t2.getOption("nextLabel"), (g2 = w("a")).onclick = function() {
            return e(J2, void 0, void 0, function() {
              return n(this, function(e2) {
                switch (e2.label) {
                  case 0:
                    return t2.getCurrentStep() > 0 ? [4, ot(t2)] : [3, 2];
                  case 1:
                    e2.sent(), e2.label = 2;
                  case 2:
                    return [2];
                }
              });
            });
          }, C(g2), g2.innerHTML = t2.getOption("prevLabel"), C(v2 = w("a", { className: "introjs-skipbutton" })), v2.innerHTML = t2.getOption("skipLabel"), v2.onclick = function() {
            return e(J2, void 0, void 0, function() {
              var e2, o3;
              return n(this, function(n2) {
                switch (n2.label) {
                  case 0:
                    return t2.isLastStep() ? [4, null === (e2 = t2.callback("complete")) || void 0 === e2 ? void 0 : e2.call(t2, t2.getCurrentStep(), "skip")] : [3, 2];
                  case 1:
                    n2.sent(), n2.label = 2;
                  case 2:
                    return [4, null === (o3 = t2.callback("skip")) || void 0 === o3 ? void 0 : o3.call(t2, t2.getCurrentStep())];
                  case 3:
                    return n2.sent(), [4, t2.exit()];
                  case 4:
                    return n2.sent(), [2];
                }
              });
            });
          }, N2.appendChild(v2), t2.getSteps().length > 1 && P2.appendChild(g2), P2.appendChild(f2), _2.appendChild(P2), I(_2, T2, o2.element, o2.position, t2.getOption("positionPrecedence"), t2.getOption("showStepNumbers"), t2.getOption("autoPosition"), null !== (s2 = o2.tooltipClass) && void 0 !== s2 ? s2 : t2.getOption("tooltipClass")), W(t2.getOption("scrollToElement"), o2.scrollTo, t2.getOption("scrollPadding"), o2.element, _2)), (F2 = a("introjs-disableInteraction", t2.getTargetElement())) && F2.parentNode && F2.parentNode.removeChild(F2), o2.disableInteraction && function(t3, e2) {
            var n2 = a("introjs-disableInteraction");
            null === n2 && (n2 = w("div", { className: "introjs-disableInteraction" }), t3.getTargetElement().appendChild(n2)), U(t3.getTargetElement(), n2, e2, t3.getOption("helperElementPadding"));
          }(t2, o2), 0 === t2.getCurrentStep() && t2.getSteps().length > 1 ? (f2 && (O(f2, t2.getOption("buttonClass"), "introjs-nextbutton"), f2.innerHTML = t2.getOption("nextLabel")), true === t2.getOption("hidePrev") ? (g2 && O(g2, t2.getOption("buttonClass"), "introjs-prevbutton", "introjs-hidden"), f2 && S(f2, "introjs-fullbutton")) : g2 && O(g2, t2.getOption("buttonClass"), "introjs-prevbutton", "introjs-disabled")) : t2.isLastStep() || 1 === t2.getSteps().length ? (g2 && O(g2, t2.getOption("buttonClass"), "introjs-prevbutton"), true === t2.getOption("hideNext") ? (f2 && O(f2, t2.getOption("buttonClass"), "introjs-nextbutton", "introjs-hidden"), g2 && S(g2, "introjs-fullbutton")) : f2 && (true === t2.getOption("nextToDone") ? (f2.innerHTML = t2.getOption("doneLabel"), S(f2, t2.getOption("buttonClass"), "introjs-nextbutton", "introjs-donebutton")) : O(f2, t2.getOption("buttonClass"), "introjs-nextbutton", "introjs-disabled"))) : (g2 && O(g2, t2.getOption("buttonClass"), "introjs-prevbutton"), f2 && (O(f2, t2.getOption("buttonClass"), "introjs-nextbutton"), f2.innerHTML = t2.getOption("nextLabel"))), g2 && g2.setAttribute("role", "button"), f2 && f2.setAttribute("role", "button"), v2 && v2.setAttribute("role", "button"), f2 && f2.focus(), function(t3) {
            S(t3, "introjs-showElement");
            var e2 = d(t3, "position");
            "absolute" !== e2 && "relative" !== e2 && "sticky" !== e2 && "fixed" !== e2 && S(t3, "introjs-relativePosition");
          }(o2.element), [4, null === (l2 = t2.callback("afterChange")) || void 0 === l2 ? void 0 : l2.call(t2, o2.element)];
        case 1:
          return V2.sent(), [2];
      }
    });
  });
}
function nt(t2) {
  var o2, i2;
  return e(this, void 0, void 0, function() {
    var e2;
    return n(this, function(n2) {
      switch (n2.label) {
        case 0:
          return t2.incrementCurrentStep(), e2 = t2.getStep(t2.getCurrentStep()), true, [4, null === (o2 = t2.callback("beforeChange")) || void 0 === o2 ? void 0 : o2.call(t2, e2 && e2.element, t2.getCurrentStep(), t2.getDirection())];
        case 1:
          return false === n2.sent() ? (t2.decrementCurrentStep(), [2, false]) : t2.isEnd() ? [4, null === (i2 = t2.callback("complete")) || void 0 === i2 ? void 0 : i2.call(t2, t2.getCurrentStep(), "end")] : [3, 4];
        case 2:
          return n2.sent(), [4, t2.exit()];
        case 3:
          return n2.sent(), [2, false];
        case 4:
          return [4, et(t2, e2)];
        case 5:
          return n2.sent(), [2, true];
      }
    });
  });
}
function ot(t2) {
  var o2;
  return e(this, void 0, void 0, function() {
    var e2;
    return n(this, function(n2) {
      switch (n2.label) {
        case 0:
          return t2.getCurrentStep() <= 0 ? [2, false] : (t2.decrementCurrentStep(), e2 = t2.getStep(t2.getCurrentStep()), true, [4, null === (o2 = t2.callback("beforeChange")) || void 0 === o2 ? void 0 : o2.call(t2, e2 && e2.element, t2.getCurrentStep(), t2.getDirection())]);
        case 1:
          return false === n2.sent() ? (t2.incrementCurrentStep(), [2, false]) : [4, et(t2, e2)];
        case 2:
          return n2.sent(), [2, true];
      }
    });
  });
}
var it = function(t2) {
  var e2, n2 = [];
  if (null === (e2 = t2.getOption("steps")) || void 0 === e2 ? void 0 : e2.length) for (var o2 = 0, l2 = t2.getOption("steps"); o2 < l2.length; o2++) {
    var c2 = i(l2[o2]);
    if (c2.step = n2.length + 1, c2.title = c2.title || "", "string" == typeof c2.element && (c2.element = r(c2.element) || void 0), !c2.element) {
      var u2 = a("introjsFloatingElement");
      u2 || (u2 = w("div", { className: "introjsFloatingElement" }), document.body.appendChild(u2)), c2.element = u2, c2.position = "floating";
    }
    c2.position = c2.position || t2.getOption("tooltipPosition"), c2.scrollTo = c2.scrollTo || t2.getOption("scrollTo"), void 0 === c2.disableInteraction && (c2.disableInteraction = t2.getOption("disableInteraction")), null !== c2.element && n2.push(c2);
  }
  else {
    var p2 = Array.from(s("*[".concat("data-intro", "]"), t2.getTargetElement()));
    if (p2.length < 1) return [];
    for (var h2 = [], d2 = 0, f2 = p2; d2 < f2.length; d2++) {
      var g2 = f2[d2];
      if ((!t2.getOption("group") || g2.getAttribute("data-intro-group") === t2.getOption("group")) && "none" !== g2.style.display) {
        var v2 = parseInt(g2.getAttribute("data-step") || "0", 10), b2 = t2.getOption("disableInteraction");
        g2.hasAttribute("data-disable-interaction") && (b2 = !!g2.getAttribute("data-disable-interaction"));
        var m2 = { step: v2, element: g2, title: g2.getAttribute("data-title") || "", intro: g2.getAttribute("data-intro") || "", tooltipClass: g2.getAttribute("data-tooltip-class") || void 0, highlightClass: g2.getAttribute("data-highlight-class") || void 0, position: g2.getAttribute("data-position") || t2.getOption("tooltipPosition"), scrollTo: g2.getAttribute("data-scroll-to") || t2.getOption("scrollTo"), disableInteraction: b2 };
        v2 > 0 ? n2[v2 - 1] = m2 : h2.push(m2);
      }
    }
    for (var y2 = 0; h2.length > 0; y2++) if (void 0 === n2[y2]) {
      var C2 = h2.shift();
      if (!C2) break;
      C2.step = y2 + 1, n2[y2] = C2;
    }
  }
  return (n2 = n2.filter(function(t3) {
    return t3;
  })).sort(function(t3, e3) {
    return t3.step - e3.step;
  }), n2;
};
var rt = function(t2) {
  return e(void 0, void 0, void 0, function() {
    var o2, i2;
    return n(this, function(r2) {
      switch (r2.label) {
        case 0:
          return t2.isActive() ? t2.hasStarted() ? [2, false] : [4, null === (i2 = t2.callback("start")) || void 0 === i2 ? void 0 : i2.call(t2, t2.getTargetElement())] : [2, false];
        case 1:
          return r2.sent(), 0 === (o2 = it(t2)).length ? [2, false] : (t2.setSteps(o2), function(t3) {
            var o3 = this, i3 = w("div", { className: "introjs-overlay" });
            y(i3, { top: 0, bottom: 0, left: 0, right: 0, position: "fixed" }), t3.getTargetElement().appendChild(i3), true === t3.getOption("exitOnOverlayClick") && (y(i3, { cursor: "pointer" }), i3.onclick = function() {
              return e(o3, void 0, void 0, function() {
                return n(this, function(e2) {
                  switch (e2.label) {
                    case 0:
                      return [4, t3.exit()];
                    case 1:
                      return e2.sent(), [2];
                  }
                });
              });
            });
          }(t2), [4, nt(t2)]);
        case 2:
          return r2.sent(), [2, true];
        case 3:
          return [2, false];
      }
    });
  });
};
var st = function(t2) {
  t2 && t2.parentElement && t2.parentElement.removeChild(t2);
};
function at(t2, o2) {
  var i2, r2;
  return void 0 === o2 && (o2 = false), e(this, void 0, void 0, function() {
    var s2, c2, u2, p2, h2, d2, f2, g2, v2, b2;
    return n(this, function(m2) {
      switch (m2.label) {
        case 0:
          return s2 = t2.getTargetElement(), c2 = true, [4, null === (i2 = t2.callback("beforeExit")) || void 0 === i2 ? void 0 : i2.call(t2, s2)];
        case 1:
          if (c2 = m2.sent(), !o2 && false === c2) return [2, false];
          if ((u2 = Array.from(l("introjs-overlay", s2))) && u2.length) for (p2 = 0, h2 = u2; p2 < h2.length; p2++) d2 = h2[p2], st(d2);
          return f2 = a("introjs-tooltipReferenceLayer", s2), st(f2), g2 = a("introjs-disableInteraction", s2), st(g2), v2 = a("introjsFloatingElement", s2), st(v2), Q(), b2 = a("introjs-helperLayer", s2), [4, (w2 = b2, e(void 0, void 0, void 0, function() {
            return n(this, function(t3) {
              return w2 ? (y(w2, { opacity: "0" }), [2, new Promise(function(t4) {
                setTimeout(function() {
                  try {
                    st(w2);
                  } catch (t5) {
                  } finally {
                    t4();
                  }
                }, 500);
              })]) : [2];
            });
          }))];
        case 2:
          return m2.sent(), [4, null === (r2 = t2.callback("exit")) || void 0 === r2 ? void 0 : r2.call(t2)];
        case 3:
          return m2.sent(), t2.setCurrentStep(-1), [2, true];
      }
      var w2;
    });
  });
}
function lt(t2, e2, n2) {
  var o2, i2 = ((o2 = {})[t2] = e2, o2.path = "/", o2.expires = void 0, o2);
  if (n2) {
    var r2 = /* @__PURE__ */ new Date();
    r2.setTime(r2.getTime() + 24 * n2 * 60 * 60 * 1e3), i2.expires = r2.toUTCString();
  }
  var s2 = [];
  for (var a2 in i2) s2.push("".concat(a2, "=").concat(i2[a2]));
  return document.cookie = s2.join("; "), ct(t2);
}
function ct(t2) {
  return (e2 = {}, document.cookie.split(";").forEach(function(t3) {
    var n2 = t3.split("="), o2 = n2[0], i2 = n2[1];
    e2[o2.trim()] = i2;
  }), e2)[t2];
  var e2;
}
function ut(t2, e2, n2) {
  t2 ? lt(e2, "true", n2) : lt(e2, "", -1);
}
function pt(t2, e2) {
  var n2, o2 = t2.getCurrentStep();
  if (null != o2 && -1 != o2) {
    var i2 = t2.getStep(o2), r2 = c("introjs-tooltipReferenceLayer"), s2 = c("introjs-helperLayer"), l2 = a("introjs-disableInteraction"), u2 = t2.getTargetElement(), p2 = t2.getOption("helperElementPadding");
    U(u2, s2, i2, p2), U(u2, r2, i2, p2), l2 && U(u2, l2, i2, p2), e2 && (t2.setSteps(it(t2)), function(t3, e3) {
      if (t3.getOption("showBullets")) {
        var n3 = a("introjs-bullets");
        n3 && n3.parentNode && n3.parentNode.replaceChild($(t3, e3), n3);
      }
    }(t2, i2), tt(r2, o2, t2.getSteps().length));
    var h2 = document.querySelector(".introjs-arrow"), d2 = document.querySelector(".introjs-tooltip");
    return d2 && h2 && I(d2, h2, i2.element, i2.position, t2.getOption("positionPrecedence"), t2.getOption("showStepNumbers"), t2.getOption("autoPosition"), null !== (n2 = i2.tooltipClass) && void 0 !== n2 ? n2 : t2.getOption("tooltipClass")), t2;
  }
}
var ht = function() {
  function t2(t3, e2) {
    this._steps = [], this._currentStep = -1, this.callbacks = {}, this._targetElement = m(t3), this._options = e2 ? p(this._options, e2) : { steps: [], isActive: true, nextLabel: "Next", prevLabel: "Back", skipLabel: "×", doneLabel: "Done", hidePrev: false, hideNext: false, nextToDone: true, tooltipPosition: "bottom", tooltipClass: "", group: "", highlightClass: "", exitOnEsc: true, exitOnOverlayClick: true, showStepNumbers: false, stepNumbersOfLabel: "of", keyboardNavigation: true, showButtons: true, showBullets: true, showProgress: false, scrollToElement: true, scrollTo: "element", scrollPadding: 30, overlayOpacity: 0.5, autoPosition: true, positionPrecedence: ["bottom", "top", "right", "left"], disableInteraction: false, dontShowAgain: false, dontShowAgainLabel: "Don't show this again", dontShowAgainCookie: "introjs-dontShowAgain", dontShowAgainCookieDays: 365, helperElementPadding: 10, buttonClass: "introjs-button", progressBarAdditionalClass: "" };
  }
  return t2.prototype.callback = function(t3) {
    var e2 = this.callbacks[t3];
    if (h(e2)) return e2;
  }, t2.prototype.goToStep = function(t3) {
    return e(this, void 0, void 0, function() {
      return n(this, function(e2) {
        switch (e2.label) {
          case 0:
            return this.setCurrentStep(t3 - 2), [4, nt(this)];
          case 1:
            return e2.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.goToStepNumber = function(t3) {
    return e(this, void 0, void 0, function() {
      var e2;
      return n(this, function(n2) {
        switch (n2.label) {
          case 0:
            for (e2 = 0; e2 < this._steps.length; e2++) if (this._steps[e2].step === t3) {
              this.setCurrentStep(e2 - 1);
              break;
            }
            return [4, nt(this)];
          case 1:
            return n2.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.addStep = function(t3) {
    return this._options.steps || (this._options.steps = []), this._options.steps.push(t3), this;
  }, t2.prototype.addSteps = function(t3) {
    if (!t3.length) return this;
    for (var e2 = 0, n2 = t3; e2 < n2.length; e2++) {
      var o2 = n2[e2];
      this.addStep(o2);
    }
    return this;
  }, t2.prototype.setSteps = function(t3) {
    return this._steps = t3, this;
  }, t2.prototype.getSteps = function() {
    return this._steps;
  }, t2.prototype.getStep = function(t3) {
    return this._steps[t3];
  }, t2.prototype.getCurrentStep = function() {
    return this._currentStep;
  }, t2.prototype.currentStep = function() {
    return this._currentStep;
  }, t2.prototype.setCurrentStep = function(t3) {
    return t3 >= this._currentStep ? this._direction = "forward" : this._direction = "backward", this._currentStep = t3, this;
  }, t2.prototype.incrementCurrentStep = function() {
    return -1 === this.getCurrentStep() ? this.setCurrentStep(0) : this.setCurrentStep(this.getCurrentStep() + 1), this;
  }, t2.prototype.decrementCurrentStep = function() {
    return this.getCurrentStep() > 0 && this.setCurrentStep(this._currentStep - 1), this;
  }, t2.prototype.getDirection = function() {
    return this._direction;
  }, t2.prototype.nextStep = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        switch (t3.label) {
          case 0:
            return [4, nt(this)];
          case 1:
            return t3.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.previousStep = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        switch (t3.label) {
          case 0:
            return [4, ot(this)];
          case 1:
            return t3.sent(), [2, this];
        }
      });
    });
  }, t2.prototype.isEnd = function() {
    return this.getCurrentStep() >= this._steps.length;
  }, t2.prototype.isLastStep = function() {
    return this.getCurrentStep() === this._steps.length - 1;
  }, t2.prototype.getTargetElement = function() {
    return this._targetElement;
  }, t2.prototype.setOptions = function(t3) {
    return this._options = p(this._options, t3), this;
  }, t2.prototype.setOption = function(t3, e2) {
    return this._options = u(this._options, t3, e2), this;
  }, t2.prototype.getOption = function(t3) {
    return this._options[t3];
  }, t2.prototype.clone = function() {
    return new t2(this._targetElement, this._options);
  }, t2.prototype.isActive = function() {
    return (!this.getOption("dontShowAgain") || (t3 = this.getOption("dontShowAgainCookie"), "" === (e2 = ct(t3)) || "true" !== e2)) && this.getOption("isActive");
    var t3, e2;
  }, t2.prototype.hasStarted = function() {
    return this.getCurrentStep() > -1;
  }, t2.prototype.setDontShowAgain = function(t3) {
    return ut(t3, this.getOption("dontShowAgainCookie"), this.getOption("dontShowAgainCookieDays")), this;
  }, t2.prototype.enableKeyboardNavigation = function() {
    var t3 = this;
    return this.getOption("keyboardNavigation") && (this._keyboardNavigationHandler = function(o2) {
      return function(t4, o3) {
        var i2;
        return e(this, void 0, void 0, function() {
          var e2, r2;
          return n(this, function(n2) {
            switch (n2.label) {
              case 0:
                return null === (e2 = void 0 === o3.code ? o3.which : o3.code) && (e2 = null === o3.charCode ? o3.keyCode : o3.charCode), "Escape" !== e2 && 27 !== e2 || true !== t4.getOption("exitOnEsc") ? [3, 2] : [4, t4.exit()];
              case 1:
                return n2.sent(), [3, 16];
              case 2:
                return "ArrowLeft" !== e2 && 37 !== e2 ? [3, 4] : [4, ot(t4)];
              case 3:
                return n2.sent(), [3, 16];
              case 4:
                return "ArrowRight" !== e2 && 39 !== e2 ? [3, 6] : [4, nt(t4)];
              case 5:
                return n2.sent(), [3, 16];
              case 6:
                return "Enter" !== e2 && "NumpadEnter" !== e2 && 13 !== e2 ? [3, 16] : (r2 = o3.target || o3.srcElement) && r2.className.match("introjs-prevbutton") ? [4, ot(t4)] : [3, 8];
              case 7:
                return n2.sent(), [3, 15];
              case 8:
                return r2 && r2.className.match("introjs-skipbutton") ? t4.isEnd() ? [4, null === (i2 = t4.callback("complete")) || void 0 === i2 ? void 0 : i2.call(t4, t4.getCurrentStep(), "skip")] : [3, 10] : [3, 12];
              case 9:
                n2.sent(), n2.label = 10;
              case 10:
                return [4, t4.exit()];
              case 11:
                return n2.sent(), [3, 15];
              case 12:
                return r2 && r2.getAttribute("data-step-number") ? (r2.click(), [3, 15]) : [3, 13];
              case 13:
                return [4, nt(t4)];
              case 14:
                n2.sent(), n2.label = 15;
              case 15:
                o3.preventDefault ? o3.preventDefault() : o3.returnValue = false, n2.label = 16;
              case 16:
                return [2];
            }
          });
        });
      }(t3, o2);
    }, b.on(window, "keydown", this._keyboardNavigationHandler, true)), this;
  }, t2.prototype.disableKeyboardNavigation = function() {
    return this._keyboardNavigationHandler && (b.off(window, "keydown", this._keyboardNavigationHandler, true), this._keyboardNavigationHandler = void 0), this;
  }, t2.prototype.enableRefreshOnResize = function() {
    var t3 = this;
    this._refreshOnResizeHandler = function(e2) {
      pt(t3);
    }, b.on(window, "resize", this._refreshOnResizeHandler, true);
  }, t2.prototype.disableRefreshOnResize = function() {
    this._refreshOnResizeHandler && (b.off(window, "resize", this._refreshOnResizeHandler, true), this._refreshOnResizeHandler = void 0);
  }, t2.prototype.start = function() {
    return e(this, void 0, void 0, function() {
      return n(this, function(t3) {
        switch (t3.label) {
          case 0:
            return [4, rt(this)];
          case 1:
            return t3.sent() && (this.enableKeyboardNavigation(), this.enableRefreshOnResize()), [2, this];
        }
      });
    });
  }, t2.prototype.exit = function(t3) {
    return e(this, void 0, void 0, function() {
      return n(this, function(e2) {
        switch (e2.label) {
          case 0:
            return [4, at(this, null != t3 && t3)];
          case 1:
            return e2.sent() && (this.disableKeyboardNavigation(), this.disableRefreshOnResize()), [2, this];
        }
      });
    });
  }, t2.prototype.refresh = function(t3) {
    return pt(this, t3), this;
  }, t2.prototype.onbeforechange = function(t3) {
    return this.onBeforeChange(t3);
  }, t2.prototype.onBeforeChange = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onBeforeChange was not a function");
    return this.callbacks.beforeChange = t3, this;
  }, t2.prototype.onchange = function(t3) {
    this.onChange(t3);
  }, t2.prototype.onChange = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onChange was not a function.");
    return this.callbacks.change = t3, this;
  }, t2.prototype.onafterchange = function(t3) {
    this.onAfterChange(t3);
  }, t2.prototype.onAfterChange = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onAfterChange was not a function");
    return this.callbacks.afterChange = t3, this;
  }, t2.prototype.oncomplete = function(t3) {
    return this.onComplete(t3);
  }, t2.prototype.onComplete = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for oncomplete was not a function.");
    return this.callbacks.complete = t3, this;
  }, t2.prototype.onstart = function(t3) {
    return this.onStart(t3);
  }, t2.prototype.onStart = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onstart was not a function.");
    return this.callbacks.start = t3, this;
  }, t2.prototype.onexit = function(t3) {
    return this.onExit(t3);
  }, t2.prototype.onExit = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onexit was not a function.");
    return this.callbacks.exit = t3, this;
  }, t2.prototype.onskip = function(t3) {
    return this.onSkip(t3);
  }, t2.prototype.onSkip = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onskip was not a function.");
    return this.callbacks.skip = t3, this;
  }, t2.prototype.onbeforeexit = function(t3) {
    return this.onBeforeExit(t3);
  }, t2.prototype.onBeforeExit = function(t3) {
    if (!h(t3)) throw new Error("Provided callback for onbeforeexit was not a function.");
    return this.callbacks.beforeExit = t3, this;
  }, t2;
}();
var dt = function(e2) {
  function n2() {
    return null !== e2 && e2.apply(this, arguments) || this;
  }
  return function(e3, n3) {
    if ("function" != typeof n3 && null !== n3) throw new TypeError("Class extends value " + String(n3) + " is not a constructor or null");
    function o2() {
      this.constructor = e3;
    }
    t(e3, n3), e3.prototype = null === n3 ? Object.create(n3) : (o2.prototype = n3.prototype, new o2());
  }(n2, e2), n2.prototype.addHints = function() {
    console.error("introJs().addHints() is deprecated, please use introJs.hint.addHints() instead.");
  }, n2.prototype.addHint = function() {
    console.error("introJs().addHint() is deprecated, please use introJs.hint.addHint() instead.");
  }, n2.prototype.removeHints = function() {
    console.error("introJs().removeHints() is deprecated, please use introJs.hint.removeHints() instead.");
  }, n2;
}(ht);
var ft = function(t2) {
  return console.warn("introJs() is deprecated. Please use introJs.tour() or introJs.hint() instead."), new dt(t2);
};
ft.tour = function(t2) {
  return new ht(t2);
}, ft.hint = function(t2) {
  return new q(t2);
}, ft.version = "8.0.0-beta.1";
export {
  ft as default
};
/*! Bundled license information:

intro.js/intro.module.js:
  (*!
   * Intro.js v8.0.0-beta.1
   * https://introjs.com
   *
   * Copyright (C) 2012-2024 Afshin Mehrabani (@afshinmeh).
   * https://introjs.com
   *
   * Date: Sun, 21 Jul 2024 11:55:52 GMT
   *)
*/
//# sourceMappingURL=intro__js.js.map
