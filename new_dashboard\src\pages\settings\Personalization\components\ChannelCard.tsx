"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

interface ChannelKPIConfig {
  primaryKPI: string;
  secondaryKPI: string;
  tertiaryKPI: string;
  weighting: {
    primary: number;
    secondary: number;
    tertiary: number;
  };
  benchmarks: Record<string, any>[];
  channelPersona?: string;
}

interface ChannelCardProps {
  channelId: string;
  config: ChannelKPIConfig;
}

const ChannelCard = ({ channelId, config }: ChannelCardProps) => {
  const headers = config.benchmarks?.length ? Object.keys(config.benchmarks[0]) : [];

  return (
  <Card className="w-full border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 bg-white">
  <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between pb-2">
    <div>
      <CardTitle className="text-lg capitalize text-gray-900 font-semibold">
        {channelId.replace(/([A-Z])/g, " $1")}
      </CardTitle>
      <CardDescription className="text-sm text-gray-600">
        Channel-specific KPIs, Weighting, Persona, and Benchmarks.
      </CardDescription>
    </div>
    <Badge
      variant="secondary"
      className="bg-purple-50 text-purple-700 border border-purple-200 mt-3 sm:mt-0"
    >
      {config.benchmarks?.length || 0} Benchmarks
    </Badge>
  </CardHeader>

  {/* Add scrollable container here */}
  <CardContent className="max-h-[400px] overflow-y-auto space-y-5 pt-2 pr-2 custom-scrollbar">
    {/* KPIs */}
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-y-2 text-sm text-gray-800">
      <p><strong>Primary KPI:</strong> {config.primaryKPI}</p>
      <p><strong>Secondary KPI:</strong> {config.secondaryKPI}</p>
      <p><strong>Tertiary KPI:</strong> {config.tertiaryKPI}</p>
      <p><strong>Weighting:</strong> {`${config.weighting.primary}/${config.weighting.secondary}/${config.weighting.tertiary}`}</p>
    </div>

    {/* Channel Persona */}
    {config.channelPersona && (
      <div>
        <Separator className="my-3" />
        <h4 className="font-semibold text-gray-900 mb-1">Channel Persona</h4>
        <p className="text-gray-700 bg-gray-50 border border-gray-100 rounded-md p-3">
          {config.channelPersona}
        </p>
      </div>
    )}

    {/* Benchmarks Table */}
    <div>
      <Separator className="my-3" />
      <h4 className="font-semibold text-gray-900 mb-2">Benchmarks</h4>
      {config.benchmarks?.length ? (
        <div className="overflow-x-auto border border-gray-100 rounded-md bg-gray-50">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-100">
                {Object.keys(config.benchmarks[0]).map((h) => (
                  <TableHead
                    key={h}
                    className="uppercase text-xs font-semibold text-gray-600 tracking-wider"
                  >
                    {h}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {config.benchmarks.map((b, i) => (
                <TableRow key={i}>
                  {Object.keys(b).map((h) => (
                    <TableCell key={h} className="text-sm text-gray-800">
                      {b[h] ?? "—"}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <p className="text-sm text-gray-500 mt-2">
          No benchmark data available.
        </p>
      )}
    </div>
  </CardContent>
</Card>


  );
};

export default ChannelCard;
