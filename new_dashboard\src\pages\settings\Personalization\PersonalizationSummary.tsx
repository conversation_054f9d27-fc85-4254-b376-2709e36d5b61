"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import ChannelCard from "./components/ChannelCard";

interface ChannelKPIConfig {
  primaryKPI: string;
  secondaryKPI: string;
  tertiaryKPI: string;
  weighting: { primary: number; secondary: number; tertiary: number };
  benchmarks: Record<string, any>[];
  channelPersona?: string;
}

interface PersonalizationFormData {
  agentPersona?: string;
  selectedChannels: string[];
  channelConfigs: Record<string, ChannelKPIConfig>;
}

interface PersonalizationSummaryProps {
  data: PersonalizationFormData;
  onEdit: () => void;
}

const PersonalizationSummary = ({ data, onEdit }: PersonalizationSummaryProps) => {
  if (!data || !data.selectedChannels?.length)
    return (
      <div className="text-center py-12 text-gray-500">
        No personalization data found.
      </div>
    );

  return (
    <div className="w-3/4 mx-auto py-10 space-y-8">
      {/* Header Section with <PERSON><PERSON> aligned right */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-3xl font-semibold text-gray-900">
            Your AI CMO Personalization
          </h1>
          <p className="text-gray-600 mt-1">
            Here’s your AI CMO setup summary — including your persona and
            channel-specific KPIs and benchmarks.
          </p>
        </div>

        <Button
          onClick={onEdit}
          className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md shadow-sm mt-2"
        >
          Edit Personalization
        </Button>
      </div>

      {/* Agent Persona Card */}
      {data.agentPersona && (
        <Card className="border border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-xl text-purple-700 font-semibold">
              AI CMO Persona
            </CardTitle>
            <CardDescription>
              Defines your AI CMO’s tone, behavior, and focus.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="bg-gray-50 text-gray-800 rounded-md p-4 border border-gray-100 leading-relaxed">
              {data.agentPersona}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Channels Section */}
      <div className="space-y-6">
        {Object.entries(data.channelConfigs || {}).map(([channelId, config]) => (
          <ChannelCard key={channelId} channelId={channelId} config={config} />
        ))}
      </div>
    </div>
  );
};

export default PersonalizationSummary;
