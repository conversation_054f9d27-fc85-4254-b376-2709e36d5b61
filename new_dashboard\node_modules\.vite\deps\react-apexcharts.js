import {
  apexcharts_esm_exports,
  init_apexcharts_esm
} from "./chunk-4UF7UCSS.js";
import {
  require_prop_types
} from "./chunk-3KYMYWYK.js";
import {
  require_react
} from "./chunk-CMM6OKGN.js";
import {
  __commonJS,
  __toCommonJS
} from "./chunk-OL46QLBJ.js";

// node_modules/react-apexcharts/dist/react-apexcharts.min.js
var require_react_apexcharts_min = __commonJS({
  "node_modules/react-apexcharts/dist/react-apexcharts.min.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true }), exports.default = Charts;
    var _react = _interopRequireWildcard(require_react());
    var _apexcharts = _interopRequireDefault((init_apexcharts_esm(), __toCommonJS(apexcharts_esm_exports)));
    var _propTypes = _interopRequireDefault(require_prop_types());
    var _excluded = ["type", "width", "height", "series", "options"];
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : { default: e };
    }
    function _getRequireWildcardCache(e) {
      var t, r;
      return "function" != typeof WeakMap ? null : (t = /* @__PURE__ */ new WeakMap(), r = /* @__PURE__ */ new WeakMap(), (_getRequireWildcardCache = function(e2) {
        return e2 ? r : t;
      })(e));
    }
    function _interopRequireWildcard(e, t) {
      if (!t && e && e.__esModule) return e;
      if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e };
      t = _getRequireWildcardCache(t);
      if (t && t.has(e)) return t.get(e);
      var r, n, o = { __proto__: null }, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
      for (r in e) "default" !== r && {}.hasOwnProperty.call(e, r) && ((n = i ? Object.getOwnPropertyDescriptor(e, r) : null) && (n.get || n.set) ? Object.defineProperty(o, r, n) : o[r] = e[r]);
      return o.default = e, t && t.set(e, o), o;
    }
    function _extends() {
      return (_extends = Object.assign ? Object.assign.bind() : function(e) {
        for (var t = 1; t < arguments.length; t++) {
          var r, n = arguments[t];
          for (r in n) !{}.hasOwnProperty.call(n, r) || (e[r] = n[r]);
        }
        return e;
      }).apply(null, arguments);
    }
    function _objectWithoutProperties(e, t) {
      if (null == e) return {};
      var r, n = _objectWithoutPropertiesLoose(e, t);
      if (Object.getOwnPropertySymbols) for (var o = Object.getOwnPropertySymbols(e), i = 0; i < o.length; i++) r = o[i], t.includes(r) || {}.propertyIsEnumerable.call(e, r) && (n[r] = e[r]);
      return n;
    }
    function _objectWithoutPropertiesLoose(e, t) {
      if (null == e) return {};
      var r, n = {};
      for (r in e) if ({}.hasOwnProperty.call(e, r)) {
        if (t.includes(r)) continue;
        n[r] = e[r];
      }
      return n;
    }
    function _typeof(e) {
      return (_typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e2) {
        return typeof e2;
      } : function(e2) {
        return e2 && "function" == typeof Symbol && e2.constructor === Symbol && e2 !== Symbol.prototype ? "symbol" : typeof e2;
      })(e);
    }
    function ownKeys(t, e) {
      var r, n = Object.keys(t);
      return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function(e2) {
        return Object.getOwnPropertyDescriptor(t, e2).enumerable;
      })), n.push.apply(n, r)), n;
    }
    function _objectSpread(t) {
      for (var e = 1; e < arguments.length; e++) {
        var r = null != arguments[e] ? arguments[e] : {};
        e % 2 ? ownKeys(Object(r), true).forEach(function(e2) {
          _defineProperty(t, e2, r[e2]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : ownKeys(Object(r)).forEach(function(e2) {
          Object.defineProperty(t, e2, Object.getOwnPropertyDescriptor(r, e2));
        });
      }
      return t;
    }
    function _defineProperty(e, t, r) {
      return (t = _toPropertyKey(t)) in e ? Object.defineProperty(e, t, { value: r, enumerable: true, configurable: true, writable: true }) : e[t] = r, e;
    }
    function _toPropertyKey(e) {
      e = _toPrimitive(e, "string");
      return "symbol" == _typeof(e) ? e : e + "";
    }
    function _toPrimitive(e, t) {
      if ("object" != _typeof(e) || !e) return e;
      var r = e[Symbol.toPrimitive];
      if (void 0 === r) return ("string" === t ? String : Number)(e);
      r = r.call(e, t || "default");
      if ("object" != _typeof(r)) return r;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    function omit(e, t) {
      var r = _objectSpread({}, e);
      return t.forEach(function(e2) {
        delete r[e2];
      }), r;
    }
    function deepEqual(e, t) {
      var r = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : /* @__PURE__ */ new WeakSet();
      if (e !== t) {
        if ("object" !== _typeof(e) || null === e || "object" !== _typeof(t) || null === t) return false;
        if (!r.has(e) && !r.has(t)) {
          r.add(e), r.add(t);
          var n = Object.keys(e), o = Object.keys(t);
          if (n.length !== o.length) return false;
          for (var i = 0, u = n; i < u.length; i++) {
            var c = u[i];
            if (!o.includes(c) || !deepEqual(e[c], t[c], r)) return false;
          }
        }
      }
      return true;
    }
    function Charts(e) {
      function o(e2) {
        return e2 && "object" === _typeof(e2) && !Array.isArray(e2);
      }
      var t = e.type, r = void 0 === t ? "line" : t, t = e.width, n = void 0 === t ? "100%" : t, t = e.height, i = void 0 === t ? "auto" : t, u = e.series, c = e.options, t = _objectWithoutProperties(e, _excluded), p = (0, _react.useRef)(null), a = (0, _react.useRef)(null), f = (0, _react.useRef)(), s = ((0, _react.useEffect)(function() {
        f.current = c;
        var e2 = p.current;
        return a.current = new _apexcharts.default(e2, s()), a.current.render(), function() {
          a.current && "function" == typeof a.current.destroy && a.current.destroy();
        };
      }, []), (0, _react.useEffect)(function() {
        var e2 = !deepEqual(a.current.w.config.series, u), t2 = !deepEqual(f.current, c) || i !== a.current.opts.chart.height || n !== a.current.opts.chart.width;
        (e2 || t2) && (!e2 || t2 ? a.current.updateOptions(s()) : a.current.updateSeries(u)), f.current = c;
      }, [c, u, i, n]), function() {
        return l(c, { chart: { type: r, height: i, width: n }, series: u });
      }), l = function(t2, r2) {
        var n2 = _objectSpread({}, t2);
        return o(t2) && o(r2) && Object.keys(r2).forEach(function(e2) {
          o(r2[e2]) && e2 in t2 ? n2[e2] = l(t2[e2], r2[e2]) : Object.assign(n2, _defineProperty({}, e2, r2[e2]));
        }), n2;
      }, e = omit(t, Object.keys(Charts.propTypes));
      return _react.default.createElement("div", _extends({ ref: p }, e));
    }
    Charts.propTypes = { type: _propTypes.default.string.isRequired, series: _propTypes.default.array.isRequired, options: _propTypes.default.object.isRequired, width: _propTypes.default.oneOfType([_propTypes.default.string, _propTypes.default.number]), height: _propTypes.default.oneOfType([_propTypes.default.string, _propTypes.default.number]) };
  }
});
export default require_react_apexcharts_min();
//# sourceMappingURL=react-apexcharts.js.map
