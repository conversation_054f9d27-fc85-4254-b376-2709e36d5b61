import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { KPIMeta } from '@/pages/dashboard/utils/interface';

interface ChannelKPIConfig {
  primaryKPI: string;
  secondaryKPI: string;
  tertiaryKPI?: string;
}

interface KPISelectorProps {
  config: ChannelKPIConfig;
  kpiMeta: KPIMeta[];
  activeChannel: string;
  onChange: (update: Partial<ChannelKPIConfig>) => void;
}

export default function KPISelector({
  config,
  kpiMeta,
  activeChannel,
  onChange,
}: KPISelectorProps) {

  


  const filteredKPIs = kpiMeta.filter(
    (kpi) => kpi.category === activeChannel
  );

  const handleChange = (key: keyof ChannelKPIConfig, value: string) => {
    onChange({ [key]: value });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pb-8">
      
      <div className="space-y-2">
        <Label className="text-sm font-medium text-gray-700">Primary KPI</Label>
        <Select
          value={config.primaryKPI}
          onValueChange={(value) => handleChange("primaryKPI", value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select KPI" />
          </SelectTrigger>

          <SelectContent className="max-h-[180px] overflow-y-auto bg-white dark:bg-neutral-900 border border-gray-200 dark:border-gray-800 shadow-md">
            {filteredKPIs.map((item) => (
              <SelectItem key={item.kpi} value={item.kpi}>
                {item.kpi_display_name || item.kpi}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      
      <div className="space-y-2">
        <Label className="text-sm font-medium text-gray-700">Secondary KPI</Label>
        <Select
          value={config.secondaryKPI}
          onValueChange={(value) => handleChange("secondaryKPI", value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select KPI" />
          </SelectTrigger>

          <SelectContent className="max-h-[180px] overflow-y-auto bg-white dark:bg-neutral-900 border border-gray-200 dark:border-gray-800 shadow-md">
            {filteredKPIs.map((item) => (
              <SelectItem key={item.kpi} value={item.kpi}>
                {item.kpi_display_name || item.kpi}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium text-gray-700">Tertiary KPI</Label>
        <Select
          value={config.tertiaryKPI || ""}
          onValueChange={(value) => handleChange("tertiaryKPI", value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select KPI" />
          </SelectTrigger>

          <SelectContent className="max-h-[180px] overflow-y-auto bg-white dark:bg-neutral-900 border border-gray-200 dark:border-gray-800 shadow-md">
            {filteredKPIs.map((item) => (
              <SelectItem key={item.kpi} value={item.kpi}>
                {item.kpi_display_name || item.kpi}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
