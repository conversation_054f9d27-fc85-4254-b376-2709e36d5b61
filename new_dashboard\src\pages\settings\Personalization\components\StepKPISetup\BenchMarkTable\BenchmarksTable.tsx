"use client";
import { useState } from "react";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import TableBodyRow from "./TableBodyRow";
import { KPIBenchmark } from "../../../utils/types";
import {headersByChannel } from "../../../utils/channels";
import { KPIMeta } from '@/pages/dashboard/utils/interface';
import { MdOutlineEventRepeat } from "react-icons/md";
import { RiPlayListAddLine } from "react-icons/ri";
import { TbTablePlus } from "react-icons/tb";

interface BenchmarksTableProps {
  channelId: string;
  benchmarks: KPIBenchmark[];
  kpiMeta: KPIMeta[];
  onChange: (benchmarks: KPIBenchmark[]) => void;
}

const BenchmarksTable = ({ channelId, benchmarks,kpiMeta, onChange }: BenchmarksTableProps) => {

 
  const [extraColumns, setExtraColumns] = useState<string[]>([]);
 const availableKPIs = kpiMeta.filter((kpi) => kpi.category === channelId).map((kpi) => kpi.kpi_display_name);
  const headers = [...(headersByChannel[channelId] ?? []), ...extraColumns];

  const addMonth = () => {
    onChange([...benchmarks, { month: "", spend: 0 }]);
  };

  const replicateCurrentMonth = () => {
    if (benchmarks.length === 0) return;
    const last = benchmarks[benchmarks.length - 1];
    const newMonth = { ...last, month: "" };
    onChange([...benchmarks, newMonth]);
  };

  const handleCellChange = (i: number, key: string, value: string) => {
    const updated = [...benchmarks];
    updated[i] = { ...benchmarks[i], [key.toLowerCase().replace(/\s/g, "")]: value };
    onChange(updated);
  };

  const handleDeleteRow = (i: number) => {
    const updated = benchmarks.filter((_, idx) => idx !== i);
    onChange(updated);
  };

  const handleAddColumn = (newColumn: string) => {
    if (!extraColumns.includes(newColumn)) setExtraColumns([...extraColumns, newColumn]);
  };

  return (
    <div className="rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm bg-white dark:bg-neutral-900 transition-all">
      <div className="overflow-x-auto">
        <Table>
        <TableHeader>
          <TableRow className="bg-gray-50 dark:bg-neutral-800/60">
            {headers.map((h) => (
              <TableHead key={h} className="text-gray-700 dark:text-gray-300 font-medium">
                {h}
              </TableHead>
            ))}
            
            <TableHead className="w-[180px]">
              <Select onValueChange={handleAddColumn}>
                <SelectTrigger className="h-8 text-sm border-dashed border-gray-400 dark:border-gray-600">
             
  <TbTablePlus className="text-gray-600 dark:text-gray-300 h-5 w-5" />
</SelectTrigger>

                
                <SelectContent className="max-h-[180px] overflow-y-auto bg-white dark:bg-neutral-900">
                  {availableKPIs.map((kpi) => (
                    <SelectItem key={kpi} value={kpi}>
                      {kpi}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {benchmarks.length > 0 ? (
            benchmarks.map((row, i) => (
              <TableBodyRow
                key={i}
                row={row}
                headers={headers}
                index={i}
                onChange={handleCellChange}
                onDelete={handleDeleteRow}
              />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={headers.length + 2} className="text-center text-gray-500 text-sm py-6">
                No benchmark data available
              </TableCell>
            </TableRow>
          )}

          <TableRow>
            <TableCell colSpan={headers.length + 2} className="text-center py-4 bg-gray-50 dark:bg-neutral-800/60">
              <div className="flex justify-center gap-3">
                <Button
                  onClick={addMonth}
                  variant="ghost"
                  size="sm"
                  className="text-[#7F56D9] hover:bg-[#7F56D9]/10 flex items-center gap-1"
                >
                  <RiPlayListAddLine className="h-4 w-4" />
                  Add Month
                </Button>
                <Button
                  onClick={replicateCurrentMonth}
                  variant="ghost"
                  size="sm"
                  className="text-[#7F56D9]  hover:bg-[#7F56D9]/10 flex items-center gap-1"
                >
                  <MdOutlineEventRepeat className="h-4 w-4" />
                  Replicate Current Month
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      </div>
    </div>
  );
};

export default BenchmarksTable;
