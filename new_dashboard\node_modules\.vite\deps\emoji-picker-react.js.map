{"version": 3, "sources": ["../../flairup/src/utils/asArray.ts", "../../flairup/src/utils/is.ts", "../../flairup/src/utils/joinTruthy.ts", "../../flairup/src/utils/stableHash.ts", "../../flairup/src/utils/stringManipulators.ts", "../../flairup/src/Rule.ts", "../../flairup/src/Sheet.ts", "../../flairup/src/utils/forIn.ts", "../../flairup/src/cx.ts", "../../flairup/src/index.ts", "../../emoji-picker-react/src/DomUtils/classNames.ts", "../../emoji-picker-react/src/Stylesheet/stylesheet.tsx", "../../emoji-picker-react/src/config/compareConfig.ts", "../../emoji-picker-react/src/components/Reactions/DEFAULT_REACTIONS.ts", "../../emoji-picker-react/src/types/exposedTypes.ts", "../../emoji-picker-react/src/config/categoryConfig.ts", "../../emoji-picker-react/src/config/cdnUrls.ts", "../../emoji-picker-react/src/data/skinToneVariations.ts", "../../emoji-picker-react/src/dataUtils/DataTypes.ts", "../../emoji-picker-react/src/dataUtils/alphaNumericEmojiIndex.ts", "../../emoji-picker-react/src/dataUtils/emojiSelectors.ts", "../../emoji-picker-react/src/config/config.ts", "../../emoji-picker-react/src/components/context/PickerConfigContext.tsx", "../../emoji-picker-react/src/config/mutableConfig.ts", "../../emoji-picker-react/src/config/useConfig.ts", "../../emoji-picker-react/src/hooks/useDebouncedState.ts", "../../emoji-picker-react/src/hooks/useHideEmojisByUniocode.ts", "../../emoji-picker-react/src/hooks/useDisallowedEmojis.ts", "../../emoji-picker-react/src/hooks/useInitialLoad.ts", "../../emoji-picker-react/src/components/context/PickerContext.tsx", "../../emoji-picker-react/src/hooks/useIsSearchMode.ts", "../../emoji-picker-react/src/DomUtils/focusElement.ts", "../../emoji-picker-react/src/DomUtils/getActiveElement.ts", "../../emoji-picker-react/src/components/context/ElementRefContext.tsx", "../../emoji-picker-react/src/DomUtils/scrollTo.ts", "../../emoji-picker-react/src/DomUtils/keyboardNavigation.ts", "../../emoji-picker-react/src/hooks/useCloseAllOpenToggles.ts", "../../emoji-picker-react/src/hooks/useDisallowMouseMove.ts", "../../emoji-picker-react/src/hooks/useFocus.ts", "../../emoji-picker-react/src/hooks/useFilter.ts", "../../emoji-picker-react/src/hooks/useSetVariationPicker.ts", "../../emoji-picker-react/src/hooks/useShouldShowSkinTonePicker.ts", "../../emoji-picker-react/src/hooks/useKeyboardNavigation.ts", "../../emoji-picker-react/src/hooks/preloadEmoji.ts", "../../emoji-picker-react/src/hooks/useOnFocus.ts", "../../emoji-picker-react/src/components/main/PickerMain.tsx", "../../emoji-picker-react/src/DomUtils/elementPositionInRow.ts", "../../emoji-picker-react/src/DomUtils/selectors.ts", "../../emoji-picker-react/src/dataUtils/parseNativeEmoji.ts", "../../emoji-picker-react/src/dataUtils/suggested.ts", "../../emoji-picker-react/src/typeRefinements/typeRefinements.ts", "../../emoji-picker-react/src/hooks/useMouseDownHandlers.ts", "../../emoji-picker-react/src/components/atoms/Button.tsx", "../../emoji-picker-react/src/components/emoji/ClickableEmojiButton.tsx", "../../emoji-picker-react/src/components/emoji/emojiStyles.ts", "../../emoji-picker-react/src/components/emoji/EmojiImg.tsx", "../../emoji-picker-react/src/components/emoji/NativeEmoji.tsx", "../../emoji-picker-react/src/components/emoji/ViewOnlyEmoji.tsx", "../../emoji-picker-react/src/components/emoji/Emoji.tsx", "../../emoji-picker-react/src/components/Reactions/BtnPlus.tsx", "../../emoji-picker-react/src/components/Reactions/Reactions.tsx", "../../emoji-picker-react/src/hooks/useOnScroll.ts", "../../emoji-picker-react/src/hooks/useIsEmojiHidden.ts", "../../emoji-picker-react/src/components/body/EmojiCategory.tsx", "../../emoji-picker-react/src/hooks/useIsEverMounted.ts", "../../emoji-picker-react/src/components/body/Suggested.tsx", "../../emoji-picker-react/src/components/body/EmojiList.tsx", "../../emoji-picker-react/src/components/body/EmojiVariationPicker.tsx", "../../emoji-picker-react/src/components/body/Body.tsx", "../../emoji-picker-react/src/DomUtils/detectEmojyPartiallyBelowFold.ts", "../../emoji-picker-react/src/hooks/useEmojiPreviewEvents.ts", "../../emoji-picker-react/src/components/Layout/Flex.tsx", "../../emoji-picker-react/src/components/Layout/Space.tsx", "../../emoji-picker-react/src/components/Layout/Absolute.tsx", "../../emoji-picker-react/src/components/Layout/Relative.tsx", "../../emoji-picker-react/src/components/header/SkinTonePicker/BtnSkinToneVariation.tsx", "../../emoji-picker-react/src/components/header/SkinTonePicker/SkinTonePicker.tsx", "../../emoji-picker-react/src/components/footer/Preview.tsx", "../../emoji-picker-react/src/DomUtils/categoryNameFromDom.ts", "../../emoji-picker-react/src/hooks/useActiveCategoryScrollDetection.ts", "../../emoji-picker-react/src/hooks/useScrollCategoryIntoView.ts", "../../emoji-picker-react/src/hooks/useShouldHideCustomEmojis.ts", "../../emoji-picker-react/src/components/navigation/CategoryButton.tsx", "../../emoji-picker-react/src/components/navigation/CategoryNavigation.tsx", "../../emoji-picker-react/src/components/header/Search/BtnClearSearch.tsx", "../../emoji-picker-react/src/components/header/Search/CssSearch.tsx", "../../emoji-picker-react/src/components/header/Search/IcnSearch.tsx", "../../emoji-picker-react/src/components/header/Search/Search.tsx", "../../emoji-picker-react/src/components/header/Header.tsx", "../../emoji-picker-react/src/EmojiPickerReact.tsx", "../../emoji-picker-react/src/components/ErrorBoundary.tsx", "../../emoji-picker-react/src/components/emoji/ExportedEmoji.tsx", "../../emoji-picker-react/src/index.tsx"], "sourcesContent": ["export function asArray<T>(v: T | T[]): T[] {\n  return [].concat(v as unknown as []);\n}\n", "import { ClassName } from '../types.js';\n\nexport function isPsuedoSelector(selector: string): boolean {\n  return selector.startsWith(':');\n}\n\nexport function isStyleCondition(selector: string): boolean {\n  return (\n    isString(selector) &&\n    (selector === '*' ||\n      (selector.length > 1 && ':>~.+*'.includes(selector.slice(0, 1))) ||\n      isImmediatePostcondition(selector))\n  );\n}\n\nexport function isValidProperty(\n  property: string,\n  value: unknown,\n): value is string {\n  return (\n    (isString(value) || typeof value === 'number') &&\n    !isCssVariables(property) &&\n    !isPsuedoSelector(property) &&\n    !isMediaQuery(property)\n  );\n}\n\nexport function isMediaQuery(selector: string): boolean {\n  return selector.startsWith('@media');\n}\n\nexport function isDirectClass(selector: string): boolean {\n  return selector === '.';\n}\n\nexport function isCssVariables(selector: string): boolean {\n  return selector === '--';\n}\n\nexport function isString(value: unknown): value is string {\n  return value + '' === value;\n}\n\nexport function isClassName(value: unknown): value is ClassName {\n  return isString(value) && value.length > 1 && value.startsWith('.');\n}\n\nexport function isImmediatePostcondition(\n  value: unknown,\n): value is `&${string}` {\n  return isString(value) && (value.startsWith('&') || isPsuedoSelector(value));\n}\n", "export function joinTruthy(arr: unknown[], delimiter: string = ''): string {\n  return arr.filter(Boolean).join(delimiter);\n}\n", "// Stable hash function.\nexport function stableHash(prefix: string, seed: string): string {\n  let hash = 0;\n  if (seed.length === 0) return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return `${prefix ?? 'cl'}_${hash.toString(36)}`;\n}\n", "// Some properties need special handling\nexport function handlePropertyValue(property: string, value: string): string {\n  if (property === 'content') {\n    return `\"${value}\"`;\n  }\n\n  return value;\n}\n\nexport function camelCaseToDash(str: string): string {\n  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function joinedProperty(property: string, value: string): string {\n  return `${property}:${value}`;\n}\n\nexport function toClass(str: string): string {\n  return str ? `.${str}` : '';\n}\n\nexport function appendString(base: string, line: string): string {\n  return base ? `${base}\\n${line}` : line;\n}\n", "import { Sheet } from './Sheet';\nimport { asArray } from './utils/asArray';\nimport { isImmediatePostcondition, isPsuedoSelector } from './utils/is';\nimport { joinTruthy } from './utils/joinTruthy';\nimport { stableHash } from './utils/stableHash';\nimport {\n  camelCaseToDash,\n  handlePropertyValue,\n  joinedProperty,\n  toClass,\n} from './utils/stringManipulators';\n\nexport class Rule {\n  public hash: string;\n  public joined: string;\n  public key: string;\n\n  constructor(\n    private sheet: Sheet,\n    public property: string,\n    public value: string,\n    private selector: Selector,\n  ) {\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(\n      this.selector.postconditions,\n    );\n    this.hash = this.selector.hasConditions\n      ? (this.selector.scopeClassName as string)\n      : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n\n  public toString(): string {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash,\n    });\n\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors,\n    });\n\n    return `${selectors} {${Rule.genRule(this.property, this.value)}}`;\n  }\n\n  static genRule(property: string, value: string): string {\n    const transformedProperty = camelCaseToDash(property);\n    return (\n      joinedProperty(\n        transformedProperty,\n        handlePropertyValue(property, value),\n      ) + ';'\n    );\n  }\n}\n\nexport function mergeSelectors(\n  selectors: string[],\n  { left = '', right = '' }: { left?: string; right?: string } = {},\n): string {\n  const output = selectors.reduce((selectors, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors + current;\n    }\n\n    if (isImmediatePostcondition(current)) {\n      return selectors + current.slice(1);\n    }\n\n    return joinTruthy([selectors, current], ' ');\n\n    // selector then postcondition\n  }, left);\n\n  // preconditions, then selector\n  return joinTruthy([output, toClass(right)], ' ');\n}\n\nexport class Selector {\n  public preconditions: string[] = [];\n  public scopeClassName: string | null = null;\n  public scopeName: string | null = null;\n  public postconditions: string[] = [];\n\n  constructor(\n    private sheet: Sheet,\n    scopeName: string | null = null,\n    {\n      preconditions,\n      postconditions,\n    }: {\n      preconditions?: string[] | string | undefined;\n      postconditions?: string[] | string | undefined;\n    } = {},\n  ) {\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n\n  private setScope(scopeName: string | null): Selector {\n    if (!scopeName) {\n      return this;\n    }\n\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(\n        this.sheet.name,\n        // adding the count guarantees uniqueness across style.create calls\n        scopeName + this.sheet.count,\n      );\n    }\n\n    return this;\n  }\n\n  get hasConditions(): boolean {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n\n  addScope(scopeName: string): Selector {\n    return new Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions,\n    });\n  }\n\n  addPrecondition(precondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition),\n    });\n  }\n\n  addPostcondition(postcondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition),\n    });\n  }\n\n  createRule(property: string, value: string): Rule {\n    return new Rule(this.sheet, property, value, this);\n  }\n}\n", "import { Rule } from './Rule.js';\nimport { StoredStyles } from './types.js';\nimport { isString } from './utils/is.js';\nimport { appendString } from './utils/stringManipulators.js';\n\nexport class Sheet {\n  private styleTag: HTMLStyleElement | undefined;\n\n  // Hash->css\n  private storedStyles: StoredStyles = {};\n\n  // styles->hash\n  private storedClasses: Record<string, string> = {};\n  private style: string = '';\n  public count = 0;\n  public id: string;\n\n  constructor(\n    public name: string,\n    private rootNode?: HTMLElement | null,\n  ) {\n    this.id = `flairup-${name}`;\n\n    this.styleTag = this.createStyleTag();\n  }\n\n  getStyle(): string {\n    return this.style;\n  }\n\n  append(css: string): void {\n    this.style = appendString(this.style, css);\n  }\n\n  apply(): void {\n    this.count++;\n\n    if (!this.styleTag) {\n      return;\n    }\n\n    this.styleTag.innerHTML = this.style;\n  }\n\n  isApplied(): boolean {\n    return !!this.styleTag;\n  }\n\n  createStyleTag(): HTMLStyleElement | undefined {\n    // check that we're in the browser and have access to the DOM\n    if (\n      typeof document === 'undefined' ||\n      this.isApplied() ||\n      // Explicitly disallow mounting to the DOM\n      this.rootNode === null\n    ) {\n      return this.styleTag;\n    }\n\n    const styleTag = document.createElement('style');\n    styleTag.type = 'text/css';\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n\n  addRule(rule: Rule): string {\n    const storedClass = this.storedClasses[rule.key];\n\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n\n    this.append(rule.toString());\n    return rule.hash;\n  }\n}\n", "export function forIn<O extends Record<string, unknown>>(\n  obj: O,\n  fn: (key: string, value: O[string]) => void,\n): void {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n", "import { joinTruthy } from './utils/joinTruthy';\n\nexport function cx(...args: unknown[]): string {\n  const classes = args.reduce((classes: string[], arg) => {\n    if (arg instanceof Set) {\n      classes.push(...arg);\n    } else if (typeof arg === 'string') {\n      classes.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes.push(cx(...arg));\n    } else if (typeof arg === 'object') {\n      // @ts-expect-error - it is a string\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes.push(key);\n        }\n      });\n    }\n\n    return classes;\n  }, [] as string[]);\n\n  return joinTruthy(classes, ' ').trim();\n}\n", "import { Rule, Selector, mergeSelectors } from './Rule.js';\nimport { Sheet } from './Sheet.js';\nimport {\n  CSSVariablesObject,\n  ClassSet,\n  CreateSheetInput,\n  DirectClass,\n  ScopedStyles,\n  Styles,\n  createSheetReturn,\n} from './types.js';\nimport { asArray } from './utils/asArray.js';\nimport { forIn } from './utils/forIn.js';\nimport {\n  isCssVariables,\n  isDirectClass,\n  isMediaQuery,\n  isStyleCondition,\n  isValidProperty,\n} from './utils/is.js';\n\nexport { cx } from './cx.js';\n\nexport type { CreateSheetInput, Styles };\n\nexport function createSheet(\n  name: string,\n  rootNode?: HTMLElement | null,\n): createSheetReturn {\n  const sheet = new Sheet(name, rootNode);\n\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet),\n  };\n\n  function create<K extends string>(styles: CreateSheetInput<K>) {\n    const scopedStyles: ScopedStyles<K> = {} as ScopedStyles<K>;\n\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(\n      ([scopeName, styles, selector]) => {\n        iterateStyles(sheet, styles as Styles, selector).forEach(\n          (className) => {\n            addScopedStyle(scopeName as K, className);\n          },\n        );\n      },\n    );\n\n    // Commit the styles to the sheet.\n    // Done only once per create call.\n    // This way we do not update the DOM on every style.\n    sheet.apply();\n\n    return scopedStyles;\n\n    function addScopedStyle(name: K, className: string) {\n      scopedStyles[name as keyof ScopedStyles<K>] =\n        scopedStyles[name as keyof ScopedStyles<K>] ?? new Set<string>();\n      scopedStyles[name as keyof ScopedStyles<K>].add(className);\n    }\n  }\n}\n\n// This one plucks out all of the preconditions\n// and creates selector objects from them\nfunction iteratePreconditions(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n) {\n  const output: Array<[string, Styles, Selector]> = [];\n\n  forIn(styles, (key: string, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(\n        sheet,\n        value as Styles,\n        selector.addPrecondition(key),\n      ).forEach((item) => output.push(item));\n    }\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore - this is a valid case\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n\n  return output;\n}\n\nfunction iterateStyles(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n): ClassSet {\n  const output: ClassSet = new Set<string>();\n  // eslint-disable-next-line max-statements\n  forIn(styles, (property, value) => {\n    let res: string[] | Set<string> = [];\n\n    // Postconditions\n    if (isStyleCondition(property)) {\n      res = iterateStyles(\n        sheet,\n        value as Styles,\n        selector.addPostcondition(property),\n      );\n      // Direct classes: \".\": \"className\"\n    } else if (isDirectClass(property)) {\n      res = asArray(value as DirectClass);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value as Styles, property, selector);\n\n      // \"--\": { \"--variable\": \"value\" }\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value as CSSVariablesObject, selector);\n\n      // \"property\": \"value\"\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n\n    return addEachClass(res, output);\n  });\n\n  return output;\n}\n\nfunction addEachClass(list: string[] | Set<string>, to: Set<string>) {\n  list.forEach((className) => to.add(className));\n  return to;\n}\n\n// eslint-disable-next-line max-statements\nfunction cssVariablesBlock(\n  sheet: Sheet,\n  styles: CSSVariablesObject,\n  selector: Selector,\n) {\n  const classes: ClassSet = new Set<string>();\n\n  const chunkRows: string[] = [];\n  forIn(styles, (property: string, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n\n  if (chunkRows.length) {\n    const output = chunkRows.join(' ');\n    sheet.append(\n      `${mergeSelectors(selector.preconditions, {\n        right: selector.scopeClassName,\n      })} {${output}}`,\n    );\n  }\n\n  classes.add(selector.scopeClassName);\n  return classes;\n}\n\nfunction handleMediaQuery(\n  sheet: Sheet,\n  styles: Styles,\n  mediaQuery: string,\n  selector: Selector,\n) {\n  sheet.append(mediaQuery + ' {');\n\n  // iterateStyles will internally append each rule to the sheet\n  // as needed. All we have to do is just open the block and close it after.\n  const output = iterateStyles(sheet, styles, selector);\n\n  sheet.append('}');\n\n  return output;\n}\n", "export enum ClassNames {\n  hiddenOnSearch = 'epr-hidden-on-search',\n  searchActive = 'epr-search-active',\n  hidden = 'epr-hidden',\n  visible = 'epr-visible',\n  active = 'epr-active',\n  emoji = 'epr-emoji',\n  category = 'epr-emoji-category',\n  label = 'epr-emoji-category-label',\n  categoryContent = 'epr-emoji-category-content',\n  emojiHasVariations = 'epr-emoji-has-variations',\n  scrollBody = 'epr-body',\n  emojiList = 'epr-emoji-list',\n  external = '__EmojiPicker__',\n  emojiPicker = 'EmojiPickerReact',\n  open = 'epr-open',\n  vertical = 'epr-vertical',\n  horizontal = 'epr-horizontal',\n  variationPicker = 'epr-emoji-variation-picker',\n  darkTheme = 'epr-dark-theme',\n  autoTheme = 'epr-auto-theme'\n}\n\nexport function asSelectors(...classNames: ClassNames[]): string {\n  return classNames.map(c => `.${c}`).join('');\n}\n", "import { Styles, createSheet } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../DomUtils/classNames';\n\nexport const stylesheet = createSheet('epr', null);\n\nconst hidden = {\n  display: 'none',\n  opacity: '0',\n  pointerEvents: 'none',\n  visibility: 'hidden',\n  overflow: 'hidden'\n};\n\nexport const commonStyles = stylesheet.create({\n  hidden: {\n    '.': ClassNames.hidden,\n    ...hidden\n  }\n});\n\nexport const PickerStyleTag = React.memo(function PickerStyleTag() {\n  return (\n    <style\n      suppressHydrationWarning\n      dangerouslySetInnerHTML={{ __html: stylesheet.getStyle() }}\n    />\n  );\n});\n\nexport const commonInteractionStyles = stylesheet.create({\n  '.epr-main': {\n    ':has(input:not(:placeholder-shown))': {\n      categoryBtn: {\n        ':hover': {\n          opacity: '1',\n          backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n        }\n      },\n      hiddenOnSearch: {\n        '.': ClassNames.hiddenOnSearch,\n        ...hidden\n      }\n    },\n    ':has(input(:placeholder-shown))': {\n      visibleOnSearchOnly: hidden\n    }\n  },\n  hiddenOnReactions: {\n    transition: 'all 0.5s ease-in-out'\n  },\n  '.epr-reactions': {\n    hiddenOnReactions: {\n      height: '0px',\n      width: '0px',\n      opacity: '0',\n      pointerEvents: 'none',\n      overflow: 'hidden'\n    }\n  },\n  '.EmojiPickerReact:not(.epr-search-active)': {\n    categoryBtn: {\n      ':hover': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      },\n      '&.epr-active': {\n        opacity: '1',\n        backgroundPositionY: 'var(--epr-category-navigation-button-size)'\n      }\n    },\n    visibleOnSearchOnly: {\n      '.': 'epr-visible-on-search-only',\n      ...hidden\n    }\n  }\n});\n\nexport function darkMode(key: string, value: Styles) {\n  return {\n    '.epr-dark-theme': {\n      [key]: value\n    },\n    '.epr-auto-theme': {\n      [key]: {\n        '@media (prefers-color-scheme: dark)': value\n      }\n    }\n  };\n}\n", "import { PickerConfig } from './config';\n\n// eslint-disable-next-line complexity\nexport function compareConfig(prev: PickerConfig, next: PickerConfig) {\n  const prevCustomEmojis = prev.customEmojis ?? [];\n  const nextCustomEmojis = next.customEmojis ?? [];\n  return (\n    prev.open === next.open &&\n    prev.emojiVersion === next.emojiVersion &&\n    prev.reactionsDefaultOpen === next.reactionsDefaultOpen &&\n    prev.searchPlaceHolder === next.searchPlaceHolder &&\n    prev.searchPlaceholder === next.searchPlaceholder &&\n    prev.defaultSkinTone === next.defaultSkinTone &&\n    prev.skinTonesDisabled === next.skinTonesDisabled &&\n    prev.autoFocusSearch === next.autoFocusSearch &&\n    prev.emojiStyle === next.emojiStyle &&\n    prev.theme === next.theme &&\n    prev.suggestedEmojisMode === next.suggestedEmojisMode &&\n    prev.lazyLoadEmojis === next.lazyLoadEmojis &&\n    prev.className === next.className &&\n    prev.height === next.height &&\n    prev.width === next.width &&\n    prev.style === next.style &&\n    prev.searchDisabled === next.searchDisabled &&\n    prev.skinTonePickerLocation === next.skinTonePickerLocation &&\n    prevCustomEmojis.length === nextCustomEmojis.length\n  );\n}", "export const DEFAULT_REACTIONS = [\n  '1f44d', // 👍\n  '2764-fe0f', // ❤️\n  '1f603', // 😃\n  '1f622', // 😢\n  '1f64f', // 🙏\n  '1f44e', // 👎\n  '1f621' // 😡\n];\n", "export type EmojiClickData = {\n  activeSkinTone: SkinTones;\n  unified: string;\n  unifiedWithoutSkinTone: string;\n  emoji: string;\n  names: string[];\n  imageUrl: string;\n  getImageUrl: (emojiStyle?: EmojiStyle) => string;\n  isCustom: boolean;\n};\n\nexport enum SuggestionMode {\n  RECENT = 'recent',\n  FREQUENT = 'frequent'\n}\n\nexport enum EmojiStyle {\n  NATIVE = 'native',\n  APPLE = 'apple',\n  TWITTER = 'twitter',\n  GOOGLE = 'google',\n  FACEBOOK = 'facebook'\n}\n\nexport enum Theme {\n  DARK = 'dark',\n  LIGHT = 'light',\n  AUTO = 'auto'\n}\n\nexport enum SkinTones {\n  NEUTRAL = 'neutral',\n  LIGHT = '1f3fb',\n  MEDIUM_LIGHT = '1f3fc',\n  MEDIUM = '1f3fd',\n  MEDIUM_DARK = '1f3fe',\n  DARK = '1f3ff'\n}\n\nexport enum Categories {\n  SUGGESTED = 'suggested',\n  CUSTOM = 'custom',\n  SMILEYS_PEOPLE = 'smileys_people',\n  ANIMALS_NATURE = 'animals_nature',\n  FOOD_DRINK = 'food_drink',\n  TRAVEL_PLACES = 'travel_places',\n  ACTIVITIES = 'activities',\n  OBJECTS = 'objects',\n  SYMBOLS = 'symbols',\n  FLAGS = 'flags'\n}\n\nexport enum SkinTonePickerLocation {\n  SEARCH = 'SEARCH',\n  PREVIEW = 'PREVIEW'\n}\n", "import { Categories, SuggestionMode } from '../types/exposedTypes';\n\nexport { Categories };\n\nconst categoriesOrdered: Categories[] = [\n  Categories.SUGGESTED,\n  Categories.CUSTOM,\n  Categories.SMILEYS_PEOPLE,\n  Categories.ANIMALS_NATURE,\n  Categories.FOOD_DRINK,\n  Categories.TRAVEL_PLACES,\n  Categories.ACTIVITIES,\n  Categories.OBJECTS,\n  Categories.SYMBOLS,\n  Categories.FLAGS\n];\n\nexport const SuggestedRecent: CategoryConfig = {\n  name: 'Recently Used',\n  category: Categories.SUGGESTED\n};\n\nexport type CustomCategoryConfig = {\n  category: Categories.CUSTOM;\n  name: string;\n};\n\nconst configByCategory: Record<Categories, CategoryConfig> = {\n  [Categories.SUGGESTED]: {\n    category: Categories.SUGGESTED,\n    name: 'Frequently Used'\n  },\n  [Categories.CUSTOM]: {\n    category: Categories.CUSTOM,\n    name: 'Custom Emojis'\n  },\n  [Categories.SMILEYS_PEOPLE]: {\n    category: Categories.SMILEYS_PEOPLE,\n    name: 'Smileys & People'\n  },\n  [Categories.ANIMALS_NATURE]: {\n    category: Categories.ANIMALS_NATURE,\n    name: 'Animals & Nature'\n  },\n  [Categories.FOOD_DRINK]: {\n    category: Categories.FOOD_DRINK,\n    name: 'Food & Drink'\n  },\n  [Categories.TRAVEL_PLACES]: {\n    category: Categories.TRAVEL_PLACES,\n    name: 'Travel & Places'\n  },\n  [Categories.ACTIVITIES]: {\n    category: Categories.ACTIVITIES,\n    name: 'Activities'\n  },\n  [Categories.OBJECTS]: {\n    category: Categories.OBJECTS,\n    name: 'Objects'\n  },\n  [Categories.SYMBOLS]: {\n    category: Categories.SYMBOLS,\n    name: 'Symbols'\n  },\n  [Categories.FLAGS]: {\n    category: Categories.FLAGS,\n    name: 'Flags'\n  }\n};\n\nexport function baseCategoriesConfig(\n  modifiers?: Record<Categories, CategoryConfig>\n): CategoriesConfig {\n  return categoriesOrdered.map(category => {\n    return {\n      ...configByCategory[category],\n      ...(modifiers && modifiers[category] && modifiers[category])\n    };\n  });\n}\n\nexport function categoryFromCategoryConfig(category: CategoryConfig) {\n  return category.category;\n}\n\nexport function categoryNameFromCategoryConfig(category: CategoryConfig) {\n  return category.name;\n}\n\nexport type CategoriesConfig = CategoryConfig[];\n\nexport type CategoryConfig = {\n  category: Categories;\n  name: string;\n};\n\nexport type UserCategoryConfig = Array<Categories | CategoryConfig>;\n\nexport function mergeCategoriesConfig(\n  userCategoriesConfig: UserCategoryConfig = [],\n  modifiers: CategoryConfigModifiers = {}\n): CategoriesConfig {\n  const extra = {} as Record<Categories, CategoryConfig>;\n\n  if (modifiers.suggestionMode === SuggestionMode.RECENT) {\n    extra[Categories.SUGGESTED] = SuggestedRecent;\n  }\n\n  const base = baseCategoriesConfig(extra);\n  if (!userCategoriesConfig?.length) {\n    return base;\n  }\n\n  return userCategoriesConfig.map(category => {\n    if (typeof category === 'string') {\n      return getBaseConfigByCategory(category, extra[category]);\n    }\n\n    return {\n      ...getBaseConfigByCategory(category.category, extra[category.category]),\n      ...category\n    };\n  });\n}\n\nfunction getBaseConfigByCategory(\n  category: Categories,\n  modifier: CategoryConfig = {} as CategoryConfig\n) {\n  return Object.assign(configByCategory[category], modifier);\n}\n\ntype CategoryConfigModifiers = {\n  suggestionMode?: SuggestionMode;\n};\n", "import { EmojiStyle } from '../types/exposedTypes';\n\nconst CDN_URL_APPLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-apple/img/apple/64/';\nconst CDN_URL_FACEBOOK =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-facebook/img/facebook/64/';\nconst CDN_URL_TWITTER =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-twitter/img/twitter/64/';\nconst CDN_URL_GOOGLE =\n  'https://cdn.jsdelivr.net/npm/emoji-datasource-google/img/google/64/';\n\nexport function cdnUrl(emojiStyle: EmojiStyle): string {\n  switch (emojiStyle) {\n    case EmojiStyle.TWITTER:\n      return CDN_URL_TWITTER;\n    case EmojiStyle.GOOGLE:\n      return CDN_URL_GOOGLE;\n    case EmojiStyle.FACEBOOK:\n      return CDN_URL_FACEBOOK;\n    case EmojiStyle.APPLE:\n    default:\n      return CDN_URL_APPLE;\n  }\n}\n", "import { SkinTones } from '../types/exposedTypes';\n\nconst skinToneVariations = [\n  SkinTones.NEUTRAL,\n  SkinTones.LIGHT,\n  SkinTones.MEDIUM_LIGHT,\n  SkinTones.MEDIUM,\n  SkinTones.MEDIUM_DARK,\n  SkinTones.DARK\n];\n\nexport const skinTonesNamed = Object.entries(SkinTones).reduce(\n  (acc, [key, value]) => {\n    acc[value] = key;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\nexport const skinTonesMapped: Record<\n  string,\n  string\n> = skinToneVariations.reduce(\n  (mapped, skinTone) =>\n    Object.assign(mapped, {\n      [skinTone]: skinTone\n    }),\n  {}\n);\n\nexport default skinToneVariations;\n", "import emojis from '../data/emojis';\n\nexport enum EmojiProperties {\n  name = 'n',\n  unified = 'u',\n  variations = 'v',\n  added_in = 'a',\n  imgUrl = 'imgUrl'\n}\n\nexport interface DataEmoji extends WithName {\n  [EmojiProperties.unified]: string;\n  [EmojiProperties.variations]?: string[];\n  [EmojiProperties.added_in]: string;\n  [EmojiProperties.imgUrl]?: string;\n}\n\nexport type DataEmojis = DataEmoji[];\n\nexport type DataGroups = keyof typeof emojis;\n\nexport type WithName = {\n  [EmojiProperties.name]: string[];\n};\n", "import { DataEmoji } from './DataTypes';\nimport { allEmojis, emojiNames, emojiUnified } from './emojiSelectors';\n\nexport const alphaNumericEmojiIndex: BaseIndex = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((searchIndex, emoji) => {\n    indexEmoji(emoji);\n    return searchIndex;\n  }, alphaNumericEmojiIndex as BaseIndex);\n});\n\ntype BaseIndex = Record<string, Record<string, DataEmoji>>;\n\nexport function indexEmoji(emoji: DataEmoji): void {\n  const joinedNameString = emojiNames(emoji)\n    .flat()\n    .join('')\n    .toLowerCase()\n    .replace(/[^a-zA-Z\\d]/g, '')\n    .split('');\n\n  joinedNameString.forEach(char => {\n    alphaNumericEmojiIndex[char] = alphaNumericEmojiIndex[char] ?? {};\n\n    alphaNumericEmojiIndex[char][emojiUnified(emoji)] = emoji;\n  });\n}\n", "import { Categories } from '../config/categoryConfig';\nimport { cdnUrl } from '../config/cdnUrls';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport emojis from '../data/emojis';\nimport skinToneVariations, {\n  skinTonesMapped\n} from '../data/skinToneVariations';\nimport { EmojiStyle, SkinTones } from '../types/exposedTypes';\n\nimport { DataEmoji, DataEmojis, EmojiProperties, WithName } from './DataTypes';\nimport { indexEmoji } from './alphaNumericEmojiIndex';\n\nexport function emojiNames(emoji: WithName): string[] {\n  return emoji[EmojiProperties.name] ?? [];\n}\n\nexport function addedIn(emoji: DataEmoji): number {\n  return parseFloat(emoji[EmojiProperties.added_in]);\n}\n\nexport function emojiName(emoji?: WithName): string {\n  if (!emoji) {\n    return '';\n  }\n\n  return emojiNames(emoji)[0];\n}\n\nexport function unifiedWithoutSkinTone(unified: string): string {\n  const splat = unified.split('-');\n  const [skinTone] = splat.splice(1, 1);\n\n  if (skinTonesMapped[skinTone]) {\n    return splat.join('-');\n  }\n\n  return unified;\n}\n\nexport function emojiUnified(emoji: DataEmoji, skinTone?: string): string {\n  const unified = emoji[EmojiProperties.unified];\n\n  if (!skinTone || !emojiHasVariations(emoji)) {\n    return unified;\n  }\n\n  return emojiVariationUnified(emoji, skinTone) ?? unified;\n}\n\nexport function emojisByCategory(category: Categories): DataEmojis {\n  // @ts-ignore\n  return emojis?.[category] ?? [];\n}\n\n// WARNING: DO NOT USE DIRECTLY\nexport function emojiUrlByUnified(\n  unified: string,\n  emojiStyle: EmojiStyle\n): string {\n  return `${cdnUrl(emojiStyle)}${unified}.png`;\n}\n\nexport function emojiVariations(emoji: DataEmoji): string[] {\n  return emoji[EmojiProperties.variations] ?? [];\n}\n\nexport function emojiHasVariations(emoji: DataEmoji): boolean {\n  return emojiVariations(emoji).length > 0;\n}\n\nexport function emojiVariationUnified(\n  emoji: DataEmoji,\n  skinTone?: string\n): string | undefined {\n  return skinTone\n    ? emojiVariations(emoji).find(variation => variation.includes(skinTone))\n    : emojiUnified(emoji);\n}\n\nexport function emojiByUnified(unified?: string): DataEmoji | undefined {\n  if (!unified) {\n    return;\n  }\n\n  if (allEmojisByUnified[unified]) {\n    return allEmojisByUnified[unified];\n  }\n\n  const withoutSkinTone = unifiedWithoutSkinTone(unified);\n  return allEmojisByUnified[withoutSkinTone];\n}\n\nexport const allEmojis: DataEmojis = Object.values(emojis).flat();\n\nexport function setCustomEmojis(customEmojis: CustomEmoji[]): void {\n  emojis[Categories.CUSTOM].length = 0;\n\n  customEmojis.forEach(emoji => {\n    const emojiData = customToRegularEmoji(emoji);\n\n    emojis[Categories.CUSTOM].push(emojiData as never);\n\n    if (allEmojisByUnified[emojiData[EmojiProperties.unified]]) {\n      return;\n    }\n\n    allEmojis.push(emojiData);\n    allEmojisByUnified[emojiData[EmojiProperties.unified]] = emojiData;\n    indexEmoji(emojiData);\n  });\n}\n\nfunction customToRegularEmoji(emoji: CustomEmoji): DataEmoji {\n  return {\n    [EmojiProperties.name]: emoji.names.map(name => name.toLowerCase()),\n    [EmojiProperties.unified]: emoji.id.toLowerCase(),\n    [EmojiProperties.added_in]: '0',\n    [EmojiProperties.imgUrl]: emoji.imgUrl\n  };\n}\n\nconst allEmojisByUnified: {\n  [unified: string]: DataEmoji;\n} = {};\n\nsetTimeout(() => {\n  allEmojis.reduce((allEmojis, Emoji) => {\n    allEmojis[emojiUnified(Emoji)] = Emoji;\n\n    if (emojiHasVariations(Emoji)) {\n      emojiVariations(Emoji).forEach(variation => {\n        allEmojis[variation] = Emoji;\n      });\n    }\n\n    return allEmojis;\n  }, allEmojisByUnified);\n});\n\nexport function activeVariationFromUnified(unified: string): SkinTones | null {\n  const [, suspectedSkinTone] = unified.split('-') as [string, SkinTones];\n  return skinToneVariations.includes(suspectedSkinTone)\n    ? suspectedSkinTone\n    : null;\n}\n", "import * as React from 'react';\n\nimport { DEFAULT_REACTIONS } from '../components/Reactions/DEFAULT_REACTIONS';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  setCustomEmojis,\n  emojiUrlByUnified\n} from '../dataUtils/emojiSelectors';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport {\n  CategoriesConfig,\n  baseCategoriesConfig,\n  mergeCategoriesConfig\n} from './categoryConfig';\nimport { CustomEmoji } from './customEmojiConfig';\n\nconst KNOWN_FAILING_EMOJIS = ['2640-fe0f', '2642-fe0f', '2695-fe0f'];\n\nexport const DEFAULT_SEARCH_PLACEHOLDER = 'Search';\nexport const SEARCH_RESULTS_NO_RESULTS_FOUND = 'No results found';\nexport const SEARCH_RESULTS_SUFFIX =\n  ' found. Use up and down arrow keys to navigate.';\nexport const SEARCH_RESULTS_ONE_RESULT_FOUND =\n  '1 result' + SEARCH_RESULTS_SUFFIX;\nexport const SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND =\n  '%n results' + SEARCH_RESULTS_SUFFIX;\n\nexport function mergeConfig(\n  userConfig: PickerConfig = {}\n): PickerConfigInternal {\n  const base = basePickerConfig();\n\n  const previewConfig = Object.assign(\n    base.previewConfig,\n    userConfig.previewConfig ?? {}\n  );\n  const config = Object.assign(base, userConfig);\n\n  const categories = mergeCategoriesConfig(userConfig.categories, {\n    suggestionMode: config.suggestedEmojisMode\n  });\n\n  config.hiddenEmojis.forEach(emoji => {\n    config.unicodeToHide.add(emoji);\n  });\n\n  setCustomEmojis(config.customEmojis ?? []);\n\n  const skinTonePickerLocation = config.searchDisabled\n    ? SkinTonePickerLocation.PREVIEW\n    : config.skinTonePickerLocation;\n\n  return {\n    ...config,\n    categories,\n    previewConfig,\n    skinTonePickerLocation\n  };\n}\n\nexport function basePickerConfig(): PickerConfigInternal {\n  return {\n    autoFocusSearch: true,\n    categories: baseCategoriesConfig(),\n    className: '',\n    customEmojis: [],\n    defaultSkinTone: SkinTones.NEUTRAL,\n    emojiStyle: EmojiStyle.APPLE,\n    emojiVersion: null,\n    getEmojiUrl: emojiUrlByUnified,\n    height: 450,\n    lazyLoadEmojis: false,\n    previewConfig: {\n      ...basePreviewConfig\n    },\n    searchDisabled: false,\n    searchPlaceHolder: DEFAULT_SEARCH_PLACEHOLDER,\n    searchPlaceholder: DEFAULT_SEARCH_PLACEHOLDER,\n    skinTonePickerLocation: SkinTonePickerLocation.SEARCH,\n    skinTonesDisabled: false,\n    style: {},\n    suggestedEmojisMode: SuggestionMode.FREQUENT,\n    theme: Theme.LIGHT,\n    unicodeToHide: new Set<string>(KNOWN_FAILING_EMOJIS),\n    width: 350,\n    reactionsDefaultOpen: false,\n    reactions: DEFAULT_REACTIONS,\n    open: true,\n    allowExpandReactions: true,\n    hiddenEmojis: []\n  };\n}\n\nexport type PickerConfigInternal = {\n  emojiVersion: string | null;\n  searchPlaceHolder: string;\n  searchPlaceholder: string;\n  defaultSkinTone: SkinTones;\n  skinTonesDisabled: boolean;\n  autoFocusSearch: boolean;\n  emojiStyle: EmojiStyle;\n  categories: CategoriesConfig;\n  theme: Theme;\n  suggestedEmojisMode: SuggestionMode;\n  lazyLoadEmojis: boolean;\n  previewConfig: PreviewConfig;\n  className: string;\n  height: PickerDimensions;\n  width: PickerDimensions;\n  style: React.CSSProperties;\n  getEmojiUrl: GetEmojiUrl;\n  searchDisabled: boolean;\n  skinTonePickerLocation: SkinTonePickerLocation;\n  unicodeToHide: Set<string>;\n  customEmojis: CustomEmoji[];\n  reactionsDefaultOpen: boolean;\n  reactions: string[];\n  open: boolean;\n  allowExpandReactions: boolean;\n  hiddenEmojis: string[];\n};\n\nexport type PreviewConfig = {\n  defaultEmoji: string;\n  defaultCaption: string;\n  showPreview: boolean;\n};\n\nconst basePreviewConfig: PreviewConfig = {\n  defaultEmoji: '1f60a',\n  defaultCaption: \"What's your mood?\",\n  showPreview: true\n};\n\ntype ConfigExternal = {\n  previewConfig: Partial<PreviewConfig>;\n  onEmojiClick: MouseDownEvent;\n  onReactionClick: MouseDownEvent;\n  onSkinToneChange: OnSkinToneChange;\n} & Omit<PickerConfigInternal, 'previewConfig' | 'unicodeToHide'>;\n\nexport type PickerConfig = Partial<ConfigExternal>;\n\nexport type PickerDimensions = string | number;\n\nexport type MouseDownEvent = (emoji: EmojiClickData, event: MouseEvent) => void;\nexport type OnSkinToneChange = (emoji: SkinTones) => void;\n", "import * as React from 'react';\n\nimport { compareConfig } from '../../config/compareConfig';\nimport {\n  basePickerConfig,\n  mergeConfig,\n  PickerConfig,\n  PickerConfigInternal\n} from '../../config/config';\n\ntype Props = PickerConfig &\n  Readonly<{\n    children: React.ReactNode;\n  }>;\n\nconst ConfigContext = React.createContext<PickerConfigInternal>(\n  basePickerConfig()\n);\n\nexport function PickerConfigProvider({ children, ...config }: Props) {\n  const mergedConfig = useSetConfig(config);\n\n  return (\n    <ConfigContext.Provider value={mergedConfig}>\n      {children}\n    </ConfigContext.Provider>\n  );\n}\n\nexport function useSetConfig(config: PickerConfig) {\n  const [mergedConfig, setMergedConfig] = React.useState(() =>\n    mergeConfig(config)\n  );\n\n  React.useEffect(() => {\n    if (compareConfig(mergedConfig, config)) {\n      return;\n    }\n    setMergedConfig(mergeConfig(config));\n    // not gonna...\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    config.customEmojis?.length,\n    config.open,\n    config.emojiVersion,\n    config.reactionsDefaultOpen,\n    config.searchPlaceHolder,\n    config.searchPlaceholder,\n    config.defaultSkinTone,\n    config.skinTonesDisabled,\n    config.autoFocusSearch,\n    config.emojiStyle,\n    config.theme,\n    config.suggestedEmojisMode,\n    config.lazyLoadEmojis,\n    config.className,\n    config.height,\n    config.width,\n    config.searchDisabled,\n    config.skinTonePickerLocation,\n    config.allowExpandReactions\n  ]);\n\n  return mergedConfig;\n}\n\nexport function usePickerConfig() {\n  return React.useContext(ConfigContext);\n}\n", "import React from 'react';\n\nimport {MouseDownEvent, OnSkinToneChange} from './config';\n\nexport type MutableConfig = {\n  onEmojiClick?: MouseDownEvent;\n  onReactionClick?: MouseDownEvent;\n  onSkinToneChange?: OnSkinToneChange;\n};\n\nexport const MutableConfigContext = React.createContext<\n  React.MutableRefObject<MutableConfig>\n>({} as React.MutableRefObject<MutableConfig>);\n\nexport function useMutableConfig(): React.MutableRefObject<MutableConfig> {\n  const mutableConfig = React.useContext(MutableConfigContext);\n  return mutableConfig;\n}\n\nexport function useDefineMutableConfig(\n  config: MutableConfig\n): React.MutableRefObject<MutableConfig> {\n  const MutableConfigRef = React.useRef<MutableConfig>({\n    onEmojiClick: config.onEmojiClick || emptyFunc,\n    onReactionClick: config.onReactionClick || config.onEmojiClick,\n    onSkinToneChange: config.onSkinToneChange || emptyFunc\n  });\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onEmojiClick = config.onEmojiClick || emptyFunc;\n    MutableConfigRef.current.onReactionClick =\n      config.onReactionClick || config.onEmojiClick;\n  }, [config.onEmojiClick, config.onReactionClick]);\n\n  React.useEffect(() => {\n    MutableConfigRef.current.onSkinToneChange = config.onSkinToneChange || emptyFunc;\n  }, [config.onSkinToneChange])\n\n  return MutableConfigRef;\n}\n\nfunction emptyFunc() {}\n", "import * as React from 'react';\n\nimport { usePickerConfig } from '../components/context/PickerConfigContext';\nimport {\n  EmojiClickData,\n  EmojiStyle,\n  SkinTonePickerLocation,\n  SkinTones,\n  SuggestionMode,\n  Theme\n} from '../types/exposedTypes';\n\nimport { CategoriesConfig } from './categoryConfig';\nimport {\n  DEFAULT_SEARCH_PLACEHOLDER,\n  SEARCH_RESULTS_NO_RESULTS_FOUND,\n  SEARCH_RESULTS_ONE_RESULT_FOUND,\n  SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND,\n  PickerDimensions,\n  PreviewConfig\n} from './config';\nimport { CustomEmoji } from './customEmojiConfig';\nimport { useMutableConfig } from './mutableConfig';\n\nexport enum MOUSE_EVENT_SOURCE {\n  REACTIONS = 'reactions',\n  PICKER = 'picker'\n}\n\nexport function useSearchPlaceHolderConfig(): string {\n  const { searchPlaceHolder, searchPlaceholder } = usePickerConfig();\n  return (\n    [searchPlaceHolder, searchPlaceholder].find(\n      p => p !== DEFAULT_SEARCH_PLACEHOLDER\n    ) ?? DEFAULT_SEARCH_PLACEHOLDER\n  );\n}\n\nexport function useDefaultSkinToneConfig(): SkinTones {\n  const { defaultSkinTone } = usePickerConfig();\n  return defaultSkinTone;\n}\n\nexport function useAllowExpandReactions(): boolean {\n  const { allowExpandReactions } = usePickerConfig();\n  return allowExpandReactions;\n}\n\nexport function useSkinTonesDisabledConfig(): boolean {\n  const { skinTonesDisabled } = usePickerConfig();\n  return skinTonesDisabled;\n}\n\nexport function useEmojiStyleConfig(): EmojiStyle {\n  const { emojiStyle } = usePickerConfig();\n  return emojiStyle;\n}\n\nexport function useAutoFocusSearchConfig(): boolean {\n  const { autoFocusSearch } = usePickerConfig();\n  return autoFocusSearch;\n}\n\nexport function useCategoriesConfig(): CategoriesConfig {\n  const { categories } = usePickerConfig();\n  return categories;\n}\n\nexport function useCustomEmojisConfig(): CustomEmoji[] {\n  const { customEmojis } = usePickerConfig();\n  return customEmojis;\n}\n\nexport function useOpenConfig(): boolean {\n  const { open } = usePickerConfig();\n  return open;\n}\n\nexport function useOnEmojiClickConfig(\n  mouseEventSource: MOUSE_EVENT_SOURCE\n): (emoji: EmojiClickData, event: MouseEvent) => void {\n  const { current } = useMutableConfig();\n\n  const handler =\n    (mouseEventSource === MOUSE_EVENT_SOURCE.REACTIONS\n      ? current.onReactionClick\n      : current.onEmojiClick) ?? current.onEmojiClick;\n\n  return handler || (() => {});\n}\n\nexport function useOnSkinToneChangeConfig(\n): (skinTone: SkinTones) => void {\n  const { current } = useMutableConfig();\n\n  return current.onSkinToneChange || (() => {});\n}\n\nexport function usePreviewConfig(): PreviewConfig {\n  const { previewConfig } = usePickerConfig();\n  return previewConfig;\n}\n\nexport function useThemeConfig(): Theme {\n  const { theme } = usePickerConfig();\n\n  return theme;\n}\n\nexport function useSuggestedEmojisModeConfig(): SuggestionMode {\n  const { suggestedEmojisMode } = usePickerConfig();\n  return suggestedEmojisMode;\n}\n\nexport function useLazyLoadEmojisConfig(): boolean {\n  const { lazyLoadEmojis } = usePickerConfig();\n  return lazyLoadEmojis;\n}\n\nexport function useClassNameConfig(): string {\n  const { className } = usePickerConfig();\n  return className;\n}\n\nexport function useStyleConfig(): React.CSSProperties {\n  const { height, width, style } = usePickerConfig();\n  return { height: getDimension(height), width: getDimension(width), ...style };\n}\n\nexport function useReactionsOpenConfig(): boolean {\n  const { reactionsDefaultOpen } = usePickerConfig();\n  return reactionsDefaultOpen;\n}\n\nexport function useEmojiVersionConfig(): string | null {\n  const { emojiVersion } = usePickerConfig();\n  return emojiVersion;\n}\n\nexport function useSearchDisabledConfig(): boolean {\n  const { searchDisabled } = usePickerConfig();\n  return searchDisabled;\n}\n\nexport function useSkinTonePickerLocationConfig(): SkinTonePickerLocation {\n  const { skinTonePickerLocation } = usePickerConfig();\n  return skinTonePickerLocation;\n}\n\nexport function useUnicodeToHide() {\n  const { unicodeToHide } = usePickerConfig();\n  return unicodeToHide;\n}\n\nexport function useReactionsConfig(): string[] {\n  const { reactions } = usePickerConfig();\n  return reactions;\n}\n\nexport function useGetEmojiUrlConfig(): (\n  unified: string,\n  style: EmojiStyle\n) => string {\n  const { getEmojiUrl } = usePickerConfig();\n  return getEmojiUrl;\n}\n\nfunction getDimension(dimensionConfig: PickerDimensions): PickerDimensions {\n  return typeof dimensionConfig === 'number'\n    ? `${dimensionConfig}px`\n    : dimensionConfig;\n}\n\nexport function useSearchResultsConfig(searchResultsCount: number): string {\n  const hasResults = searchResultsCount > 0;\n  const isPlural = searchResultsCount > 1;\n\n  if (hasResults) {\n    return isPlural\n      ? SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND.replace(\n          '%n',\n          searchResultsCount.toString()\n        )\n      : SEARCH_RESULTS_ONE_RESULT_FOUND;\n  }\n\n  return SEARCH_RESULTS_NO_RESULTS_FOUND;\n}\n", "import { useRef, useState } from 'react';\n\nexport function useDebouncedState<T>(\n  initialValue: T,\n  delay: number = 0\n): [T, (value: T) => Promise<T>] {\n  const [state, setState] = useState<T>(initialValue);\n  const timer = useRef<number | null>(null);\n\n  function debouncedSetState(value: T) {\n    return new Promise<T>(resolve => {\n      if (timer.current) {\n        clearTimeout(timer.current);\n      }\n\n      timer.current = window?.setTimeout(() => {\n        setState(value);\n        resolve(value);\n      }, delay);\n    });\n  }\n\n  return [state, debouncedSetState];\n}\n", "import { useUnicodeToHide } from \"../config/useConfig\";\n\nexport function useIsUnicodeHidden() {\n    const unicodeToHide = useUnicodeToHide();\n    return (emojiUnified: string) => unicodeToHide.has(emojiUnified);\n  }\n", "import { useRef, useMemo } from 'react';\n\nimport { useEmojiVersionConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  addedIn,\n  allEmojis,\n  emojiUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { useIsUnicodeHidden } from './useHideEmojisByUniocode';\n\nexport function useDisallowedEmojis() {\n  const DisallowedEmojisRef = useRef<Record<string, boolean>>({});\n  const emojiVersionConfig = useEmojiVersionConfig();\n\n  return useMemo(() => {\n    const emojiVersion = parseFloat(`${emojiVersionConfig}`);\n\n    if (!emojiVersionConfig || Number.isNaN(emojiVersion)) {\n      return DisallowedEmojisRef.current;\n    }\n\n    return allEmojis.reduce((disallowedEmojis, emoji) => {\n      if (addedInNewerVersion(emoji, emojiVersion)) {\n        disallowedEmojis[emojiUnified(emoji)] = true;\n      }\n\n      return disallowedEmojis;\n    }, DisallowedEmojisRef.current);\n  }, [emojiVersionConfig]);\n}\n\nexport function useIsEmojiDisallowed() {\n  const disallowedEmojis = useDisallowedEmojis();\n  const isUnicodeHidden = useIsUnicodeHidden();\n\n  return function isEmojiDisallowed(emoji: DataEmoji) {\n    const unified = unifiedWithoutSkinTone(emojiUnified(emoji));\n\n    return Boolean(disallowedEmojis[unified] || isUnicodeHidden(unified));\n  };\n}\n\nfunction addedInNewerVersion(\n  emoji: DataEmoji,\n  supportedLevel: number\n): boolean {\n  return addedIn(emoji) > supportedLevel;\n}\n", "import { useEffect } from 'react';\nimport * as React from 'react';\n\nexport function useMarkInitialLoad(\n  dispatch: React.Dispatch<React.SetStateAction<boolean>>\n) {\n  useEffect(() => {\n    dispatch(true);\n  }, [dispatch]);\n}\n", "import * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  useDefaultSkinToneConfig,\n  useReactionsOpenConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { alphaNumericEmojiIndex } from '../../dataUtils/alphaNumericEmojiIndex';\nimport { useDebouncedState } from '../../hooks/useDebouncedState';\nimport { useDisallowedEmojis } from '../../hooks/useDisallowedEmojis';\nimport { FilterDict } from '../../hooks/useFilter';\nimport { useMarkInitialLoad } from '../../hooks/useInitialLoad';\nimport { SkinTones } from '../../types/exposedTypes';\n\nexport function PickerContextProvider({ children }: Props) {\n  const disallowedEmojis = useDisallowedEmojis();\n  const defaultSkinTone = useDefaultSkinToneConfig();\n  const reactionsDefaultOpen = useReactionsOpenConfig();\n\n  // Initialize the filter with the inititial dictionary\n  const filterRef = React.useRef<FilterState>(alphaNumericEmojiIndex);\n  const disallowClickRef = React.useRef<boolean>(false);\n  const disallowMouseRef = React.useRef<boolean>(false);\n  const disallowedEmojisRef = React.useRef<Record<string, boolean>>(\n    disallowedEmojis\n  );\n\n  const suggestedUpdateState = useDebouncedState(Date.now(), 200);\n  const searchTerm = useDebouncedState('', 100);\n  const skinToneFanOpenState = useState<boolean>(false);\n  const activeSkinTone = useState<SkinTones>(defaultSkinTone);\n  const activeCategoryState = useState<ActiveCategoryState>(null);\n  const emojisThatFailedToLoadState = useState<Set<string>>(new Set());\n  const emojiVariationPickerState = useState<DataEmoji | null>(null);\n  const reactionsModeState = useState(reactionsDefaultOpen);\n  const [isPastInitialLoad, setIsPastInitialLoad] = useState(false);\n\n  useMarkInitialLoad(setIsPastInitialLoad);\n\n  return (\n    <PickerContext.Provider\n      value={{\n        activeCategoryState,\n        activeSkinTone,\n        disallowClickRef,\n        disallowMouseRef,\n        disallowedEmojisRef,\n        emojiVariationPickerState,\n        emojisThatFailedToLoadState,\n        filterRef,\n        isPastInitialLoad,\n        searchTerm,\n        skinToneFanOpenState,\n        suggestedUpdateState,\n        reactionsModeState\n      }}\n    >\n      {children}\n    </PickerContext.Provider>\n  );\n}\n\ntype ReactState<T> = [T, React.Dispatch<React.SetStateAction<T>>];\n\nconst PickerContext = React.createContext<{\n  searchTerm: [string, (term: string) => Promise<string>];\n  suggestedUpdateState: [number, (term: number) => void];\n  activeCategoryState: ReactState<ActiveCategoryState>;\n  activeSkinTone: ReactState<SkinTones>;\n  emojisThatFailedToLoadState: ReactState<Set<string>>;\n  isPastInitialLoad: boolean;\n  emojiVariationPickerState: ReactState<DataEmoji | null>;\n  skinToneFanOpenState: ReactState<boolean>;\n  filterRef: React.MutableRefObject<FilterState>;\n  disallowClickRef: React.MutableRefObject<boolean>;\n  disallowMouseRef: React.MutableRefObject<boolean>;\n  disallowedEmojisRef: React.MutableRefObject<Record<string, boolean>>;\n  reactionsModeState: ReactState<boolean>;\n}>({\n  activeCategoryState: [null, () => {}],\n  activeSkinTone: [SkinTones.NEUTRAL, () => {}],\n  disallowClickRef: { current: false },\n  disallowMouseRef: { current: false },\n  disallowedEmojisRef: { current: {} },\n  emojiVariationPickerState: [null, () => {}],\n  emojisThatFailedToLoadState: [new Set(), () => {}],\n  filterRef: { current: {} },\n  isPastInitialLoad: true,\n  searchTerm: ['', () => new Promise<string>(() => undefined)],\n  skinToneFanOpenState: [false, () => {}],\n  suggestedUpdateState: [Date.now(), () => {}],\n  reactionsModeState: [false, () => {}]\n});\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport function useFilterRef() {\n  const { filterRef } = React.useContext(PickerContext);\n  return filterRef;\n}\n\nexport function useDisallowClickRef() {\n  const { disallowClickRef } = React.useContext(PickerContext);\n  return disallowClickRef;\n}\n\nexport function useDisallowMouseRef() {\n  const { disallowMouseRef } = React.useContext(PickerContext);\n  return disallowMouseRef;\n}\n\nexport function useReactionsModeState() {\n  const { reactionsModeState } = React.useContext(PickerContext);\n  return reactionsModeState;\n}\n\nexport function useSearchTermState() {\n  const { searchTerm } = React.useContext(PickerContext);\n  return searchTerm;\n}\n\nexport function useActiveSkinToneState(): [\n  SkinTones,\n  (skinTone: SkinTones) => void\n] {\n  const { activeSkinTone } = React.useContext(PickerContext);\n  return activeSkinTone;\n}\n\nexport function useEmojisThatFailedToLoadState() {\n  const { emojisThatFailedToLoadState } = React.useContext(PickerContext);\n  return emojisThatFailedToLoadState;\n}\n\nexport function useIsPastInitialLoad(): boolean {\n  const { isPastInitialLoad } = React.useContext(PickerContext);\n  return isPastInitialLoad;\n}\n\nexport function useEmojiVariationPickerState() {\n  const { emojiVariationPickerState } = React.useContext(PickerContext);\n  return emojiVariationPickerState;\n}\n\nexport function useSkinToneFanOpenState() {\n  const { skinToneFanOpenState } = React.useContext(PickerContext);\n  return skinToneFanOpenState;\n}\n\nexport function useDisallowedEmojisRef() {\n  const { disallowedEmojisRef } = React.useContext(PickerContext);\n  return disallowedEmojisRef;\n}\n\nexport function useUpdateSuggested(): [number, () => void] {\n  const { suggestedUpdateState } = React.useContext(PickerContext);\n\n  const [suggestedUpdated, setsuggestedUpdate] = suggestedUpdateState;\n  return [\n    suggestedUpdated,\n    function updateSuggested() {\n      setsuggestedUpdate(Date.now());\n    }\n  ];\n}\n\nexport type FilterState = Record<string, FilterDict>;\n\ntype ActiveCategoryState = null | string;\n", "import { useSearchTermState } from '../components/context/PickerContext';\n\nexport default function useIsSearchMode(): boolean {\n  const [searchTerm] = useSearchTermState();\n\n  return !!searchTerm;\n}\n", "import { NullableElement } from './selectors';\n\nexport function focusElement(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    element.focus();\n  });\n}\n\nexport function focusPrevElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const prev = element.previousElementSibling as HTMLElement;\n\n  focusElement(prev);\n}\n\nexport function focusNextElementSibling(element: NullableElement) {\n  if (!element) return;\n\n  const next = element.nextElementSibling as HTMLElement;\n\n  focusElement(next);\n}\n\nexport function focusFirstElementChild(element: NullableElement) {\n  if (!element) return;\n\n  const first = element.firstElementChild as HTMLElement;\n\n  focusElement(first);\n}\n", "import { NullableElement } from './selectors';\n\nexport function getActiveElement() {\n  return document.activeElement as NullableElement;\n}\n", "import * as React from 'react';\n\nimport { focusElement } from '../../DomUtils/focusElement';\nimport { NullableElement } from '../../DomUtils/selectors';\n\nexport function ElementRefContextProvider({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const PickerMainRef = React.useRef<HTMLElement>(null);\n  const AnchoredEmojiRef = React.useRef<HTMLElement>(null);\n  const BodyRef = React.useRef<HTMLDivElement>(null);\n  const SearchInputRef = React.useRef<HTMLInputElement>(null);\n  const SkinTonePickerRef = React.useRef<HTMLDivElement>(null);\n  const CategoryNavigationRef = React.useRef<HTMLDivElement>(null);\n  const VariationPickerRef = React.useRef<HTMLDivElement>(null);\n  const ReactionsRef = React.useRef<HTMLUListElement>(null);\n\n  return (\n    <ElementRefContext.Provider\n      value={{\n        AnchoredEmojiRef,\n        BodyRef,\n        CategoryNavigationRef,\n        PickerMainRef,\n        SearchInputRef,\n        SkinTonePickerRef,\n        VariationPickerRef,\n        ReactionsRef\n      }}\n    >\n      {children}\n    </ElementRefContext.Provider>\n  );\n}\n\nexport type ElementRef<\n  E extends HTMLElement = HTMLElement\n> = React.MutableRefObject<E | null>;\n\ntype ElementRefs = {\n  PickerMainRef: ElementRef;\n  AnchoredEmojiRef: ElementRef;\n  SkinTonePickerRef: ElementRef<HTMLDivElement>;\n  SearchInputRef: ElementRef<HTMLInputElement>;\n  BodyRef: ElementRef<HTMLDivElement>;\n  CategoryNavigationRef: ElementRef<HTMLDivElement>;\n  VariationPickerRef: ElementRef<HTMLDivElement>;\n  ReactionsRef: ElementRef<HTMLUListElement>;\n};\n\nconst ElementRefContext = React.createContext<ElementRefs>({\n  AnchoredEmojiRef: React.createRef(),\n  BodyRef: React.createRef(),\n  CategoryNavigationRef: React.createRef(),\n  PickerMainRef: React.createRef(),\n  SearchInputRef: React.createRef(),\n  SkinTonePickerRef: React.createRef(),\n  VariationPickerRef: React.createRef(),\n  ReactionsRef: React.createRef()\n});\n\nfunction useElementRef() {\n  return React.useContext(ElementRefContext);\n}\n\nexport function usePickerMainRef() {\n  return useElementRef()['PickerMainRef'];\n}\n\nexport function useAnchoredEmojiRef() {\n  return useElementRef()['AnchoredEmojiRef'];\n}\n\nexport function useSetAnchoredEmojiRef(): (target: NullableElement) => void {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return (target: NullableElement) => {\n    if (target === null && AnchoredEmojiRef.current !== null) {\n      focusElement(AnchoredEmojiRef.current);\n    }\n\n    AnchoredEmojiRef.current = target;\n  };\n}\n\nexport function useBodyRef() {\n  return useElementRef()['BodyRef'];\n}\n\nexport function useReactionsRef() {\n  return useElementRef()['ReactionsRef'];\n}\n\nexport function useSearchInputRef() {\n  return useElementRef()['SearchInputRef'];\n}\n\nexport function useSkinTonePickerRef() {\n  return useElementRef()['SkinTonePickerRef'];\n}\n\nexport function useCategoryNavigationRef() {\n  return useElementRef()['CategoryNavigationRef'];\n}\n\nexport function useVariationPickerRef() {\n  return useElementRef()['VariationPickerRef'];\n}\n", "import { useCallback } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport {\n  categoryLabelHeight,\n  closestCategory,\n  closestScrollBody,\n  emojiDistanceFromScrollTop,\n  isEmojiBehindLabel,\n  NullableElement,\n  queryScrollBody\n} from './selectors';\n\nexport function scrollTo(root: NullableElement, top: number = 0) {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = top;\n  });\n}\n\nexport function scrollBy(root: NullableElement, by: number): void {\n  const $eprBody = queryScrollBody(root);\n\n  if (!$eprBody) {\n    return;\n  }\n\n  requestAnimationFrame(() => {\n    $eprBody.scrollTop = $eprBody.scrollTop + by;\n  });\n}\n\nexport function useScrollTo() {\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    (top: number) => {\n      requestAnimationFrame(() => {\n        if (BodyRef.current) {\n          BodyRef.current.scrollTop = top;\n        }\n      });\n    },\n    [BodyRef]\n  );\n}\n\nexport function scrollEmojiAboveLabel(emoji: NullableElement) {\n  if (!emoji || !isEmojiBehindLabel(emoji)) {\n    return;\n  }\n\n  if (emoji.closest(asSelectors(ClassNames.variationPicker))) {\n    return;\n  }\n\n  const scrollBody = closestScrollBody(emoji);\n  const by = emojiDistanceFromScrollTop(emoji);\n  scrollBy(scrollBody, -(categoryLabelHeight(closestCategory(emoji)) - by));\n}\n", "import {\n  elementCountInRow,\n  elementIndexInRow,\n  getElementInNextRow,\n  getElementInPrevRow,\n  getElementInRow,\n  hasNextRow,\n  rowNumber\n} from './elementPositionInRow';\nimport { focusElement } from './focusElement';\nimport { scrollEmojiAboveLabel } from './scrollTo';\nimport {\n  allVisibleEmojis,\n  closestCategory,\n  firstVisibleEmoji,\n  lastVisibleEmoji,\n  nextCategory,\n  nextVisibleEmoji,\n  NullableElement,\n  prevCategory,\n  prevVisibleEmoji,\n  closestCategoryContent\n} from './selectors';\n\nexport function focusFirstVisibleEmoji(parent: NullableElement) {\n  const emoji = firstVisibleEmoji(parent);\n  focusElement(emoji);\n  scrollEmojiAboveLabel(emoji);\n}\n\nexport function focusAndClickFirstVisibleEmoji(parent: NullableElement) {\n  const firstEmoji = firstVisibleEmoji(parent);\n\n  focusElement(firstEmoji);\n  firstEmoji?.click();\n}\n\nexport function focusLastVisibleEmoji(parent: NullableElement) {\n  focusElement(lastVisibleEmoji(parent));\n}\n\nexport function focusNextVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = nextVisibleEmoji(element);\n\n  if (!next) {\n    return focusFirstVisibleEmoji(nextCategory(element));\n  }\n\n  focusElement(next);\n  scrollEmojiAboveLabel(next);\n}\n\nexport function focusPrevVisibleEmoji(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const prev = prevVisibleEmoji(element);\n\n  if (!prev) {\n    return focusLastVisibleEmoji(prevCategory(element));\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowUp(\n  element: NullableElement,\n  exitUp: () => void\n) {\n  if (!element) {\n    return;\n  }\n\n  const prev = visibleEmojiOneRowUp(element);\n\n  if (!prev) {\n    return exitUp();\n  }\n\n  focusElement(prev);\n  scrollEmojiAboveLabel(prev);\n}\n\nexport function focusVisibleEmojiOneRowDown(element: NullableElement) {\n  if (!element) {\n    return;\n  }\n\n  const next = visibleEmojiOneRowDown(element);\n\n  return focusElement(next);\n}\n\nfunction visibleEmojiOneRowUp(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n\n  if (row === 0) {\n    const prevVisibleCategory = prevCategory(category);\n\n    if (!prevVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(prevVisibleCategory),\n      -1, // last row\n      countInRow,\n      indexInRow\n    );\n  }\n\n  return getElementInPrevRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n}\n\nfunction visibleEmojiOneRowDown(element: HTMLElement) {\n  if (!element) {\n    return null;\n  }\n\n  const categoryContent = closestCategoryContent(element);\n  const category = closestCategory(categoryContent);\n  const indexInRow = elementIndexInRow(categoryContent, element);\n  const row = rowNumber(categoryContent, element);\n  const countInRow = elementCountInRow(categoryContent, element);\n  if (!hasNextRow(categoryContent, element)) {\n    const nextVisibleCategory = nextCategory(category);\n\n    if (!nextVisibleCategory) {\n      return null;\n    }\n\n    return getElementInRow(\n      allVisibleEmojis(nextVisibleCategory),\n      0,\n      countInRow,\n      indexInRow\n    );\n  }\n\n  const itemInNextRow = getElementInNextRow(\n    allVisibleEmojis(categoryContent),\n    row,\n    countInRow,\n    indexInRow\n  );\n\n  return itemInNextRow;\n}\n", "import { useCallback } from 'react';\n\nimport {\n  useEmojiVariationPickerState,\n  useSkinToneFanOpenState\n} from '../components/context/PickerContext';\n\nexport function useCloseAllOpenToggles() {\n  const [variationPicker, setVariationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen, setSkinToneFanOpen] = useSkinToneFanOpenState();\n\n  const closeAllOpenToggles = useCallback(() => {\n    if (variationPicker) {\n      setVariationPicker(null);\n    }\n\n    if (skinToneFanOpen) {\n      setSkinToneFanOpen(false);\n    }\n  }, [\n    variationPicker,\n    skinToneFanOpen,\n    setVariationPicker,\n    setSkinToneFanOpen\n  ]);\n\n  return closeAllOpenToggles;\n}\n\nexport function useHasOpenToggles() {\n  const [variationPicker] = useEmojiVariationPickerState();\n  const [skinToneFanOpen] = useSkinToneFanOpenState();\n\n  return function hasOpenToggles() {\n    return !!variationPicker || skinToneFanOpen;\n  };\n}\n", "import { useEffect } from 'react';\n\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useDisallowMouseRef } from '../components/context/PickerContext';\n\nexport function useDisallowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function disallowMouseMove() {\n    DisallowMouseRef.current = true;\n  };\n}\n\nexport function useAllowMouseMove() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function allowMouseMove() {\n    DisallowMouseRef.current = false;\n  };\n}\n\nexport function useIsMouseDisallowed() {\n  const DisallowMouseRef = useDisallowMouseRef();\n  return function isMouseDisallowed() {\n    return DisallowMouseRef.current;\n  };\n}\n\nexport function useOnMouseMove() {\n  const BodyRef = useBodyRef();\n  const allowMouseMove = useAllowMouseMove();\n  const isMouseDisallowed = useIsMouseDisallowed();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    bodyRef?.addEventListener('mousemove', onMouseMove, {\n      passive: true\n    });\n\n    function onMouseMove() {\n      if (isMouseDisallowed()) {\n        allowMouseMove();\n      }\n    }\n    return () => {\n      bodyRef?.removeEventListener('mousemove', onMouseMove);\n    };\n  }, [BodyRef, allowMouseMove, isMouseDisallowed]);\n}\n", "import { useCallback } from 'react';\n\nimport { focusElement, focusFirstElementChild } from '../DomUtils/focusElement';\nimport {\n  useCategoryNavigationRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\n\nexport function useFocusSearchInput() {\n  const SearchInputRef = useSearchInputRef();\n\n  return useCallback(() => {\n    focusElement(SearchInputRef.current);\n  }, [SearchInputRef]);\n}\n\nexport function useFocusSkinTonePicker() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n\n  return useCallback(() => {\n    if (!SkinTonePickerRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(SkinTonePickerRef.current);\n  }, [SkinTonePickerRef]);\n}\n\nexport function useFocusCategoryNavigation() {\n  const CategoryNavigationRef = useCategoryNavigationRef();\n\n  return useCallback(() => {\n    if (!CategoryNavigationRef.current) {\n      return;\n    }\n\n    focusFirstElementChild(CategoryNavigationRef.current);\n  }, [CategoryNavigationRef]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport {\n  usePickerMainRef,\n  useSearchInputRef\n} from '../components/context/ElementRefContext';\nimport {\n  FilterState,\n  useFilterRef,\n  useSearchTermState\n} from '../components/context/PickerContext';\nimport { useSearchResultsConfig } from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiNames } from '../dataUtils/emojiSelectors';\n\nimport { useFocusSearchInput } from './useFocus';\n\nfunction useSetFilterRef() {\n  const filterRef = useFilterRef();\n\n  return function setFilter(\n    setter: FilterState | ((current: FilterState) => FilterState)\n  ): void {\n    if (typeof setter === 'function') {\n      return setFilter(setter(filterRef.current));\n    }\n\n    filterRef.current = setter;\n  };\n}\n\nexport function useClearSearch() {\n  const applySearch = useApplySearch();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n\n  return function clearSearch() {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = '';\n    }\n\n    applySearch('');\n    focusSearchInput();\n  };\n}\n\nexport function useAppendSearch() {\n  const SearchInputRef = useSearchInputRef();\n  const applySearch = useApplySearch();\n\n  return function appendSearch(str: string) {\n    if (SearchInputRef.current) {\n      SearchInputRef.current.value = `${SearchInputRef.current.value}${str}`;\n      applySearch(getNormalizedSearchTerm(SearchInputRef.current.value));\n    } else {\n      applySearch(getNormalizedSearchTerm(str));\n    }\n  };\n}\n\nexport function useFilter() {\n  const SearchInputRef = useSearchInputRef();\n  const filterRef = useFilterRef();\n  const setFilterRef = useSetFilterRef();\n  const applySearch = useApplySearch();\n\n  const [searchTerm] = useSearchTermState();\n  const statusSearchResults = getStatusSearchResults(\n    filterRef.current,\n    searchTerm\n  );\n\n  return {\n    onChange,\n    searchTerm,\n    SearchInputRef,\n    statusSearchResults\n  };\n\n  function onChange(inputValue: string) {\n    const filter = filterRef.current;\n\n    const nextValue = inputValue.toLowerCase();\n\n    if (filter?.[nextValue] || nextValue.length <= 1) {\n      return applySearch(nextValue);\n    }\n\n    const longestMatch = findLongestMatch(nextValue, filter);\n\n    if (!longestMatch) {\n      // Can we even get here?\n      // If so, we need to search among all emojis\n      return applySearch(nextValue);\n    }\n\n    setFilterRef(current =>\n      Object.assign(current, {\n        [nextValue]: filterEmojiObjectByKeyword(longestMatch, nextValue)\n      })\n    );\n    applySearch(nextValue);\n  }\n}\n\nfunction useApplySearch() {\n  const [, setSearchTerm] = useSearchTermState();\n  const PickerMainRef = usePickerMainRef();\n\n  return function applySearch(searchTerm: string) {\n    requestAnimationFrame(() => {\n      setSearchTerm(searchTerm ? searchTerm?.toLowerCase() : searchTerm).then(\n        () => {\n          scrollTo(PickerMainRef.current, 0);\n        }\n      );\n    });\n  };\n}\n\nfunction filterEmojiObjectByKeyword(\n  emojis: FilterDict,\n  keyword: string\n): FilterDict {\n  const filtered: FilterDict = {};\n\n  for (const unified in emojis) {\n    const emoji = emojis[unified];\n\n    if (hasMatch(emoji, keyword)) {\n      filtered[unified] = emoji;\n    }\n  }\n\n  return filtered;\n}\n\nfunction hasMatch(emoji: DataEmoji, keyword: string): boolean {\n  return emojiNames(emoji).some(name => name.includes(keyword));\n}\n\nexport function useIsEmojiFiltered(): (unified: string) => boolean {\n  const { current: filter } = useFilterRef();\n  const [searchTerm] = useSearchTermState();\n\n  return unified => isEmojiFilteredBySearchTerm(unified, filter, searchTerm);\n}\n\nfunction isEmojiFilteredBySearchTerm(\n  unified: string,\n  filter: FilterState,\n  searchTerm: string\n): boolean {\n  if (!filter || !searchTerm) {\n    return false;\n  }\n\n  return !filter[searchTerm]?.[unified];\n}\n\nexport type FilterDict = Record<string, DataEmoji>;\n\nfunction findLongestMatch(\n  keyword: string,\n  dict: Record<string, FilterDict> | null\n): FilterDict | null {\n  if (!dict) {\n    return null;\n  }\n\n  if (dict[keyword]) {\n    return dict[keyword];\n  }\n\n  const longestMatchingKey = Object.keys(dict)\n    .sort((a, b) => b.length - a.length)\n    .find(key => keyword.includes(key));\n\n  if (longestMatchingKey) {\n    return dict[longestMatchingKey];\n  }\n\n  return null;\n}\n\nexport function getNormalizedSearchTerm(str: string): string {\n  if (!str || typeof str !== 'string') {\n    return '';\n  }\n\n  return str.trim().toLowerCase();\n}\n\nfunction getStatusSearchResults(\n  filterState: FilterState,\n  searchTerm: string\n): string {\n  if (!filterState?.[searchTerm]) return '';\n\n  const searchResultsCount =\n    Object.entries(filterState?.[searchTerm])?.length || 0;\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useSearchResultsConfig(searchResultsCount);\n}\n", "import { emojiFromElement, NullableElement } from '../DomUtils/selectors';\nimport { useSetAnchoredEmojiRef } from '../components/context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../components/context/PickerContext';\n\nexport default function useSetVariationPicker() {\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n\n  return function setVariationPicker(element: NullableElement) {\n    const [emoji] = emojiFromElement(element);\n\n    if (emoji) {\n      setAnchoredEmojiRef(element);\n      setEmojiVariationPicker(emoji);\n    }\n  };\n}\n", "import { useSkinTonePickerLocationConfig } from '../config/useConfig';\nimport { SkinTonePickerLocation } from '../types/exposedTypes';\n\nexport function useShouldShowSkinTonePicker() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return function shouldShowSkinTonePicker(location: SkinTonePickerLocation) {\n    return skinTonePickerLocationConfig === location;\n  };\n}\n\nexport function useIsSkinToneInSearch() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.SEARCH;\n}\n\nexport function useIsSkinToneInPreview() {\n  const skinTonePickerLocationConfig = useSkinTonePickerLocationConfig();\n\n  return skinTonePickerLocationConfig === SkinTonePickerLocation.PREVIEW;\n}\n", "import { useCallback, useEffect, useMemo } from 'react';\n\nimport { hasNextElementSibling } from '../DomUtils/elementPositionInRow';\nimport {\n  focusNextElementSibling,\n  focusPrevElementSibling\n} from '../DomUtils/focusElement';\nimport { getActiveElement } from '../DomUtils/getActiveElement';\nimport {\n  focusAndClickFirstVisibleEmoji,\n  focusFirstVisibleEmoji,\n  focusNextVisibleEmoji,\n  focusPrevVisibleEmoji,\n  focusVisibleEmojiOneRowDown,\n  focusVisibleEmojiOneRowUp\n} from '../DomUtils/keyboardNavigation';\nimport { useScrollTo } from '../DomUtils/scrollTo';\nimport { buttonFromTarget } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  useCategoryNavigationRef,\n  usePickerMainRef,\n  useSearchInputRef,\n  useSkinTonePickerRef\n} from '../components/context/ElementRefContext';\nimport { useSkinToneFanOpenState } from '../components/context/PickerContext';\nimport { useSearchDisabledConfig } from '../config/useConfig';\n\nimport {\n  useCloseAllOpenToggles,\n  useHasOpenToggles\n} from './useCloseAllOpenToggles';\nimport { useDisallowMouseMove } from './useDisallowMouseMove';\nimport { useAppendSearch, useClearSearch } from './useFilter';\nimport {\n  useFocusCategoryNavigation,\n  useFocusSearchInput,\n  useFocusSkinTonePicker\n} from './useFocus';\nimport useIsSearchMode from './useIsSearchMode';\nimport useSetVariationPicker from './useSetVariationPicker';\nimport {\n  useIsSkinToneInPreview,\n  useIsSkinToneInSearch\n} from './useShouldShowSkinTonePicker';\n\nenum KeyboardEvents {\n  ArrowDown = 'ArrowDown',\n  ArrowUp = 'ArrowUp',\n  ArrowLeft = 'ArrowLeft',\n  ArrowRight = 'ArrowRight',\n  Escape = 'Escape',\n  Enter = 'Enter',\n  Space = ' '\n}\n\nexport function useKeyboardNavigation() {\n  usePickerMainKeyboardEvents();\n  useSearchInputKeyboardEvents();\n  useSkinTonePickerKeyboardEvents();\n  useCategoryNavigationKeyboardEvents();\n  useBodyKeyboardEvents();\n}\n\nfunction usePickerMainKeyboardEvents() {\n  const PickerMainRef = usePickerMainRef();\n  const clearSearch = useClearSearch();\n  const scrollTo = useScrollTo();\n  const SearchInputRef = useSearchInputRef();\n  const focusSearchInput = useFocusSearchInput();\n  const hasOpenToggles = useHasOpenToggles();\n  const disallowMouseMove = useDisallowMouseMove();\n\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        disallowMouseMove();\n        switch (key) {\n          // eslint-disable-next-line no-fallthrough\n          case KeyboardEvents.Escape:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              return;\n            }\n            clearSearch();\n            scrollTo(0);\n            focusSearchInput();\n            break;\n        }\n      },\n    [\n      scrollTo,\n      clearSearch,\n      closeAllOpenToggles,\n      focusSearchInput,\n      hasOpenToggles,\n      disallowMouseMove\n    ]\n  );\n\n  useEffect(() => {\n    const current = PickerMainRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, scrollTo, onKeyDown]);\n}\n\nfunction useSearchInputKeyboardEvents() {\n  const focusSkinTonePicker = useFocusSkinTonePicker();\n  const PickerMainRef = usePickerMainRef();\n  const BodyRef = useBodyRef();\n  const SearchInputRef = useSearchInputRef();\n  const [, setSkinToneFanOpenState] = useSkinToneFanOpenState();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            if (!isSkinToneInSearch) {\n              return;\n            }\n            event.preventDefault();\n            setSkinToneFanOpenState(true);\n            focusSkinTonePicker();\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            goDownFromSearchInput();\n            break;\n          case KeyboardEvents.Enter:\n            event.preventDefault();\n            focusAndClickFirstVisibleEmoji(BodyRef.current);\n            break;\n        }\n      },\n    [\n      focusSkinTonePicker,\n      goDownFromSearchInput,\n      setSkinToneFanOpenState,\n      BodyRef,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SearchInputRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [PickerMainRef, SearchInputRef, onKeyDown]);\n}\n\nfunction useSkinTonePickerKeyboardEvents() {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const focusSearchInput = useFocusSearchInput();\n  const SearchInputRef = useSearchInputRef();\n  const goDownFromSearchInput = useGoDownFromSearchInput();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        if (isSkinToneInSearch) {\n          switch (key) {\n            case KeyboardEvents.ArrowLeft:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowRight:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (isOpen) {\n                setIsOpen(false);\n              }\n              goDownFromSearchInput();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n\n        if (isSkinToneInPreview) {\n          switch (key) {\n            case KeyboardEvents.ArrowUp:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusNextSkinTone(focusSearchInput);\n              break;\n            case KeyboardEvents.ArrowDown:\n              event.preventDefault();\n              if (!isOpen) {\n                return focusSearchInput();\n              }\n              focusPrevSkinTone();\n              break;\n            default:\n              onType(event);\n              break;\n          }\n        }\n      },\n    [\n      isOpen,\n      focusSearchInput,\n      setIsOpen,\n      goDownFromSearchInput,\n      onType,\n      isSkinToneInPreview,\n      isSkinToneInSearch\n    ]\n  );\n\n  useEffect(() => {\n    const current = SkinTonePickerRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [SkinTonePickerRef, SearchInputRef, isOpen, onKeyDown]);\n}\n\nfunction useCategoryNavigationKeyboardEvents() {\n  const focusSearchInput = useFocusSearchInput();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const BodyRef = useBodyRef();\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        switch (key) {\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            focusSearchInput();\n            break;\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevElementSibling(getActiveElement());\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            focusFirstVisibleEmoji(BodyRef.current);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [BodyRef, focusSearchInput, onType]\n  );\n\n  useEffect(() => {\n    const current = CategoryNavigationRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [CategoryNavigationRef, BodyRef, onKeyDown]);\n}\n\nfunction useBodyKeyboardEvents() {\n  const BodyRef = useBodyRef();\n  const goUpFromBody = useGoUpFromBody();\n  const setVariationPicker = useSetVariationPicker();\n  const hasOpenToggles = useHasOpenToggles();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  const onType = useOnType();\n\n  const onKeyDown = useMemo(\n    () =>\n      // eslint-disable-next-line complexity\n      function onKeyDown(event: KeyboardEvent) {\n        const { key } = event;\n\n        const activeElement = buttonFromTarget(getActiveElement());\n\n        switch (key) {\n          case KeyboardEvents.ArrowRight:\n            event.preventDefault();\n            focusNextVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowLeft:\n            event.preventDefault();\n            focusPrevVisibleEmoji(activeElement);\n            break;\n          case KeyboardEvents.ArrowDown:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowDown(activeElement);\n            break;\n          case KeyboardEvents.ArrowUp:\n            event.preventDefault();\n            if (hasOpenToggles()) {\n              closeAllOpenToggles();\n              break;\n            }\n            focusVisibleEmojiOneRowUp(activeElement, goUpFromBody);\n            break;\n          case KeyboardEvents.Space:\n            event.preventDefault();\n            setVariationPicker(event.target as HTMLElement);\n            break;\n          default:\n            onType(event);\n            break;\n        }\n      },\n    [\n      goUpFromBody,\n      onType,\n      setVariationPicker,\n      hasOpenToggles,\n      closeAllOpenToggles\n    ]\n  );\n\n  useEffect(() => {\n    const current = BodyRef.current;\n\n    if (!current) {\n      return;\n    }\n\n    current.addEventListener('keydown', onKeyDown);\n\n    return () => {\n      current.removeEventListener('keydown', onKeyDown);\n    };\n  }, [BodyRef, onKeyDown]);\n}\n\nfunction useGoDownFromSearchInput() {\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n  const BodyRef = useBodyRef();\n\n  return useCallback(\n    function goDownFromSearchInput() {\n      if (isSearchMode) {\n        return focusFirstVisibleEmoji(BodyRef.current);\n      }\n      return focusCategoryNavigation();\n    },\n    [BodyRef, focusCategoryNavigation, isSearchMode]\n  );\n}\n\nfunction useGoUpFromBody() {\n  const focusSearchInput = useFocusSearchInput();\n  const focusCategoryNavigation = useFocusCategoryNavigation();\n  const isSearchMode = useIsSearchMode();\n\n  return useCallback(\n    function goUpFromEmoji() {\n      if (isSearchMode) {\n        return focusSearchInput();\n      }\n      return focusCategoryNavigation();\n    },\n    [focusSearchInput, isSearchMode, focusCategoryNavigation]\n  );\n}\n\nfunction focusNextSkinTone(exitLeft: () => void) {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  if (!hasNextElementSibling(currentSkinTone)) {\n    exitLeft();\n  }\n\n  focusNextElementSibling(currentSkinTone);\n}\n\nfunction focusPrevSkinTone() {\n  const currentSkinTone = getActiveElement();\n\n  if (!currentSkinTone) {\n    return;\n  }\n\n  focusPrevElementSibling(currentSkinTone);\n}\n\nfunction useOnType() {\n  const appendSearch = useAppendSearch();\n  const focusSearchInput = useFocusSearchInput();\n  const searchDisabled = useSearchDisabledConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  return function onType(event: KeyboardEvent) {\n    const { key } = event;\n\n    if (hasModifier(event) || searchDisabled) {\n      return;\n    }\n\n    if (key.match(/(^[a-zA-Z0-9]$){1}/)) {\n      event.preventDefault();\n      closeAllOpenToggles();\n      focusSearchInput();\n      appendSearch(key);\n    }\n  };\n}\n\nfunction hasModifier(event: KeyboardEvent): boolean {\n  const { metaKey, ctrlKey, altKey } = event;\n\n  return metaKey || ctrlKey || altKey;\n}\n", "import { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified, emojiVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nexport function preloadEmoji(\n  getEmojiUrl: GetEmojiUrl,\n  emoji: undefined | DataEmoji,\n  emojiStyle: EmojiStyle\n): void {\n  if (!emoji) {\n    return;\n  }\n\n  if (emojiStyle === EmojiStyle.NATIVE) {\n    return;\n  }\n\n  const unified = emojiUnified(emoji);\n\n  if (preloadedEmojs.has(unified)) {\n    return;\n  }\n\n  emojiVariations(emoji).forEach((variation) => {\n    const emojiUrl = getEmojiUrl(variation, emojiStyle);\n    preloadImage(emojiUrl);\n  });\n\n  preloadedEmojs.add(unified);\n}\n\nexport const preloadedEmojs: Set<string> = new Set();\n\nfunction preloadImage(url: string): void {\n  const image = new Image();\n  image.src = url;\n}\n", "import { useEffect } from 'react';\n\nimport { buttonFromTarget, emojiFromElement } from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { useEmojiStyleConfig, useGetEmojiUrlConfig } from '../config/useConfig';\nimport { emojiHasVariations } from '../dataUtils/emojiSelectors';\nimport { EmojiStyle } from '../types/exposedTypes';\n\nimport { preloadEmoji } from './preloadEmoji';\n\nexport function useOnFocus() {\n  const BodyRef = useBodyRef();\n  const emojiStyle = useEmojiStyleConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEffect(() => {\n    if (emojiStyle === EmojiStyle.NATIVE) {\n      return;\n    }\n\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('focusin', onFocus);\n\n    return () => {\n      bodyRef?.removeEventListener('focusin', onFocus);\n    };\n\n    function onFocus(event: FocusEvent) {\n      const button = buttonFromTarget(event.target as HTMLElement);\n\n      if (!button) {\n        return;\n      }\n\n      const [emoji] = emojiFromElement(button);\n\n      if (!emoji) {\n        return;\n      }\n\n      if (emojiHasVariations(emoji)) {\n        preloadEmoji(getEmojiUrl, emoji, emojiStyle);\n      }\n    }\n  }, [BodyRef, emojiStyle, getEmojiUrl]);\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useClassNameConfig,\n  useStyleConfig,\n  useThemeConfig\n} from '../../config/useConfig';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';\nimport { useOnFocus } from '../../hooks/useOnFocus';\nimport { Theme } from '../../types/exposedTypes';\nimport { usePickerMainRef } from '../context/ElementRefContext';\nimport {\n  PickerContextProvider,\n  useReactionsModeState\n} from '../context/PickerContext';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n}>;\n\nexport const DEFAULT_LABEL_HEIGHT = 40;\n\nexport default function PickerMain({ children }: Props) {\n  return (\n    <PickerContextProvider>\n      <PickerRootElement>{children}</PickerRootElement>\n    </PickerContextProvider>\n  );\n}\n\ntype RootProps = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n  children: React.ReactNode;\n}>;\n\nfunction PickerRootElement({ children }: RootProps) {\n  const [reactionsMode] = useReactionsModeState();\n  const theme = useThemeConfig();\n  const searchModeActive = useIsSearchMode();\n  const PickerMainRef = usePickerMainRef();\n  const className = useClassNameConfig();\n  const style = useStyleConfig();\n\n  useKeyboardNavigation();\n  useOnFocus();\n\n  const { width, height, ...styleProps } = style || {};\n\n  return (\n    <aside\n      className={cx(\n        styles.main,\n        styles.baseVariables,\n        theme === Theme.DARK && styles.darkTheme,\n        theme === Theme.AUTO && styles.autoThemeDark,\n        {\n          [ClassNames.searchActive]: searchModeActive\n        },\n        reactionsMode && styles.reactionsMenu,\n        className\n      )}\n      ref={PickerMainRef}\n      style={{\n        ...styleProps,\n        ...(!reactionsMode && { height, width })\n      }}\n    >\n      {children}\n    </aside>\n  );\n}\n\nconst DarkTheme = {\n  '--epr-emoji-variation-picker-bg-color':\n    'var(--epr-dark-emoji-variation-picker-bg-color)',\n  '--epr-hover-bg-color-reduced-opacity':\n    'var(--epr-dark-hover-bg-color-reduced-opacity)',\n  '--epr-highlight-color': 'var(--epr-dark-highlight-color)',\n  '--epr-text-color': 'var(--epr-dark-text-color)',\n  '--epr-hover-bg-color': 'var(--epr-dark-hover-bg-color)',\n  '--epr-focus-bg-color': 'var(--epr-dark-focus-bg-color)',\n  '--epr-search-input-bg-color': 'var(--epr-dark-search-input-bg-color)',\n  '--epr-category-label-bg-color': 'var(--epr-dark-category-label-bg-color)',\n  '--epr-picker-border-color': 'var(--epr-dark-picker-border-color)',\n  '--epr-bg-color': 'var(--epr-dark-bg-color)',\n  '--epr-reactions-bg-color': 'var(--epr-dark-reactions-bg-color)',\n  '--epr-search-input-bg-color-active':\n    'var(--epr-dark-search-input-bg-color-active)',\n  '--epr-emoji-variation-indicator-color':\n    'var(--epr-dark-emoji-variation-indicator-color)',\n  '--epr-category-icon-active-color':\n    'var(--epr-dark-category-icon-active-color)',\n  '--epr-skin-tone-picker-menu-color':\n    'var(--epr-dark-skin-tone-picker-menu-color)'\n};\n\nconst styles = stylesheet.create({\n  main: {\n    '.': ['epr-main', ClassNames.emojiPicker],\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    borderWidth: '1px',\n    borderStyle: 'solid',\n    borderRadius: 'var(--epr-picker-border-radius)',\n    borderColor: 'var(--epr-picker-border-color)',\n    backgroundColor: 'var(--epr-bg-color)',\n    overflow: 'hidden',\n    transition: 'all 0.3s ease-in-out, background-color 0.1s ease-in-out',\n    '*': {\n      boxSizing: 'border-box',\n      fontFamily: 'sans-serif'\n    }\n  },\n  baseVariables: {\n    '--': {\n      '--epr-highlight-color': '#007aeb',\n      '--epr-hover-bg-color': '#f1f8ff',\n      '--epr-hover-bg-color-reduced-opacity': '#f1f8ff80',\n      '--epr-focus-bg-color': '#e0f0ff',\n      '--epr-text-color': '#858585',\n      '--epr-search-input-bg-color': '#f6f6f6',\n      '--epr-picker-border-color': '#e7e7e7',\n      '--epr-bg-color': '#fff',\n      '--epr-reactions-bg-color': '#ffffff90',\n      '--epr-category-icon-active-color': '#6aa8de',\n      '--epr-skin-tone-picker-menu-color': '#ffffff95',\n\n      '--epr-horizontal-padding': '10px',\n\n      '--epr-picker-border-radius': '8px',\n\n      /* Header */\n      '--epr-search-border-color': 'var(--epr-highlight-color)',\n      '--epr-header-padding': '15px var(--epr-horizontal-padding)',\n\n      /* Skin Tone Picker */\n      '--epr-active-skin-tone-indicator-border-color':\n        'var(--epr-highlight-color)',\n      '--epr-active-skin-hover-color': 'var(--epr-hover-bg-color)',\n\n      /* Search */\n      '--epr-search-input-bg-color-active': 'var(--epr-search-input-bg-color)',\n      '--epr-search-input-padding': '0 30px',\n      '--epr-search-input-border-radius': '8px',\n      '--epr-search-input-height': '40px',\n      '--epr-search-input-text-color': 'var(--epr-text-color)',\n      '--epr-search-input-placeholder-color': 'var(--epr-text-color)',\n      '--epr-search-bar-inner-padding': 'var(--epr-horizontal-padding)',\n\n      /*  Category Navigation */\n      '--epr-category-navigation-button-size': '30px',\n\n      /* Variation Picker */\n      '--epr-emoji-variation-picker-height': '45px',\n      '--epr-emoji-variation-picker-bg-color': 'var(--epr-bg-color)',\n\n      /*  Preview */\n      '--epr-preview-height': '70px',\n      '--epr-preview-text-size': '14px',\n      '--epr-preview-text-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-preview-border-color': 'var(--epr-picker-border-color)',\n      '--epr-preview-text-color': 'var(--epr-text-color)',\n\n      /* Category */\n      '--epr-category-padding': '0 var(--epr-horizontal-padding)',\n\n      /*  Category Label */\n      '--epr-category-label-bg-color': '#ffffffe6',\n      '--epr-category-label-text-color': 'var(--epr-text-color)',\n      '--epr-category-label-padding': '0 var(--epr-horizontal-padding)',\n      '--epr-category-label-height': `${DEFAULT_LABEL_HEIGHT}px`,\n\n      /*  Emoji */\n      '--epr-emoji-size': '30px',\n      '--epr-emoji-padding': '5px',\n      '--epr-emoji-fullsize':\n        'calc(var(--epr-emoji-size) + var(--epr-emoji-padding) * 2)',\n      '--epr-emoji-hover-color': 'var(--epr-hover-bg-color)',\n      '--epr-emoji-variation-indicator-color': 'var(--epr-picker-border-color)',\n      '--epr-emoji-variation-indicator-color-hover': 'var(--epr-text-color)',\n\n      /* Z-Index */\n      '--epr-header-overlay-z-index': '3',\n      '--epr-emoji-variations-indictator-z-index': '1',\n      '--epr-category-label-z-index': '2',\n      '--epr-skin-variation-picker-z-index': '5',\n      '--epr-preview-z-index': '6',\n\n      /* Dark Theme Variables */\n      '--epr-dark': '#000',\n      '--epr-dark-emoji-variation-picker-bg-color': 'var(--epr-dark)',\n      '--epr-dark-highlight-color': '#c0c0c0',\n      '--epr-dark-text-color': 'var(--epr-highlight-color)',\n      '--epr-dark-hover-bg-color': '#363636f6',\n      '--epr-dark-hover-bg-color-reduced-opacity': '#36363680',\n      '--epr-dark-focus-bg-color': '#474747',\n      '--epr-dark-search-input-bg-color': '#333333',\n      '--epr-dark-category-label-bg-color': '#222222e6',\n      '--epr-dark-picker-border-color': '#151617',\n      '--epr-dark-bg-color': '#222222',\n      '--epr-dark-reactions-bg-color': '#22222290',\n      '--epr-dark-search-input-bg-color-active': 'var(--epr-dark)',\n      '--epr-dark-emoji-variation-indicator-color': '#444',\n      '--epr-dark-category-icon-active-color': '#3271b7',\n      '--epr-dark-skin-tone-picker-menu-color': '#22222295'\n    }\n  },\n  autoThemeDark: {\n    '.': ClassNames.autoTheme,\n    '@media (prefers-color-scheme: dark)': {\n      '--': DarkTheme\n    }\n  },\n  darkTheme: {\n    '.': ClassNames.darkTheme,\n    '--': DarkTheme\n  },\n  reactionsMenu: {\n    '.': 'epr-reactions',\n    height: '50px',\n    display: 'inline-flex',\n    backgroundColor: 'var(--epr-reactions-bg-color)',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(8px)',\n    '--': {\n      '--epr-picker-border-radius': '50px'\n    }\n  }\n});\n", "import { DEFAULT_LABEL_HEIGHT } from '../components/main/PickerMain';\n\nimport { ClassNames, asSelectors } from './classNames';\nimport { NullableElement } from './selectors';\n\nexport function elementCountInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const parentWidth = parent.getBoundingClientRect().width;\n  const elementWidth = element.getBoundingClientRect().width;\n  return Math.floor(parentWidth / elementWidth);\n}\n\nexport function elementIndexInRow(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementWidth = element.getBoundingClientRect().width;\n  const elementLeft = element.getBoundingClientRect().left;\n  const parentLeft = parent.getBoundingClientRect().left;\n\n  return Math.floor((elementLeft - parentLeft) / elementWidth);\n}\n\nexport function rowNumber(\n  parent: NullableElement,\n  element: NullableElement\n): number {\n  if (!parent || !element) {\n    return 0;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  return Math.round((elementTop - parentTop) / elementHeight);\n}\n\nexport function hasNextRow(\n  parent: NullableElement,\n  element: NullableElement\n): boolean {\n  if (!parent || !element) {\n    return false;\n  }\n\n  const elementHeight = element.getBoundingClientRect().height;\n  const elementTop = element.getBoundingClientRect().top;\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentHeight = parent.getBoundingClientRect().height;\n\n  return Math.round(elementTop - parentTop + elementHeight) < parentHeight;\n}\n\nfunction getRowElements(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number\n): HTMLElement[] {\n  if (row === -1) {\n    const lastRow = Math.floor((elements.length - 1) / elementsInRow);\n    const firstElementIndex = lastRow * elementsInRow;\n    const lastElementIndex = elements.length - 1;\n    return elements.slice(firstElementIndex, lastElementIndex + 1);\n  }\n\n  return elements.slice(row * elementsInRow, (row + 1) * elementsInRow);\n}\n\nfunction getNextRowElements(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number\n): HTMLElement[] {\n  const nextRow = currentRow + 1;\n\n  if (nextRow * elementsInRow > allElements.length) {\n    return [];\n  }\n\n  return getRowElements(allElements, nextRow, elementsInRow);\n}\n\nexport function getElementInRow(\n  elements: HTMLElement[],\n  row: number,\n  elementsInRow: number,\n  indexInRow: number\n): NullableElement {\n  const rowElements = getRowElements(elements, row, elementsInRow);\n  // get element, default to last\n  return rowElements[indexInRow] || rowElements[rowElements.length - 1] || null;\n}\n\nexport function getElementInNextRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const nextRowElements = getNextRowElements(\n    allElements,\n    currentRow,\n    elementsInRow\n  );\n\n  // return item in index, or last item in row\n  return (\n    nextRowElements[index] ||\n    nextRowElements[nextRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function getElementInPrevRow(\n  allElements: HTMLElement[],\n  currentRow: number,\n  elementsInRow: number,\n  index: number\n): NullableElement {\n  const prevRowElements = getRowElements(\n    allElements,\n    currentRow - 1,\n    elementsInRow\n  );\n\n  // default to last\n  return (\n    prevRowElements[index] ||\n    prevRowElements[prevRowElements.length - 1] ||\n    null\n  );\n}\n\nexport function firstVisibleElementInContainer(\n  parent: NullableElement,\n  elements: HTMLElement[],\n  maxVisibilityDiffThreshold = 0\n): NullableElement {\n  if (!parent || !elements.length) {\n    return null;\n  }\n\n  const parentTop = parent.getBoundingClientRect().top;\n  const parentBottom = parent.getBoundingClientRect().bottom;\n  const parentTopWithLabel = parentTop + getLabelHeight(parent);\n\n  const visibleElements = elements.find(element => {\n    const elementTop = element.getBoundingClientRect().top;\n    const elementBottom = element.getBoundingClientRect().bottom;\n    const maxVisibilityDiffPixels =\n      element.clientHeight * maxVisibilityDiffThreshold;\n\n    const elementTopWithAllowedDiff = elementTop + maxVisibilityDiffPixels;\n    const elementBottomWithAllowedDiff =\n      elementBottom - maxVisibilityDiffPixels;\n\n    if (elementTopWithAllowedDiff < parentTopWithLabel) {\n      return false;\n    }\n\n    return (\n      (elementTopWithAllowedDiff >= parentTop &&\n        elementTopWithAllowedDiff <= parentBottom) ||\n      (elementBottomWithAllowedDiff >= parentTop &&\n        elementBottomWithAllowedDiff <= parentBottom)\n    );\n  });\n\n  return visibleElements || null;\n}\n\nexport function hasNextElementSibling(element: HTMLElement) {\n  return !!element.nextElementSibling;\n}\n\nfunction getLabelHeight(parentNode: HTMLElement) {\n  const labels = Array.from(\n    parentNode.querySelectorAll(asSelectors(ClassNames.label))\n  );\n\n  for (const label of labels) {\n    const height = label.getBoundingClientRect().height;\n    // return height if label is not hidden\n    if (height > 0) {\n      return height;\n    }\n  }\n\n  return DEFAULT_LABEL_HEIGHT;\n}\n", "import { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  emojiByUnified,\n  unifiedWithoutSkinTone\n} from '../dataUtils/emojiSelectors';\n\nimport { asSelectors, ClassNames } from './classNames';\nimport { firstVisibleElementInContainer } from './elementPositionInRow';\n\nexport type NullableElement = HTMLElement | null;\n\nexport const EmojiButtonSelector = `button${asSelectors(ClassNames.emoji)}`;\nexport const VisibleEmojiSelector = [\n  EmojiButtonSelector,\n  asSelectors(ClassNames.visible),\n  `:not(${asSelectors(ClassNames.hidden)})`\n].join('');\n\nexport function buttonFromTarget(\n  emojiElement: NullableElement\n): HTMLButtonElement | null {\n  return emojiElement?.closest(EmojiButtonSelector) ?? null;\n}\n\nexport function isEmojiButton(element: NullableElement): boolean {\n  if (!element) {\n    return false;\n  }\n\n  return element.matches(EmojiButtonSelector);\n}\n\nexport function emojiFromElement(\n  element: NullableElement\n): [DataEmoji, string] | [] {\n  const originalUnified = originalUnifiedFromEmojiElement(element);\n  const unified = unifiedFromEmojiElement(element);\n\n  if (!originalUnified) {\n    return [];\n  }\n\n  const emoji = emojiByUnified(unified ?? originalUnified);\n\n  if (!emoji) {\n    return [];\n  }\n\n  return [emoji, unified as string];\n}\n\nexport function isEmojiElement(element: NullableElement): boolean {\n  return Boolean(\n    element?.matches(EmojiButtonSelector) ||\n      element?.parentElement?.matches(EmojiButtonSelector)\n  );\n}\n\nexport function categoryLabelFromCategory(\n  category: NullableElement\n): NullableElement {\n  return category?.querySelector(asSelectors(ClassNames.label)) ?? null;\n}\n\nexport function closestCategoryLabel(\n  element: NullableElement\n): NullableElement {\n  const category = closestCategory(element);\n  return categoryLabelFromCategory(category);\n}\n\nexport function elementHeight(element: NullableElement): number {\n  return element?.clientHeight ?? 0;\n}\n\nexport function emojiTrueOffsetTop(element: NullableElement): number {\n  if (!element) {\n    return 0;\n  }\n\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  // compensate for the label height\n  const labelHeight = categoryLabelHeight(category);\n\n  return elementOffsetTop(button) + elementOffsetTop(category) + labelHeight;\n}\n\nexport function categoryLabelHeight(category: NullableElement): number {\n  if (!category) {\n    return 0;\n  }\n\n  const categoryWithoutLabel = category.querySelector(\n    asSelectors(ClassNames.categoryContent)\n  );\n\n  return (\n    (category?.clientHeight ?? 0) - (categoryWithoutLabel?.clientHeight ?? 0)\n  );\n}\n\nexport function isEmojiBehindLabel(emoji: NullableElement): boolean {\n  if (!emoji) {\n    return false;\n  }\n\n  return (\n    emojiDistanceFromScrollTop(emoji) <\n    categoryLabelHeight(closestCategory(emoji))\n  );\n}\n\nexport function queryScrollBody(root: NullableElement): NullableElement {\n  if (!root) return null;\n\n  return root.matches(asSelectors(ClassNames.scrollBody))\n    ? root\n    : root.querySelector(asSelectors(ClassNames.scrollBody));\n}\n\nexport function emojiDistanceFromScrollTop(emoji: NullableElement): number {\n  if (!emoji) {\n    return 0;\n  }\n\n  return emojiTrueOffsetTop(emoji) - (closestScrollBody(emoji)?.scrollTop ?? 0);\n}\n\nexport function closestScrollBody(element: NullableElement): NullableElement {\n  if (!element) {\n    return null;\n  }\n\n  return element.closest(asSelectors(ClassNames.scrollBody)) ?? null;\n}\n\nexport function emojiTruOffsetLeft(element: NullableElement): number {\n  const button = buttonFromTarget(element);\n  const category = closestCategory(button);\n\n  return elementOffsetLeft(button) + elementOffsetLeft(category);\n}\n\nfunction elementOffsetTop(element: NullableElement): number {\n  return element?.offsetTop ?? 0;\n}\n\nfunction elementOffsetLeft(element: NullableElement): number {\n  return element?.offsetLeft ?? 0;\n}\n\nexport function unifiedFromEmojiElement(emoji: NullableElement): string | null {\n  return elementDataSetKey(buttonFromTarget(emoji), 'unified') ?? null;\n}\n\nexport function originalUnifiedFromEmojiElement(\n  emoji: NullableElement\n): string | null {\n  const unified = unifiedFromEmojiElement(emoji);\n\n  if (unified) {\n    return unifiedWithoutSkinTone(unified);\n  }\n  return null;\n}\n\nexport function allUnifiedFromEmojiElement(\n  emoji: NullableElement\n): { unified: string | null; originalUnified: string | null } {\n  if (!emoji) {\n    return {\n      unified: null,\n      originalUnified: null\n    };\n  }\n\n  return {\n    unified: unifiedFromEmojiElement(emoji),\n    originalUnified: originalUnifiedFromEmojiElement(emoji)\n  };\n}\n\nfunction elementDataSetKey(\n  element: NullableElement,\n  key: string\n): string | null {\n  return elementDataSet(element)[key] ?? null;\n}\n\nfunction elementDataSet(element: NullableElement): DOMStringMap {\n  return element?.dataset ?? {};\n}\n\nexport function isVisibleEmoji(element: HTMLElement) {\n  return element.classList.contains(ClassNames.visible);\n}\n\nexport function isHidden(element: NullableElement) {\n  if (!element) return true;\n\n  return element.classList.contains(ClassNames.hidden);\n}\n\nexport function allVisibleEmojis(parent: NullableElement) {\n  if (!parent) {\n    return [];\n  }\n\n  return Array.from(\n    parent.querySelectorAll(VisibleEmojiSelector)\n  ) as HTMLElement[];\n}\n\nexport function lastVisibleEmoji(element: NullableElement): NullableElement {\n  if (!element) return null;\n\n  const allEmojis = allVisibleEmojis(element);\n  const [last] = allEmojis.slice(-1);\n  if (!last) {\n    return null;\n  }\n\n  if (!isVisibleEmoji(last)) {\n    return prevVisibleEmoji(last);\n  }\n\n  return last;\n}\n\nexport function nextVisibleEmoji(element: HTMLElement): NullableElement {\n  const next = element.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return firstVisibleEmoji(nextCategory(element));\n  }\n\n  if (!isVisibleEmoji(next)) {\n    return nextVisibleEmoji(next);\n  }\n\n  return next;\n}\n\nexport function prevVisibleEmoji(element: HTMLElement): NullableElement {\n  const prev = element.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return lastVisibleEmoji(prevCategory(element));\n  }\n\n  if (!isVisibleEmoji(prev)) {\n    return prevVisibleEmoji(prev);\n  }\n\n  return prev;\n}\n\nexport function firstVisibleEmoji(parent: NullableElement) {\n  if (!parent) {\n    return null;\n  }\n\n  const allEmojis = allVisibleEmojis(parent);\n\n  return firstVisibleElementInContainer(parent, allEmojis, 0.1);\n}\n\nexport function prevCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const prev = category.previousElementSibling as HTMLElement;\n\n  if (!prev) {\n    return null;\n  }\n\n  if (isHidden(prev)) {\n    return prevCategory(prev);\n  }\n\n  return prev;\n}\n\nexport function nextCategory(element: NullableElement): NullableElement {\n  const category = closestCategory(element);\n\n  if (!category) {\n    return null;\n  }\n\n  const next = category.nextElementSibling as HTMLElement;\n\n  if (!next) {\n    return null;\n  }\n\n  if (isHidden(next)) {\n    return nextCategory(next);\n  }\n\n  return next;\n}\n\nexport function closestCategory(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(asSelectors(ClassNames.category)) as HTMLElement;\n}\n\nexport function closestCategoryContent(element: NullableElement) {\n  if (!element) {\n    return null;\n  }\n  return element.closest(\n    asSelectors(ClassNames.categoryContent)\n  ) as HTMLElement;\n}\n", "export function parseNativeEmoji(unified: string): string {\n  return unified\n    .split('-')\n    .map(hex => String.fromCodePoint(parseInt(hex, 16)))\n    .join('');\n}\n", "import { SkinTones, SuggestionMode } from '../types/exposedTypes';\n\nimport { DataEmoji } from './DataTypes';\nimport { emojiUnified } from './emojiSelectors';\n\nconst SUGGESTED_LS_KEY = 'epr_suggested';\n\ntype SuggestedItem = {\n  unified: string;\n  original: string;\n  count: number;\n};\n\ntype Suggested = SuggestedItem[];\n\nexport function getSuggested(mode?: SuggestionMode): Suggested {\n  try {\n    if (!window?.localStorage) {\n      return [];\n    }\n    const recent = JSON.parse(\n      window?.localStorage.getItem(SUGGESTED_LS_KEY) ?? '[]'\n    ) as Suggested;\n\n    if (mode === SuggestionMode.FREQUENT) {\n      return recent.sort((a, b) => b.count - a.count);\n    }\n\n    return recent;\n  } catch {\n    return [];\n  }\n}\n\nexport function setSuggested(emoji: DataEmoji, skinTone: SkinTones) {\n  const recent = getSuggested();\n\n  const unified = emojiUnified(emoji, skinTone);\n  const originalUnified = emojiUnified(emoji);\n\n  let existing = recent.find(({ unified: u }) => u === unified);\n\n  let nextList: SuggestedItem[];\n\n  if (existing) {\n    nextList = [existing].concat(recent.filter(i => i !== existing));\n  } else {\n    existing = {\n      unified,\n      original: originalUnified,\n      count: 0\n    };\n    nextList = [existing, ...recent];\n  }\n\n  existing.count++;\n\n  nextList.length = Math.min(nextList.length, 14);\n\n  try {\n    window?.localStorage.setItem(SUGGESTED_LS_KEY, JSON.stringify(nextList));\n    // Prevents the change from being seen immediately.\n  } catch {\n    // ignore\n  }\n}\n", "import {\n  Categories,\n  CategoryConfig,\n  CustomCategoryConfig\n} from '../config/categoryConfig';\nimport { CustomEmoji } from '../config/customEmojiConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\n\nexport function isCustomCategory(\n  category: CategoryConfig | CustomCategoryConfig\n): category is CustomCategoryConfig {\n  return category.category === Categories.CUSTOM;\n}\n\nexport function isCustomEmoji(emoji: Partial<DataEmoji>): emoji is CustomEmoji {\n  return emoji.imgUrl !== undefined;\n}\n", "import * as React from 'react';\nimport { useEffect, useRef } from 'react';\n\nimport {\n  emojiFromElement,\n  isEmojiElement,\n  NullableElement\n} from '../DomUtils/selectors';\nimport {\n  useActiveSkinToneState,\n  useDisallowClickRef,\n  useEmojiVariationPickerState,\n  useUpdateSuggested\n} from '../components/context/PickerContext';\nimport { GetEmojiUrl } from '../components/emoji/BaseEmojiProps';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useOnEmojiClickConfig\n} from '../config/useConfig';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport {\n  activeVariationFromUnified,\n  emojiHasVariations,\n  emojiNames,\n  emojiUnified\n} from '../dataUtils/emojiSelectors';\nimport { parseNativeEmoji } from '../dataUtils/parseNativeEmoji';\nimport { setSuggested } from '../dataUtils/suggested';\nimport { isCustomEmoji } from '../typeRefinements/typeRefinements';\nimport { EmojiClickData, SkinTones, EmojiStyle } from '../types/exposedTypes';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\nimport useSetVariationPicker from './useSetVariationPicker';\n\nexport function useMouseDownHandlers(\n  ContainerRef: React.MutableRefObject<NullableElement>,\n  mouseEventSource: MOUSE_EVENT_SOURCE\n) {\n  const mouseDownTimerRef = useRef<undefined | number>();\n  const setVariationPicker = useSetVariationPicker();\n  const disallowClickRef = useDisallowClickRef();\n  const [, setEmojiVariationPicker] = useEmojiVariationPickerState();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const onEmojiClick = useOnEmojiClickConfig(mouseEventSource);\n  const [, updateSuggested] = useUpdateSuggested();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const activeEmojiStyle = useEmojiStyleConfig();\n\n  const onClick = React.useCallback(\n    function onClick(event: MouseEvent) {\n      if (disallowClickRef.current) {\n        return;\n      }\n\n      closeAllOpenToggles();\n\n      const [emoji, unified] = emojiFromEvent(event);\n\n      if (!emoji || !unified) {\n        return;\n      }\n\n      const skinToneToUse =\n        activeVariationFromUnified(unified) || activeSkinTone;\n\n      updateSuggested();\n      setSuggested(emoji, skinToneToUse);\n      onEmojiClick(\n        emojiClickOutput(emoji, skinToneToUse, activeEmojiStyle, getEmojiUrl),\n        event\n      );\n    },\n    [\n      activeSkinTone,\n      closeAllOpenToggles,\n      disallowClickRef,\n      onEmojiClick,\n      updateSuggested,\n      getEmojiUrl,\n      activeEmojiStyle\n    ]\n  );\n\n  const onMouseDown = React.useCallback(\n    function onMouseDown(event: MouseEvent) {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n      }\n\n      const [emoji] = emojiFromEvent(event);\n\n      if (!emoji || !emojiHasVariations(emoji)) {\n        return;\n      }\n\n      mouseDownTimerRef.current = window?.setTimeout(() => {\n        disallowClickRef.current = true;\n        mouseDownTimerRef.current = undefined;\n        closeAllOpenToggles();\n        setVariationPicker(event.target as HTMLElement);\n        setEmojiVariationPicker(emoji);\n      }, 500);\n    },\n    [\n      disallowClickRef,\n      closeAllOpenToggles,\n      setVariationPicker,\n      setEmojiVariationPicker\n    ]\n  );\n  const onMouseUp = React.useCallback(\n    function onMouseUp() {\n      if (mouseDownTimerRef.current) {\n        clearTimeout(mouseDownTimerRef.current);\n        mouseDownTimerRef.current = undefined;\n      } else if (disallowClickRef.current) {\n        // The problem we're trying to overcome here\n        // is that the emoji has both mouseup and click events\n        // and when releasing a mouseup event\n        // the click gets triggered too\n        // So we're disallowing the click event for a short time\n\n        requestAnimationFrame(() => {\n          disallowClickRef.current = false;\n        });\n      }\n    },\n    [disallowClickRef]\n  );\n\n  useEffect(() => {\n    if (!ContainerRef.current) {\n      return;\n    }\n    const confainerRef = ContainerRef.current;\n    confainerRef.addEventListener('click', onClick, {\n      passive: true\n    });\n\n    confainerRef.addEventListener('mousedown', onMouseDown, {\n      passive: true\n    });\n    confainerRef.addEventListener('mouseup', onMouseUp, {\n      passive: true\n    });\n\n    return () => {\n      confainerRef?.removeEventListener('click', onClick);\n      confainerRef?.removeEventListener('mousedown', onMouseDown);\n      confainerRef?.removeEventListener('mouseup', onMouseUp);\n    };\n  }, [ContainerRef, onClick, onMouseDown, onMouseUp]);\n}\n\nfunction emojiFromEvent(event: MouseEvent): [DataEmoji, string] | [] {\n  const target = event?.target as HTMLElement;\n  if (!isEmojiElement(target)) {\n    return [];\n  }\n\n  return emojiFromElement(target);\n}\n\nfunction emojiClickOutput(\n  emoji: DataEmoji,\n  activeSkinTone: SkinTones,\n  activeEmojiStyle: EmojiStyle,\n  getEmojiUrl: GetEmojiUrl\n): EmojiClickData {\n  const names = emojiNames(emoji);\n\n  if (isCustomEmoji(emoji)) {\n    const unified = emojiUnified(emoji);\n    return {\n      activeSkinTone,\n      emoji: unified,\n      getImageUrl() {\n        return emoji.imgUrl;\n      },\n      imageUrl: emoji.imgUrl,\n      isCustom: true,\n      names,\n      unified,\n      unifiedWithoutSkinTone: unified\n    };\n  }\n  const unified = emojiUnified(emoji, activeSkinTone);\n\n  return {\n    activeSkinTone,\n    emoji: parseNativeEmoji(unified),\n    getImageUrl(emojiStyle: EmojiStyle = activeEmojiStyle ?? EmojiStyle.APPLE) {\n      return getEmojiUrl(unified, emojiStyle);\n    },\n    imageUrl: getEmojiUrl(unified, activeEmojiStyle ?? EmojiStyle.APPLE),\n    isCustom: false,\n    names,\n    unified,\n    unifiedWithoutSkinTone: emojiUnified(emoji)\n  };\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\ninterface Props\n  extends React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  > {\n  className?: string;\n}\n\nexport function Button(props: Props) {\n  return (\n    <button\n      type=\"button\"\n      {...props}\n      className={cx(styles.button, props.className)}\n    >\n      {props.children}\n    </button>\n  );\n}\n\nconst styles = stylesheet.create({\n  button: {\n    '.': 'epr-btn',\n    cursor: 'pointer',\n    border: '0',\n    background: 'none',\n    outline: 'none'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\n\ntype ClickableEmojiButtonProps = Readonly<{\n  hidden?: boolean;\n  showVariations?: boolean;\n  hiddenOnSearch?: boolean;\n  emojiNames: string[];\n  children: React.ReactNode;\n  hasVariations: boolean;\n  unified?: string;\n  noBackground?: boolean;\n  className?: string;\n}>;\n\nexport function ClickableEmojiButton({\n  emojiNames,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  showVariations = true,\n  hasVariations,\n  children,\n  className,\n  noBackground = false\n}: ClickableEmojiButtonProps) {\n  return (\n    <Button\n      className={cx(\n        styles.emoji,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch,\n        {\n          [ClassNames.visible]: !hidden && !hiddenOnSearch\n        },\n        !!(hasVariations && showVariations) && styles.hasVariations,\n        noBackground && styles.noBackground,\n        className\n      )}\n      data-unified={unified}\n      aria-label={getAriaLabel(emojiNames)}\n      data-full-name={emojiNames}\n    >\n      {children}\n    </Button>\n  );\n}\n\nfunction getAriaLabel(emojiNames: string[]) {\n  return emojiNames[0].match('flag-')\n    ? emojiNames[1] ?? emojiNames[0]\n    : emojiNames[0];\n}\n\nconst styles = stylesheet.create({\n  emoji: {\n    '.': ClassNames.emoji,\n    position: 'relative',\n    width: 'var(--epr-emoji-fullsize)',\n    height: 'var(--epr-emoji-fullsize)',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    borderRadius: '8px',\n    overflow: 'hidden',\n    transition: 'background-color 0.2s',\n    ':hover': {\n      backgroundColor: 'var(--epr-emoji-hover-color)'\n    },\n    ':focus': {\n      backgroundColor: 'var(--epr-focus-bg-color)'\n    }\n  },\n  noBackground: {\n    background: 'none',\n    ':hover': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    },\n    ':focus': {\n      backgroundColor: 'transparent',\n      background: 'none'\n    }\n  },\n  hasVariations: {\n    '.': ClassNames.emojiHasVariations,\n    ':after': {\n      content: '',\n      display: 'block',\n      width: '0',\n      height: '0',\n      right: '0px',\n      bottom: '1px',\n      position: 'absolute',\n      borderLeft: '4px solid transparent',\n      borderRight: '4px solid transparent',\n      transform: 'rotate(135deg)',\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color)',\n      zIndex: 'var(--epr-emoji-variations-indictator-z-index)'\n    },\n    ':hover:after': {\n      borderBottom: '4px solid var(--epr-emoji-variation-indicator-color-hover)'\n    }\n  }\n});\n", "import { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport const emojiStyles = stylesheet.create({\n  external: {\n    '.': ClassNames.external,\n    fontSize: '0'\n  },\n  common: {\n    alignSelf: 'center',\n    justifySelf: 'center',\n    display: 'block'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function EmojiImg({\n  emojiName,\n  style,\n  lazyLoad = false,\n  imgUrl,\n  onError,\n  className\n}: {\n  emojiName: string;\n  emojiStyle: EmojiStyle;\n  style: React.CSSProperties;\n  lazyLoad?: boolean;\n  imgUrl: string;\n    onError: () => void;\n  className?: string;\n}) {\n  return (\n    <img\n      src={imgUrl}\n      alt={emojiName}\n      className={cx(styles.emojiImag, emojiStyles.external, emojiStyles.common, className)}\n      loading={lazyLoad ? 'lazy' : 'eager'}\n      onError={onError}\n      style={style}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiImag: {\n    '.': 'epr-emoji-img',\n    maxWidth: 'var(--epr-emoji-fullsize)',\n    maxHeight: 'var(--epr-emoji-fullsize)',\n    minWidth: 'var(--epr-emoji-fullsize)',\n    minHeight: 'var(--epr-emoji-fullsize)',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { parseNativeEmoji } from '../../dataUtils/parseNativeEmoji';\n\nimport { emojiStyles } from './emojiStyles';\n\nexport function NativeEmoji({\n  unified,\n  style,\n  className\n}: {\n  unified: string;\n  style: React.CSSProperties;\n  className?: string;\n}) {\n  return (\n    <span\n      className={cx(\n        styles.nativeEmoji,\n        emojiStyles.common,\n        emojiStyles.external,\n        className\n      )}\n      data-unified={unified}\n      style={style}\n    >\n      {parseNativeEmoji(unified)}\n    </span>\n  );\n}\n\nconst styles = stylesheet.create({\n  nativeEmoji: {\n    '.': 'epr-emoji-native',\n    fontFamily:\n      '\"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Segoe UI\", \"Apple Color Emoji\", \"Twemoji Mozilla\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\"!important',\n    position: 'relative',\n    lineHeight: '100%',\n    fontSize: 'var(--epr-emoji-size)',\n    textAlign: 'center',\n    alignSelf: 'center',\n    justifySelf: 'center',\n    letterSpacing: '0',\n    padding: 'var(--epr-emoji-padding)'\n  }\n});\n", "import * as React from 'react';\n\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUrlByUnified\n} from '../../dataUtils/emojiSelectors';\nimport { isCustomEmoji } from '../../typeRefinements/typeRefinements';\nimport { EmojiStyle } from '../../types/exposedTypes';\nimport { useEmojisThatFailedToLoadState } from '../context/PickerContext';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { EmojiImg } from './EmojiImg';\nimport { NativeEmoji } from './NativeEmoji';\n\nexport function ViewOnlyEmoji({\n  emoji,\n  unified,\n  emojiStyle,\n  size,\n  lazyLoad,\n  getEmojiUrl = emojiUrlByUnified,\n  className\n}: BaseEmojiProps) {\n  const [, setEmojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n\n  const style = {} as React.CSSProperties;\n  if (size) {\n    style.width = style.height = style.fontSize = `${size}px`;\n  }\n\n  const emojiToRender = emoji ? emoji : emojiByUnified(unified);\n\n  if (!emojiToRender) {\n    return null;\n  }\n\n  if (isCustomEmoji(emojiToRender)) {\n    return (\n      <EmojiImg\n        style={style}\n        emojiName={unified}\n        emojiStyle={EmojiStyle.NATIVE}\n        lazyLoad={lazyLoad}\n        imgUrl={emojiToRender.imgUrl}\n        onError={onError}\n        className={className}\n      />\n    );\n  }\n\n  return (\n    <>\n      {emojiStyle === EmojiStyle.NATIVE ? (\n        <NativeEmoji unified={unified} style={style} className={className} />\n      ) : (\n        <EmojiImg\n          style={style}\n          emojiName={emojiName(emojiToRender)}\n          emojiStyle={emojiStyle}\n          lazyLoad={lazyLoad}\n          imgUrl={getEmojiUrl(unified, emojiStyle)}\n          onError={onError}\n          className={className}\n        />\n      )}\n    </>\n  );\n\n  function onError() {\n    setEmojisThatFailedToLoad(prev => new Set(prev).add(unified));\n  }\n}\n", "import * as React from 'react';\n\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiHasVariations, emojiNames } from '../../dataUtils/emojiSelectors';\n\nimport { BaseEmojiProps } from './BaseEmojiProps';\nimport { ClickableEmojiButton } from './ClickableEmojiButton';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\ntype ClickableEmojiProps = Readonly<\n  BaseEmojiProps & {\n    hidden?: boolean;\n    showVariations?: boolean;\n    hiddenOnSearch?: boolean;\n    emoji: DataEmoji;\n    className?: string;\n    noBackground?: boolean;\n  }\n>;\n\nexport function ClickableEmoji({\n  emoji,\n  unified,\n  hidden,\n  hiddenOnSearch,\n  emojiStyle,\n  showVariations = true,\n  size,\n  lazyLoad,\n  getEmojiUrl,\n  className,\n  noBackground = false\n}: ClickableEmojiProps) {\n  const hasVariations = emojiHasVariations(emoji);\n\n  return (\n    <ClickableEmojiButton\n      hasVariations={hasVariations}\n      showVariations={showVariations}\n      hidden={hidden}\n      hiddenOnSearch={hiddenOnSearch}\n      emojiNames={emojiNames(emoji)}\n      unified={unified}\n      noBackground={noBackground}\n    >\n      <ViewOnlyEmoji\n        unified={unified}\n        emoji={emoji}\n        size={size}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoad}\n        getEmojiUrl={getEmojiUrl}\n        className={className}\n      />\n    </ClickableEmojiButton>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport { Button } from '../atoms/Button';\nimport { useReactionsModeState } from '../context/PickerContext';\n\nimport Plus from './svg/plus.svg';\n\nexport function BtnPlus() {\n  const [, setReactionsMode] = useReactionsModeState();\n  return (\n    <Button\n      aria-label=\"Show all Emojis\"\n      title=\"Show all Emojis\"\n      tabIndex={0}\n      className={cx(styles.plusSign)}\n      onClick={() => setReactionsMode(false)}\n    />\n  );\n}\n\nconst styles = stylesheet.create({\n  plusSign: {\n    fontSize: '20px',\n    padding: '17px',\n    color: 'var(--epr-text-color)',\n    borderRadius: '50%',\n    textAlign: 'center',\n    lineHeight: '100%',\n    width: '20px',\n    height: '20px',\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    transition: 'background-color 0.2s ease-in-out',\n    ':after': {\n      content: '',\n      minWidth: '20px',\n      minHeight: '20px',\n      backgroundImage: `url(${Plus})`,\n      backgroundColor: 'transparent',\n      backgroundRepeat: 'no-repeat',\n      backgroundSize: '20px',\n      backgroundPositionY: '0'\n    },\n    ':hover': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-20px'\n      }\n    },\n    ':focus': {\n      color: 'var(--epr-highlight-color)',\n      backgroundColor: 'var(--epr-hover-bg-color-reduced-opacity)',\n      ':after': {\n        backgroundPositionY: '-40px'\n      }\n    }\n  },\n  ...darkMode('plusSign', {\n    ':after': { backgroundPositionY: '-40px' },\n    ':hover:after': { backgroundPositionY: '-60px' }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonStyles, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  MOUSE_EVENT_SOURCE,\n  useEmojiStyleConfig,\n  useReactionsConfig,\n  useAllowExpandReactions,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport { DataEmoji } from '../../dataUtils/DataTypes';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useReactionsRef } from '../context/ElementRefContext';\nimport { useReactionsModeState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { BtnPlus } from './BtnPlus';\n\nexport function Reactions() {\n  const [reactionsOpen] = useReactionsModeState();\n  const ReactionsRef = useReactionsRef();\n  const reactions = useReactionsConfig();\n  useMouseDownHandlers(ReactionsRef, MOUSE_EVENT_SOURCE.REACTIONS);\n  const emojiStyle = useEmojiStyleConfig();\n  const allowExpandReactions = useAllowExpandReactions();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  if (!reactionsOpen) {\n    return null;\n  }\n\n  return (\n    <ul\n      className={cx(styles.list, !reactionsOpen && commonStyles.hidden)}\n      ref={ReactionsRef}\n    >\n      {reactions.map(reaction => (\n        <li key={reaction}>\n          <ClickableEmoji\n            emoji={emojiByUnified(reaction) as DataEmoji}\n            emojiStyle={emojiStyle}\n            unified={reaction}\n            showVariations={false}\n            className={cx(styles.emojiButton)}\n            noBackground\n            getEmojiUrl={getEmojiUrl}\n          />\n        </li>\n      ))}\n      {allowExpandReactions ? (\n        <li>\n          <BtnPlus />\n        </li>\n      ) : null}\n    </ul>\n  );\n}\n\nconst styles = stylesheet.create({\n  list: {\n    listStyle: 'none',\n    margin: '0',\n    padding: '0 5px',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    height: '100%'\n  },\n  emojiButton: {\n    ':hover': {\n      transform: 'scale(1.2)'\n    },\n    ':focus': {\n      transform: 'scale(1.2)'\n    },\n    ':active': {\n      transform: 'scale(1.1)'\n    },\n    transition: 'transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.5)'\n  }\n});\n", "import { useEffect } from 'react';\n\nimport { ElementRef } from '../components/context/ElementRefContext';\n\nimport { useCloseAllOpenToggles } from './useCloseAllOpenToggles';\n\nexport function useOnScroll(BodyRef: ElementRef) {\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n\n  useEffect(() => {\n    const bodyRef = BodyRef.current;\n    if (!bodyRef) {\n      return;\n    }\n\n    bodyRef.addEventListener('scroll', onScroll, {\n      passive: true\n    });\n\n    function onScroll() {\n      closeAllOpenToggles();\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('scroll', onScroll);\n    };\n  }, [BodyRef, closeAllOpenToggles]);\n}\n", "import { useEmojisThatFailedToLoadState } from '../components/context/PickerContext';\nimport { DataEmoji } from '../dataUtils/DataTypes';\nimport { emojiUnified } from '../dataUtils/emojiSelectors';\n\nimport { useIsEmojiFiltered } from './useFilter';\n\nexport function useIsEmojiHidden(): (emoji: DataEmoji) => IsHiddenReturn {\n  const [emojisThatFailedToLoad] = useEmojisThatFailedToLoadState();\n  const isEmojiFiltered = useIsEmojiFiltered();\n\n  return (emoji: DataEmoji): IsHiddenReturn => {\n    const unified = emojiUnified(emoji);\n\n    const failedToLoad = emojisThatFailedToLoad.has(unified);\n    const filteredOut = isEmojiFiltered(unified);\n\n    return {\n      failedToLoad,\n      filteredOut,\n      hidden: failedToLoad || filteredOut\n    };\n  };\n}\n\ntype IsHiddenReturn = {\n  failedToLoad: boolean;\n  filteredOut: boolean;\n  hidden: boolean;\n};\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  commonStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryFromCategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n  children?: React.ReactNode;\n  hidden?: boolean;\n  hiddenOnSearch?: boolean;\n}>;\n\nexport function EmojiCategory({\n  categoryConfig,\n  children,\n  hidden,\n  hiddenOnSearch\n}: Props) {\n  const category = categoryFromCategoryConfig(categoryConfig);\n  const categoryName = categoryNameFromCategoryConfig(categoryConfig);\n\n  return (\n    <li\n      className={cx(\n        styles.category,\n        hidden && commonStyles.hidden,\n        hiddenOnSearch && commonInteractionStyles.hiddenOnSearch\n      )}\n      data-name={category}\n      aria-label={categoryName}\n    >\n      <h2 className={cx(styles.label)}>{categoryName}</h2>\n      <div className={cx(styles.categoryContent)}>{children}</div>\n    </li>\n  );\n}\n\nconst styles = stylesheet.create({\n  category: {\n    '.': ClassNames.category,\n    ':not(:has(.epr-visible))': {\n      display: 'none'\n    }\n  },\n  categoryContent: {\n    '.': ClassNames.categoryContent,\n    display: 'grid',\n    gridGap: '0',\n    gridTemplateColumns: 'repeat(auto-fill, var(--epr-emoji-fullsize))',\n    justifyContent: 'space-between',\n    margin: 'var(--epr-category-padding)',\n    position: 'relative'\n  },\n  label: {\n    '.': ClassNames.label,\n    alignItems: 'center',\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(3px)',\n    backgroundColor: 'var(--epr-category-label-bg-color)',\n    color: 'var(--epr-category-label-text-color)',\n    display: 'flex',\n    fontSize: '16px',\n    fontWeight: 'bold',\n    height: 'var(--epr-category-label-height)',\n    margin: '0',\n    padding: 'var(--epr-category-label-padding)',\n    position: 'sticky',\n    textTransform: 'capitalize',\n    top: '0',\n    width: '100%',\n    zIndex: 'var(--epr-category-label-z-index)'\n  }\n});\n", "import * as React from 'react';\n\nlet isEverMounted = false;\n\nexport function useIsEverMounted() {\n  const [isMounted, setIsMounted] = React.useState(isEverMounted);\n\n  React.useEffect(() => {\n    setIsMounted(true);\n    isEverMounted = true;\n  }, []);\n\n  return isMounted || isEverMounted;\n}\n", "import * as React from 'react';\n\nimport { CategoryConfig } from '../../config/categoryConfig';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useSuggestedEmojisModeConfig\n} from '../../config/useConfig';\nimport { emojiByUnified } from '../../dataUtils/emojiSelectors';\nimport { getSuggested } from '../../dataUtils/suggested';\nimport { useIsEverMounted } from '../../hooks/useIsEverMounted';\nimport { useUpdateSuggested } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\n\ntype Props = Readonly<{\n  categoryConfig: CategoryConfig;\n}>;\n\nexport function Suggested({ categoryConfig }: Props) {\n  const [suggestedUpdated] = useUpdateSuggested();\n  const isMounted = useIsEverMounted();\n  const suggestedEmojisModeConfig = useSuggestedEmojisModeConfig();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const suggested = React.useMemo(\n    () => getSuggested(suggestedEmojisModeConfig) ?? [],\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [suggestedUpdated, suggestedEmojisModeConfig]\n  );\n  const emojiStyle = useEmojiStyleConfig();\n\n  if (!isMounted) {\n    return null;\n  }\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      hiddenOnSearch\n      hidden={suggested.length === 0}\n    >\n      {suggested.map(suggestedItem => {\n        const emoji = emojiByUnified(suggestedItem.original);\n\n        if (!emoji) {\n          return null;\n        }\n\n        return (\n          <ClickableEmoji\n            showVariations={false}\n            unified={suggestedItem.unified}\n            emojiStyle={emojiStyle}\n            emoji={emoji}\n            key={suggestedItem.unified}\n            getEmojiUrl={getEmojiUrl}\n          />\n        );\n      })}\n    </EmojiCategory>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  Categories,\n  CategoryConfig,\n  categoryFromCategoryConfig\n} from '../../config/categoryConfig';\nimport {\n  useCategoriesConfig,\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  useLazyLoadEmojisConfig,\n  useSkinTonesDisabledConfig\n} from '../../config/useConfig';\nimport { emojisByCategory, emojiUnified } from '../../dataUtils/emojiSelectors';\nimport { useIsEmojiDisallowed } from '../../hooks/useDisallowedEmojis';\nimport { useIsEmojiHidden } from '../../hooks/useIsEmojiHidden';\nimport {\n  useActiveSkinToneState,\n  useIsPastInitialLoad\n} from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport { EmojiCategory } from './EmojiCategory';\nimport { Suggested } from './Suggested';\n\nexport function EmojiList() {\n  const categories = useCategoriesConfig();\n  const renderdCategoriesCountRef = React.useRef(0);\n\n  return (\n    <ul className={cx(styles.emojiList)}>\n      {categories.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n\n        if (category === Categories.SUGGESTED) {\n          return <Suggested key={category} categoryConfig={categoryConfig} />;\n        }\n\n        return (\n          <React.Suspense key={category}>\n            <RenderCategory\n              category={category}\n              categoryConfig={categoryConfig}\n              renderdCategoriesCountRef={renderdCategoriesCountRef}\n            />\n          </React.Suspense>\n        );\n      })}\n    </ul>\n  );\n}\n\nfunction RenderCategory({\n  category,\n  categoryConfig,\n  renderdCategoriesCountRef\n}: {\n  category: Categories;\n  categoryConfig: CategoryConfig;\n  renderdCategoriesCountRef: React.MutableRefObject<number>;\n}) {\n  const isEmojiHidden = useIsEmojiHidden();\n  const lazyLoadEmojis = useLazyLoadEmojisConfig();\n  const emojiStyle = useEmojiStyleConfig();\n  const isPastInitialLoad = useIsPastInitialLoad();\n  const [activeSkinTone] = useActiveSkinToneState();\n  const isEmojiDisallowed = useIsEmojiDisallowed();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n  const showVariations = !useSkinTonesDisabledConfig();\n\n  // Small trick to defer the rendering of all emoji categories until the first category is visible\n  // This way the user gets to actually see something and not wait for the whole picker to render.\n  const emojisToPush =\n    !isPastInitialLoad && renderdCategoriesCountRef.current > 0\n      ? []\n      : emojisByCategory(category);\n\n  if (emojisToPush.length > 0) {\n    renderdCategoriesCountRef.current++;\n  }\n\n  let hiddenCounter = 0;\n\n  const emojis = emojisToPush.map(emoji => {\n    const unified = emojiUnified(emoji, activeSkinTone);\n    const { failedToLoad, filteredOut, hidden } = isEmojiHidden(emoji);\n\n    const isDisallowed = isEmojiDisallowed(emoji);\n\n    if (hidden || isDisallowed) {\n      hiddenCounter++;\n    }\n\n    if (isDisallowed) {\n      return null;\n    }\n\n    return (\n      <ClickableEmoji\n        showVariations={showVariations}\n        key={unified}\n        emoji={emoji}\n        unified={unified}\n        hidden={failedToLoad}\n        hiddenOnSearch={filteredOut}\n        emojiStyle={emojiStyle}\n        lazyLoad={lazyLoadEmojis}\n        getEmojiUrl={getEmojiUrl}\n      />\n    );\n  });\n\n  return (\n    <EmojiCategory\n      categoryConfig={categoryConfig}\n      // Indicates that there are no visible emojis\n      // Hence, the category should be hidden\n      hidden={hiddenCounter === emojis.length}\n    >\n      {emojis}\n    </EmojiCategory>\n  );\n}\n\nconst styles = stylesheet.create({\n  emojiList: {\n    '.': ClassNames.emojiList,\n    listStyle: 'none',\n    margin: '0',\n    padding: '0'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport { focusFirstVisibleEmoji } from '../../DomUtils/keyboardNavigation';\nimport {\n  buttonFromTarget,\n  elementHeight,\n  emojiTrueOffsetTop,\n  emojiTruOffsetLeft\n} from '../../DomUtils/selectors';\nimport { darkMode, stylesheet } from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig\n} from '../../config/useConfig';\nimport {\n  emojiHasVariations,\n  emojiUnified,\n  emojiVariations\n} from '../../dataUtils/emojiSelectors';\nimport {\n  useAnchoredEmojiRef,\n  useBodyRef,\n  useSetAnchoredEmojiRef,\n  useVariationPickerRef\n} from '../context/ElementRefContext';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ClickableEmoji } from '../emoji/Emoji';\n\nimport SVGTriangle from './svg/triangle.svg';\n\nenum Direction {\n  Up,\n  Down\n}\n\n// eslint-disable-next-line complexity\nexport function EmojiVariationPicker() {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const VariationPickerRef = useVariationPickerRef();\n  const [emoji] = useEmojiVariationPickerState();\n  const emojiStyle = useEmojiStyleConfig();\n\n  const { getTop, getMenuDirection } = useVariationPickerTop(\n    VariationPickerRef\n  );\n  const setAnchoredEmojiRef = useSetAnchoredEmojiRef();\n  const getPointerStyle = usePointerStyle(VariationPickerRef);\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n  const visible = Boolean(\n    emoji &&\n      button &&\n      emojiHasVariations(emoji) &&\n      button.classList.contains(ClassNames.emojiHasVariations)\n  );\n\n  useEffect(() => {\n    if (!visible) {\n      return;\n    }\n\n    focusFirstVisibleEmoji(VariationPickerRef.current);\n  }, [VariationPickerRef, visible, AnchoredEmojiRef]);\n\n  let top, pointerStyle;\n\n  if (!visible && AnchoredEmojiRef.current) {\n    setAnchoredEmojiRef(null);\n  } else {\n    top = getTop();\n    pointerStyle = getPointerStyle();\n  }\n\n  return (\n    <div\n      ref={VariationPickerRef}\n      className={cx(\n        styles.variationPicker,\n        getMenuDirection() === Direction.Down && styles.pointingUp,\n        visible && styles.visible\n      )}\n      style={{ top }}\n    >\n      {visible && emoji\n        ? [emojiUnified(emoji)]\n            .concat(emojiVariations(emoji))\n            .slice(0, 6)\n            .map(unified => (\n              <ClickableEmoji\n                key={unified}\n                emoji={emoji}\n                unified={unified}\n                emojiStyle={emojiStyle}\n                showVariations={false}\n                getEmojiUrl={getEmojiUrl}\n              />\n            ))\n        : null}\n      <div className={cx(styles.pointer)} style={pointerStyle} />\n    </div>\n  );\n}\n\nfunction usePointerStyle(VariationPickerRef: React.RefObject<HTMLElement>) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  return function getPointerStyle() {\n    const style: React.CSSProperties = {};\n    if (!VariationPickerRef.current) {\n      return style;\n    }\n\n    if (AnchoredEmojiRef.current) {\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const offsetLeft = emojiTruOffsetLeft(button);\n\n      if (!button) {\n        return style;\n      }\n\n      // half of the button\n      style.left = offsetLeft + button?.clientWidth / 2;\n    }\n\n    return style;\n  };\n}\n\nfunction useVariationPickerTop(\n  VariationPickerRef: React.RefObject<HTMLElement>\n) {\n  const AnchoredEmojiRef = useAnchoredEmojiRef();\n  const BodyRef = useBodyRef();\n  let direction = Direction.Up;\n\n  return {\n    getMenuDirection,\n    getTop\n  };\n\n  function getMenuDirection() {\n    return direction;\n  }\n\n  function getTop() {\n    direction = Direction.Up;\n    let emojiOffsetTop = 0;\n\n    if (!VariationPickerRef.current) {\n      return 0;\n    }\n\n    const height = elementHeight(VariationPickerRef.current);\n\n    if (AnchoredEmojiRef.current) {\n      const bodyRef = BodyRef.current;\n      const button = buttonFromTarget(AnchoredEmojiRef.current);\n\n      const buttonHeight = elementHeight(button);\n\n      emojiOffsetTop = emojiTrueOffsetTop(button);\n\n      const scrollTop = bodyRef?.scrollTop ?? 0;\n\n      if (scrollTop > emojiOffsetTop - height) {\n        direction = Direction.Down;\n        emojiOffsetTop += buttonHeight + height;\n      }\n    }\n\n    return emojiOffsetTop - height;\n  }\n}\n\nconst styles = stylesheet.create({\n  variationPicker: {\n    '.': ClassNames.variationPicker,\n    position: 'absolute',\n    right: '15px',\n    left: '15px',\n    padding: '5px',\n    boxShadow: '0px 2px 5px rgba(0, 0, 0, 0.2)',\n    borderRadius: '3px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-around',\n    opacity: '0',\n    visibility: 'hidden',\n    pointerEvents: 'none',\n    top: '-100%',\n    border: '1px solid var(--epr-picker-border-color)',\n    height: 'var(--epr-emoji-variation-picker-height)',\n    zIndex: 'var(--epr-skin-variation-picker-z-index)',\n    background: 'var(--epr-emoji-variation-picker-bg-color)',\n    transform: 'scale(0.9)',\n    transition: 'transform 0.1s ease-out, opacity 0.2s ease-out'\n  },\n  visible: {\n    opacity: '1',\n    visibility: 'visible',\n    pointerEvents: 'all',\n    transform: 'scale(1)'\n  },\n  pointingUp: {\n    '.': 'pointing-up',\n    transformOrigin: 'center 0%',\n    transform: 'scale(0.9)'\n  },\n  '.pointing-up': {\n    pointer: {\n      top: '0',\n      transform: 'rotate(180deg) translateY(100%) translateX(18px)'\n    }\n  },\n  pointer: {\n    '.': 'epr-emoji-pointer',\n    content: '',\n    position: 'absolute',\n    width: '25px',\n    height: '15px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '50px 15px',\n    top: '100%',\n    transform: 'translateX(-18px)',\n    backgroundImage: `url(${SVGTriangle})`\n  },\n  ...darkMode('pointer', {\n    backgroundPosition: '-25px 0'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport { MOUSE_EVENT_SOURCE } from '../../config/useConfig';\nimport { useOnMouseMove } from '../../hooks/useDisallowMouseMove';\nimport { useMouseDownHandlers } from '../../hooks/useMouseDownHandlers';\nimport { useOnScroll } from '../../hooks/useOnScroll';\nimport { useBodyRef } from '../context/ElementRefContext';\n\nimport { EmojiList } from './EmojiList';\nimport { EmojiVariationPicker } from './EmojiVariationPicker';\n\nexport function Body() {\n  const BodyRef = useBodyRef();\n  useOnScroll(BodyRef);\n  useMouseDownHandlers(BodyRef, MOUSE_EVENT_SOURCE.PICKER);\n  useOnMouseMove();\n\n  return (\n    <div\n      className={cx(styles.body, commonInteractionStyles.hiddenOnReactions)}\n      ref={BodyRef}\n    >\n      <EmojiVariationPicker />\n      <EmojiList />\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  body: {\n    '.': ClassNames.scrollBody,\n    flex: '1',\n    overflowY: 'scroll',\n    overflowX: 'hidden',\n    position: 'relative'\n  }\n});\n", "import { NullableElement } from './selectors';\n\nexport function detectEmojyPartiallyBelowFold(\n  button: HTMLButtonElement,\n  bodyRef: NullableElement\n): number {\n  if (!button || !bodyRef) {\n    return 0;\n  }\n\n  const buttonRect = button.getBoundingClientRect();\n  const bodyRect = bodyRef.getBoundingClientRect();\n\n  // If the element is obscured by at least half of its size\n  return bodyRect.height - (buttonRect.y - bodyRect.y);\n}\n", "import * as React from 'react';\nimport { useEffect } from 'react';\n\nimport { detectEmojyPartiallyBelowFold } from '../DomUtils/detectEmojyPartiallyBelowFold';\nimport { focusElement } from '../DomUtils/focusElement';\nimport {\n  allUnifiedFromEmojiElement,\n  buttonFromTarget\n} from '../DomUtils/selectors';\nimport { useBodyRef } from '../components/context/ElementRefContext';\nimport { PreviewEmoji } from '../components/footer/Preview';\n\nimport {\n  useAllowMouseMove,\n  useIsMouseDisallowed\n} from './useDisallowMouseMove';\n\nexport function useEmojiPreviewEvents(\n  allow: boolean,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const BodyRef = useBodyRef();\n  const isMouseDisallowed = useIsMouseDisallowed();\n  const allowMouseMove = useAllowMouseMove();\n\n  useEffect(() => {\n    if (!allow) {\n      return;\n    }\n    const bodyRef = BodyRef.current;\n\n    bodyRef?.addEventListener('keydown', onEscape, {\n      passive: true\n    });\n\n    bodyRef?.addEventListener('mouseover', onMouseOver, true);\n\n    bodyRef?.addEventListener('focus', onEnter, true);\n\n    bodyRef?.addEventListener('mouseout', onLeave, {\n      passive: true\n    });\n    bodyRef?.addEventListener('blur', onLeave, true);\n\n    function onEnter(e: FocusEvent) {\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (!button) {\n        return onLeave();\n      }\n\n      const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n      if (!unified || !originalUnified) {\n        return onLeave();\n      }\n\n      setPreviewEmoji({\n        unified,\n        originalUnified\n      });\n    }\n    function onLeave(e?: FocusEvent | MouseEvent) {\n      if (e) {\n        const relatedTarget = e.relatedTarget as HTMLElement;\n\n        if (!buttonFromTarget(relatedTarget)) {\n          return setPreviewEmoji(null);\n        }\n      }\n\n      setPreviewEmoji(null);\n    }\n    function onEscape(e: KeyboardEvent) {\n      if (e.key === 'Escape') {\n        setPreviewEmoji(null);\n      }\n    }\n\n    function onMouseOver(e: MouseEvent) {\n      if (isMouseDisallowed()) {\n        return;\n      }\n\n      const button = buttonFromTarget(e.target as HTMLElement);\n\n      if (button) {\n        const belowFoldByPx = detectEmojyPartiallyBelowFold(button, bodyRef);\n        const buttonHeight = button.getBoundingClientRect().height;\n        if (belowFoldByPx < buttonHeight) {\n          return handlePartiallyVisibleElementFocus(button, setPreviewEmoji);\n        }\n\n        focusElement(button);\n      }\n    }\n\n    return () => {\n      bodyRef?.removeEventListener('mouseover', onMouseOver);\n      bodyRef?.removeEventListener('mouseout', onLeave);\n      bodyRef?.removeEventListener('focus', onEnter, true);\n      bodyRef?.removeEventListener('blur', onLeave, true);\n      bodyRef?.removeEventListener('keydown', onEscape);\n    };\n  }, [BodyRef, allow, setPreviewEmoji, isMouseDisallowed, allowMouseMove]);\n}\n\nfunction handlePartiallyVisibleElementFocus(\n  button: HTMLElement,\n  setPreviewEmoji: React.Dispatch<React.SetStateAction<PreviewEmoji>>\n) {\n  const { unified, originalUnified } = allUnifiedFromEmojiElement(button);\n\n  if (!unified || !originalUnified) {\n    return;\n  }\n\n  (document.activeElement as HTMLElement)?.blur?.();\n\n  setPreviewEmoji({\n    unified,\n    originalUnified\n  });\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\n\nexport enum FlexDirection {\n  ROW = 'FlexRow',\n  COLUMN = 'FlexColumn'\n}\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  direction?: FlexDirection;\n}>;\n\nexport default function Flex({\n  children,\n  className,\n  style = {},\n  direction = FlexDirection.ROW\n}: Props) {\n  return (\n    <div\n      style={{ ...style }}\n      className={cx(styles.flex, className, styles[direction])}\n    >\n      {children}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  flex: {\n    display: 'flex'\n  },\n  [FlexDirection.ROW]: {\n    flexDirection: 'row'\n  },\n  [FlexDirection.COLUMN]: {\n    flexDirection: 'column'\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\n\ntype Props = Readonly<{\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Space({ className, style = {} }: Props) {\n  return <div style={{ flex: 1, ...style }} className={cx(className)} />;\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Absolute({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'absolute' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import * as React from 'react';\n\ntype Props = Readonly<{\n  children: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}>;\n\nexport default function Relative({ children, className, style }: Props) {\n  return (\n    <div style={{ ...style, position: 'relative' }} className={className}>\n      {children}\n    </div>\n  );\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport { skinTonesNamed } from '../../../data/skinToneVariations';\nimport { SkinTones } from '../../../types/exposedTypes';\nimport { Button } from '../../atoms/Button';\n\ntype Props = {\n  isOpen: boolean;\n  onClick: () => void;\n  isActive: boolean;\n  skinToneVariation: SkinTones;\n  style?: React.CSSProperties;\n};\n\n// eslint-disable-next-line complexity\nexport function BtnSkinToneVariation({\n  isOpen,\n  onClick,\n  isActive,\n  skinToneVariation,\n  style\n}: Props) {\n  return (\n    <Button\n      style={style}\n      onClick={onClick}\n      className={cx(\n        `epr-tone-${skinToneVariation}`,\n        styles.tone,\n        !isOpen && styles.closedTone,\n        isActive && styles.active\n      )}\n      tabIndex={isOpen ? 0 : -1}\n      aria-pressed={isActive}\n      aria-label={`Skin tone ${skinTonesNamed[skinToneVariation as SkinTones]}`}\n    ></Button>\n  );\n}\n\nconst styles = stylesheet.create({\n  closedTone: {\n    opacity: '0',\n    zIndex: '0'\n  },\n  active: {\n    '.': 'epr-active',\n    zIndex: '1',\n    opacity: '1'\n  },\n  tone: {\n    '.': 'epr-tone',\n    ':hover': {\n      boxShadow: '0 0 0 3px var(--epr-active-skin-hover-color)'\n    },\n    ':focus': {\n      boxShadow: '0 0 0 3px var(--epr-focus-bg-color)'\n    },\n    '&.epr-tone-neutral': {\n      backgroundColor: '#ffd225'\n    },\n    '&.epr-tone-1f3fb': {\n      backgroundColor: '#ffdfbd'\n    },\n    '&.epr-tone-1f3fc': {\n      backgroundColor: '#e9c197'\n    },\n    '&.epr-tone-1f3fd': {\n      backgroundColor: '#c88e62'\n    },\n    '&.epr-tone-1f3fe': {\n      backgroundColor: '#a86637'\n    },\n    '&.epr-tone-1f3ff': {\n      backgroundColor: '#60463a'\n    }\n  }\n});\n", "/* eslint-disable complexity */\nimport { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../../DomUtils/classNames';\nimport { stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useOnSkinToneChangeConfig,\n  useSkinTonesDisabledConfig\n} from '../../../config/useConfig';\nimport skinToneVariations from '../../../data/skinToneVariations';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFocusSearchInput } from '../../../hooks/useFocus';\nimport Absolute from '../../Layout/Absolute';\nimport Relative from '../../Layout/Relative';\nimport { useSkinTonePickerRef } from '../../context/ElementRefContext';\nimport {\n  useActiveSkinToneState,\n  useSkinToneFanOpenState\n} from '../../context/PickerContext';\n\nimport { BtnSkinToneVariation } from './BtnSkinToneVariation';\n\nconst ITEM_SIZE = 28;\n\ntype Props = {\n  direction?: SkinTonePickerDirection;\n};\n\nexport function SkinTonePickerMenu() {\n  return (\n    <Relative style={{ height: ITEM_SIZE }}>\n      <Absolute style={{ bottom: 0, right: 0 }}>\n        <SkinTonePicker direction={SkinTonePickerDirection.VERTICAL} />\n      </Absolute>\n    </Relative>\n  );\n}\n\nexport function SkinTonePicker({\n  direction = SkinTonePickerDirection.HORIZONTAL\n}: Props) {\n  const SkinTonePickerRef = useSkinTonePickerRef();\n  const isDisabled = useSkinTonesDisabledConfig();\n  const [isOpen, setIsOpen] = useSkinToneFanOpenState();\n  const [activeSkinTone, setActiveSkinTone] = useActiveSkinToneState();\n  const onSkinToneChange = useOnSkinToneChangeConfig();\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const focusSearchInput = useFocusSearchInput();\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const fullWidth = `${ITEM_SIZE * skinToneVariations.length}px`;\n\n  const expandedSize = isOpen ? fullWidth : ITEM_SIZE + 'px';\n\n  const vertical = direction === SkinTonePickerDirection.VERTICAL;\n\n  return (\n    <Relative\n      className={cx(\n        styles.skinTones,\n        vertical && styles.vertical,\n        isOpen && styles.open,\n        vertical && isOpen && styles.verticalShadow\n      )}\n      style={\n        vertical\n          ? { flexBasis: expandedSize, height: expandedSize }\n          : { flexBasis: expandedSize }\n      }\n    >\n      <div className={cx(styles.select)} ref={SkinTonePickerRef}>\n        {skinToneVariations.map((skinToneVariation, i) => {\n          const active = skinToneVariation === activeSkinTone;\n\n          return (\n            <BtnSkinToneVariation\n              key={skinToneVariation}\n              skinToneVariation={skinToneVariation}\n              isOpen={isOpen}\n              style={{\n                transform: cx(\n                  vertical\n                    ? `translateY(-${i * (isOpen ? ITEM_SIZE : 0)}px)`\n                    : `translateX(-${i * (isOpen ? ITEM_SIZE : 0)}px)`,\n                  isOpen && active && 'scale(1.3)'\n                )\n              }}\n              isActive={active}\n              onClick={() => {\n                if (isOpen) {\n                  setActiveSkinTone(skinToneVariation);\n                  onSkinToneChange(skinToneVariation);\n                  focusSearchInput();\n                } else {\n                  setIsOpen(true);\n                }\n                closeAllOpenToggles();\n              }}\n            />\n          );\n        })}\n      </div>\n    </Relative>\n  );\n}\n\nexport enum SkinTonePickerDirection {\n  VERTICAL = ClassNames.vertical,\n  HORIZONTAL = ClassNames.horizontal\n}\n\nconst styles = stylesheet.create({\n  skinTones: {\n    '.': 'epr-skin-tones',\n    '--': {\n      '--epr-skin-tone-size': '15px'\n    },\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'flex-end',\n    transition: 'all 0.3s ease-in-out',\n    padding: '10px 0'\n  },\n  vertical: {\n    padding: '9px',\n    alignItems: 'flex-end',\n    flexDirection: 'column',\n    borderRadius: '6px',\n    border: '1px solid var(--epr-bg-color)'\n  },\n  verticalShadow: {\n    boxShadow: '0px 0 7px var(--epr-picker-border-color)'\n  },\n  open: {\n    // @ts-ignore - backdropFilter is not recognized.\n    backdropFilter: 'blur(5px)',\n    background: 'var(--epr-skin-tone-picker-menu-color)',\n    '.epr-active:after': {\n      content: '',\n      position: 'absolute',\n      top: '-2px',\n      left: '-2px',\n      right: '-2px',\n      bottom: '-2px',\n      borderRadius: '5px',\n      border: '1px solid var(--epr-active-skin-tone-indicator-border-color)'\n    }\n  },\n  select: {\n    '.': 'epr-skin-tone-select',\n    position: 'relative',\n    width: 'var(--epr-skin-tone-size)',\n    height: 'var(--epr-skin-tone-size)',\n    '> button': {\n      width: 'var(--epr-skin-tone-size)',\n      display: 'block',\n      cursor: 'pointer',\n      borderRadius: '4px',\n      height: 'var(--epr-skin-tone-size)',\n      position: 'absolute',\n      right: '0',\n      transition: 'transform 0.3s ease-in-out, opacity 0.35s ease-in-out',\n      zIndex: '0',\n      boxShadow: '0 0 0 0px var(--epr-active-skin-hover-color)'\n    }\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport {\n  commonInteractionStyles,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  useEmojiStyleConfig,\n  useGetEmojiUrlConfig,\n  usePreviewConfig\n} from '../../config/useConfig';\nimport {\n  emojiByUnified,\n  emojiName,\n  emojiUnified\n} from '../../dataUtils/emojiSelectors';\nimport { useEmojiPreviewEvents } from '../../hooks/useEmojiPreviewEvents';\nimport { useIsSkinToneInPreview } from '../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../Layout/Flex';\nimport Space from '../Layout/Space';\nimport { useEmojiVariationPickerState } from '../context/PickerContext';\nimport { ViewOnlyEmoji } from '../emoji/ViewOnlyEmoji';\nimport { SkinTonePickerMenu } from '../header/SkinTonePicker/SkinTonePicker';\n\nexport function Preview() {\n  const previewConfig = usePreviewConfig();\n  const isSkinToneInPreview = useIsSkinToneInPreview();\n\n  if (!previewConfig.showPreview) {\n    return null;\n  }\n\n  return (\n    <Flex\n      className={cx(styles.preview, commonInteractionStyles.hiddenOnReactions)}\n    >\n      <PreviewBody />\n      <Space />\n      {isSkinToneInPreview ? <SkinTonePickerMenu /> : null}\n    </Flex>\n  );\n}\n\nexport function PreviewBody() {\n  const previewConfig = usePreviewConfig();\n  const [previewEmoji, setPreviewEmoji] = useState<PreviewEmoji>(null);\n  const emojiStyle = useEmojiStyleConfig();\n  const [variationPickerEmoji] = useEmojiVariationPickerState();\n  const getEmojiUrl = useGetEmojiUrlConfig();\n\n  useEmojiPreviewEvents(previewConfig.showPreview, setPreviewEmoji);\n\n  const emoji = emojiByUnified(\n    previewEmoji?.unified ?? previewEmoji?.originalUnified\n  );\n\n  const show = emoji != null && previewEmoji != null;\n\n  return <PreviewContent />;\n\n  function PreviewContent() {\n    const defaultEmoji =\n      variationPickerEmoji ?? emojiByUnified(previewConfig.defaultEmoji);\n    if (!defaultEmoji) {\n      return null;\n    }\n    const defaultText = variationPickerEmoji\n      ? emojiName(variationPickerEmoji)\n      : previewConfig.defaultCaption;\n\n    return (\n      <>\n        <div>\n          {show ? (\n            <ViewOnlyEmoji\n              unified={previewEmoji?.unified as string}\n              emoji={emoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : defaultEmoji ? (\n            <ViewOnlyEmoji\n              unified={emojiUnified(defaultEmoji)}\n              emoji={defaultEmoji}\n              emojiStyle={emojiStyle}\n              size={45}\n              getEmojiUrl={getEmojiUrl}\n              className={cx(styles.emoji)}\n            />\n          ) : null}\n        </div>\n        <div className={cx(styles.label)}>\n          {show ? emojiName(emoji) : defaultText}\n        </div>\n      </>\n    );\n  }\n}\n\nexport type PreviewEmoji = null | {\n  unified: string;\n  originalUnified: string;\n};\n\nconst styles = stylesheet.create({\n  preview: {\n    alignItems: 'center',\n    borderTop: '1px solid var(--epr-preview-border-color)',\n    height: 'var(--epr-preview-height)',\n    padding: '0 var(--epr-horizontal-padding)',\n    position: 'relative',\n    zIndex: 'var(--epr-preview-z-index)'\n  },\n  label: {\n    color: 'var(--epr-preview-text-color)',\n    fontSize: 'var(--epr-preview-text-size)',\n    padding: 'var(--epr-preview-text-padding)',\n    textTransform: 'capitalize'\n  },\n  emoji: {\n    padding: '0'\n  }\n});\n", "export function categoryNameFromDom($category: Element | null): string | null {\n  return $category?.getAttribute('data-name') ?? null;\n}\n", "import { useEffect } from 'react';\n\nimport { categoryNameFromDom } from '../DomUtils/categoryNameFromDom';\nimport { asSelectors, ClassNames } from '../DomUtils/classNames';\nimport { useBodyRef } from '../components/context/ElementRefContext';\n\nexport function useActiveCategoryScrollDetection(\n  setActiveCategory: (category: string) => void\n) {\n  const BodyRef = useBodyRef();\n\n  useEffect(() => {\n    const visibleCategories = new Map();\n    const bodyRef = BodyRef.current;\n    const observer = new IntersectionObserver(\n      entries => {\n        if (!bodyRef) {\n          return;\n        }\n\n        for (const entry of entries) {\n          const id = categoryNameFromDom(entry.target);\n          visibleCategories.set(id, entry.intersectionRatio);\n        }\n\n        const ratios = Array.from(visibleCategories);\n        const lastCategory = ratios[ratios.length - 1];\n\n        if (lastCategory[1] == 1) {\n          return setActiveCategory(lastCategory[0]);\n        }\n\n        for (const [id, ratio] of ratios) {\n          if (ratio) {\n            setActiveCategory(id);\n            break;\n          }\n        }\n      },\n      {\n        threshold: [0, 1]\n      }\n    );\n    bodyRef?.querySelectorAll(asSelectors(ClassNames.category)).forEach(el => {\n      observer.observe(el);\n    });\n  }, [BodyRef, setActiveCategory]);\n}\n", "import { scrollTo } from '../DomUtils/scrollTo';\nimport { NullableElement } from '../DomUtils/selectors';\nimport {\n  useBodyRef,\n  usePickerMainRef\n} from '../components/context/ElementRefContext';\n\nexport function useScrollCategoryIntoView() {\n  const BodyRef = useBodyRef();\n  const PickerMainRef = usePickerMainRef();\n\n  return function scrollCategoryIntoView(category: string): void {\n    if (!BodyRef.current) {\n      return;\n    }\n    const $category = BodyRef.current?.querySelector(\n      `[data-name=\"${category}\"]`\n    ) as NullableElement;\n\n    if (!$category) {\n      return;\n    }\n\n    const offsetTop = $category.offsetTop || 0;\n\n    scrollTo(PickerMainRef.current, offsetTop);\n  };\n}\n", "import { useCustomEmojisConfig } from '../config/useConfig';\n\nexport function useShouldHideCustomEmojis() {\n  const customCategoryConfig = useCustomEmojisConfig();\n\n  if (!customCategoryConfig) {\n    return false;\n  }\n\n  return customCategoryConfig.length === 0;\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { ClassNames } from '../../DomUtils/classNames';\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../Stylesheet/stylesheet';\nimport {\n  CategoryConfig,\n  categoryNameFromCategoryConfig\n} from '../../config/categoryConfig';\nimport { Button } from '../atoms/Button';\n\nimport SVGNavigation from './svg/CategoryNav.svg';\n\ntype Props = {\n  isActiveCategory: boolean;\n  category: string;\n  allowNavigation: boolean;\n  onClick: () => void;\n  categoryConfig: CategoryConfig;\n};\n\nexport function CategoryButton({\n  isActiveCategory,\n  category,\n  allowNavigation,\n  categoryConfig,\n  onClick\n}: Props) {\n  return (\n    <Button\n      tabIndex={allowNavigation ? 0 : -1}\n      className={cx(\n        styles.catBtn,\n        commonInteractionStyles.categoryBtn,\n        `epr-icn-${category}`,\n        {\n          [ClassNames.active]: isActiveCategory\n        }\n      )}\n      onClick={onClick}\n      aria-label={categoryNameFromCategoryConfig(categoryConfig)}\n      aria-selected={isActiveCategory}\n      role=\"tab\"\n      aria-controls=\"epr-category-nav-id\"\n    />\n  );\n}\n\nconst DarkActivePositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 3)'\n};\nconst DarkPositionY = {\n  backgroundPositionY: 'calc(var(--epr-category-navigation-button-size) * 2)'\n};\n\nconst DarkInactivePosition = {\n  ':not(.epr-search-active)': {\n    catBtn: {\n      ':hover': DarkActivePositionY,\n      '&.epr-active': DarkActivePositionY\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  catBtn: {\n    '.': 'epr-cat-btn',\n    display: 'inline-block',\n    transition: 'opacity 0.2s ease-in-out',\n    position: 'relative',\n    height: 'var(--epr-category-navigation-button-size)',\n    width: 'var(--epr-category-navigation-button-size)',\n    backgroundSize: 'calc(var(--epr-category-navigation-button-size) * 10)',\n    outline: 'none',\n    backgroundPosition: '0 0',\n    backgroundImage: `url(${SVGNavigation})`,\n    ':focus:before': {\n      content: '',\n      position: 'absolute',\n      top: '-2px',\n      left: '-2px',\n      right: '-2px',\n      bottom: '-2px',\n      border: '2px solid var(--epr-category-icon-active-color)',\n      borderRadius: '50%'\n    },\n    '&.epr-icn-suggested': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -8)'\n    },\n    '&.epr-icn-custom': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -9)'\n    },\n    '&.epr-icn-activities': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -4)'\n    },\n    '&.epr-icn-animals_nature': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -1)'\n    },\n    '&.epr-icn-flags': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -7)'\n    },\n    '&.epr-icn-food_drink': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -2)'\n    },\n    '&.epr-icn-objects': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -5)'\n    },\n    '&.epr-icn-smileys_people': {\n      backgroundPositionX: '0px'\n    },\n    '&.epr-icn-symbols': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -6)'\n    },\n    '&.epr-icn-travel_places': {\n      backgroundPositionX:\n        'calc(var(--epr-category-navigation-button-size) * -3)'\n    }\n  },\n  ...darkMode('catBtn', DarkPositionY),\n  '.epr-dark-theme': {\n    ...DarkInactivePosition\n  },\n  '.epr-auto-theme': {\n    ...DarkInactivePosition\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { stylesheet } from '../../Stylesheet/stylesheet';\nimport { categoryFromCategoryConfig } from '../../config/categoryConfig';\nimport { useCategoriesConfig } from '../../config/useConfig';\nimport { useActiveCategoryScrollDetection } from '../../hooks/useActiveCategoryScrollDetection';\nimport useIsSearchMode from '../../hooks/useIsSearchMode';\nimport { useScrollCategoryIntoView } from '../../hooks/useScrollCategoryIntoView';\nimport { useShouldHideCustomEmojis } from '../../hooks/useShouldHideCustomEmojis';\nimport { isCustomCategory } from '../../typeRefinements/typeRefinements';\nimport { useCategoryNavigationRef } from '../context/ElementRefContext';\n\nimport { CategoryButton } from './CategoryButton';\n\nexport function CategoryNavigation() {\n  const [activeCategory, setActiveCategory] = useState<string | null>(null);\n  const scrollCategoryIntoView = useScrollCategoryIntoView();\n  useActiveCategoryScrollDetection(setActiveCategory);\n  const isSearchMode = useIsSearchMode();\n\n  const categoriesConfig = useCategoriesConfig();\n  const CategoryNavigationRef = useCategoryNavigationRef();\n  const hideCustomCategory = useShouldHideCustomEmojis();\n\n  return (\n    <div\n      className={cx(styles.nav)}\n      role=\"tablist\"\n      aria-label=\"Category navigation\"\n      id=\"epr-category-nav-id\"\n      ref={CategoryNavigationRef}\n    >\n      {categoriesConfig.map(categoryConfig => {\n        const category = categoryFromCategoryConfig(categoryConfig);\n        const isActiveCategory = category === activeCategory;\n\n        if (isCustomCategory(categoryConfig) && hideCustomCategory) {\n          return null;\n        }\n\n        const allowNavigation = !isSearchMode && !isActiveCategory;\n\n        return (\n          <CategoryButton\n            key={category}\n            category={category}\n            isActiveCategory={isActiveCategory}\n            allowNavigation={allowNavigation}\n            categoryConfig={categoryConfig}\n            onClick={() => {\n              setActiveCategory(category);\n              scrollCategoryIntoView(category);\n            }}\n          />\n        );\n      })}\n    </div>\n  );\n}\n\nconst styles = stylesheet.create({\n  nav: {\n    '.': 'epr-category-nav',\n    display: 'flex',\n    flexDirection: 'row',\n    justifyContent: 'space-around',\n    padding: 'var(--epr-header-padding)'\n  },\n  '.epr-search-active': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  },\n  '.epr-main:has(input:not(:placeholder-shown))': {\n    nav: {\n      opacity: '0.3',\n      cursor: 'default',\n      pointerEvents: 'none'\n    }\n  }\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport {\n  commonInteractionStyles,\n  darkMode,\n  stylesheet\n} from '../../../Stylesheet/stylesheet';\nimport { useClearSearch } from '../../../hooks/useFilter';\nimport { Button } from '../../atoms/Button';\n\nimport SVGTimes from './svg/times.svg';\n\nexport function BtnClearSearch() {\n  const clearSearch = useClearSearch();\n\n  return (\n    <Button\n      className={cx(\n        styles.btnClearSearch,\n        commonInteractionStyles.visibleOnSearchOnly\n      )}\n      onClick={clearSearch}\n      aria-label=\"Clear\"\n      title=\"Clear\"\n    >\n      <div className={cx(styles.icnClearnSearch)} />\n    </Button>\n  );\n}\n\nconst HoverDark = {\n  ':hover': {\n    '> .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  }\n};\n\nconst styles = stylesheet.create({\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', HoverDark)\n});\n", "import * as React from 'react';\n\nimport { ClassNames, asSelectors } from '../../../DomUtils/classNames';\nimport { getNormalizedSearchTerm } from '../../../hooks/useFilter';\n\nconst SCOPE = `${asSelectors(ClassNames.emojiPicker)} ${asSelectors(\n  ClassNames.emojiList\n)}`;\n\nconst EMOJI_BUTTON = ['button', asSelectors(ClassNames.emoji)].join('');\nconst CATEGORY = asSelectors(ClassNames.category);\n\nexport function CssSearch({ value }: { value: undefined | string }) {\n  if (!value) {\n    return null;\n  }\n\n  const q = genQuery(value);\n\n  return (\n    <style>{`\n    ${SCOPE} ${EMOJI_BUTTON} {\n      display: none;\n    }\n\n\n    ${SCOPE} ${q} {\n      display: flex;\n    }\n\n    ${SCOPE} ${CATEGORY}:not(:has(${q})) {\n      display: none;\n    }\n  `}</style>\n  );\n}\n\nfunction genQuery(value: string): string {\n  return [\n    EMOJI_BUTTON,\n    '[data-full-name*=\"',\n    getNormalizedSearchTerm(value),\n    '\"]'\n  ].join('');\n}\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\n\nimport SVGMagnifier from './svg/magnifier.svg';\n\nexport function IcnSearch() {\n  return <div className={cx(styles.icnSearch)} />;\n}\n\nconst styles = stylesheet.create({\n  icnSearch: {\n    '.': 'epr-icn-search',\n    content: '',\n    position: 'absolute',\n    top: '50%',\n    left: 'var(--epr-search-bar-inner-padding)',\n    transform: 'translateY(-50%)',\n    width: '20px',\n    height: '20px',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: '0 0',\n    backgroundSize: '20px',\n    backgroundImage: `url(${SVGMagnifier})`\n  },\n  ...darkMode('icnSearch', {\n    backgroundPositionY: '-20px'\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\nimport { useState } from 'react';\n\nimport { darkMode, stylesheet } from '../../../Stylesheet/stylesheet';\nimport {\n  useAutoFocusSearchConfig,\n  useSearchDisabledConfig,\n  useSearchPlaceHolderConfig\n} from '../../../config/useConfig';\nimport { useCloseAllOpenToggles } from '../../../hooks/useCloseAllOpenToggles';\nimport { useFilter } from '../../../hooks/useFilter';\nimport { useIsSkinToneInSearch } from '../../../hooks/useShouldShowSkinTonePicker';\nimport Flex from '../../Layout/Flex';\nimport Relative from '../../Layout/Relative';\nimport { useSearchInputRef } from '../../context/ElementRefContext';\nimport { SkinTonePicker } from '../SkinTonePicker/SkinTonePicker';\n\nimport { BtnClearSearch } from './BtnClearSearch';\nimport { CssSearch } from './CssSearch';\nimport { IcnSearch } from './IcnSearch';\nimport SVGTimes from './svg/times.svg';\n\nexport function SearchContainer() {\n  const searchDisabled = useSearchDisabledConfig();\n\n  const isSkinToneInSearch = useIsSkinToneInSearch();\n\n  if (searchDisabled) {\n    return null;\n  }\n\n  return (\n    <Flex className={cx(styles.overlay)}>\n      <Search />\n\n      {isSkinToneInSearch ? <SkinTonePicker /> : null}\n    </Flex>\n  );\n}\n\nexport function Search() {\n  const [inc, setInc] = useState(0);\n  const closeAllOpenToggles = useCloseAllOpenToggles();\n  const SearchInputRef = useSearchInputRef();\n  const placeholder = useSearchPlaceHolderConfig();\n  const autoFocus = useAutoFocusSearchConfig();\n  const { statusSearchResults, searchTerm, onChange } = useFilter();\n\n  const input = SearchInputRef?.current;\n  const value = input?.value;\n\n  return (\n    <Relative className={cx(styles.searchContainer)}>\n      <CssSearch value={value} />\n      <input\n        // eslint-disable-next-line jsx-a11y/no-autofocus\n        autoFocus={autoFocus}\n        aria-label={'Type to search for an emoji'}\n        onFocus={closeAllOpenToggles}\n        className={cx(styles.search)}\n        type=\"text\"\n        aria-controls=\"epr-search-id\"\n        placeholder={placeholder}\n        onChange={event => {\n          setInc(inc + 1);\n          setTimeout(() => {\n            onChange(event?.target?.value ?? value);\n          });\n        }}\n        ref={SearchInputRef}\n      />\n      {searchTerm ? (\n        <div\n          role=\"status\"\n          className={cx('epr-status-search-results', styles.visuallyHidden)}\n          aria-live=\"polite\"\n          id=\"epr-search-id\"\n          aria-atomic=\"true\"\n        >\n          {statusSearchResults}\n        </div>\n      ) : null}\n      <IcnSearch />\n      <BtnClearSearch />\n    </Relative>\n  );\n}\n\nconst styles = stylesheet.create({\n  overlay: {\n    padding: 'var(--epr-header-padding)',\n    zIndex: 'var(--epr-header-overlay-z-index)'\n  },\n  searchContainer: {\n    '.': 'epr-search-container',\n    flex: '1',\n    display: 'block',\n    minWidth: '0'\n  },\n  visuallyHidden: {\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(50%)',\n    height: '1px',\n    overflow: 'hidden',\n    position: 'absolute',\n    whiteSpace: 'nowrap',\n    width: '1px'\n  },\n  search: {\n    outline: 'none',\n    transition: 'all 0.2s ease-in-out',\n    color: 'var(--epr-search-input-text-color)',\n    borderRadius: 'var(--epr-search-input-border-radius)',\n    padding: 'var(--epr-search-input-padding)',\n    height: 'var(--epr-search-input-height)',\n    backgroundColor: 'var(--epr-search-input-bg-color)',\n    border: '1px solid var(--epr-search-input-bg-color)',\n    width: '100%',\n    ':focus': {\n      backgroundColor: 'var(--epr-search-input-bg-color-active)',\n      border: '1px solid var(--epr-search-border-color)'\n    },\n    '::placeholder': {\n      color: 'var(--epr-search-input-placeholder-color)'\n    }\n  },\n\n  btnClearSearch: {\n    '.': 'epr-btn-clear-search',\n    position: 'absolute',\n    right: 'var(--epr-search-bar-inner-padding)',\n    height: '30px',\n    width: '30px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    padding: '0',\n    borderRadius: '50%',\n    ':hover': {\n      background: 'var(--epr-hover-bg-color)'\n    },\n    ':focus': {\n      background: 'var(--epr-hover-bg-color)'\n    }\n  },\n  icnClearnSearch: {\n    '.': 'epr-icn-clear-search',\n    backgroundColor: 'transparent',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: '20px',\n    height: '20px',\n    width: '20px',\n    backgroundImage: `url(${SVGTimes})`,\n    ':hover': {\n      backgroundPositionY: '-20px'\n    },\n    ':focus': {\n      backgroundPositionY: '-20px'\n    }\n  },\n  ...darkMode('icnClearnSearch', {\n    backgroundPositionY: '-40px'\n  }),\n  ...darkMode('btnClearSearch', {\n    ':hover > .epr-icn-clear-search': {\n      backgroundPositionY: '-60px'\n    }\n  })\n});\n", "import { cx } from 'flairup';\nimport * as React from 'react';\n\nimport { commonInteractionStyles } from '../../Stylesheet/stylesheet';\nimport Relative from '../Layout/Relative';\nimport { CategoryNavigation } from '../navigation/CategoryNavigation';\n\nimport { SearchContainer } from './Search/Search';\n\nexport function Header() {\n  return (\n    <Relative\n      className={cx('epr-header', commonInteractionStyles.hiddenOnReactions)}\n    >\n      <SearchContainer />\n      <CategoryNavigation />\n    </Relative>\n  );\n}\n", "import * as React from 'react';\n\nimport { PickerStyleTag } from './Stylesheet/stylesheet';\nimport { Reactions } from './components/Reactions/Reactions';\nimport { Body } from './components/body/Body';\nimport { ElementRefContextProvider } from './components/context/ElementRefContext';\nimport { PickerConfigProvider } from './components/context/PickerConfigContext';\nimport { useReactionsModeState } from './components/context/PickerContext';\nimport { Preview } from './components/footer/Preview';\nimport { Header } from './components/header/Header';\nimport PickerMain from './components/main/PickerMain';\nimport { compareConfig } from './config/compareConfig';\nimport { useAllowExpandReactions, useOpenConfig } from './config/useConfig';\n\nimport { PickerProps } from './index';\n\nfunction EmojiPicker(props: PickerProps) {\n  return (\n    <ElementRefContextProvider>\n      <PickerStyleTag />\n      <PickerConfigProvider {...props}>\n        <ContentControl />\n      </PickerConfigProvider>\n    </ElementRefContextProvider>\n  );\n}\n\nfunction ContentControl() {\n  const [reactionsDefaultOpen] = useReactionsModeState();\n  const allowExpandReactions = useAllowExpandReactions();\n\n  const [renderAll, setRenderAll] = React.useState(!reactionsDefaultOpen);\n  const isOpen = useOpenConfig();\n\n  React.useEffect(() => {\n    if (reactionsDefaultOpen && !allowExpandReactions) {\n      return;\n    }\n\n    if (!renderAll) {\n      setRenderAll(true);\n    }\n  }, [renderAll, allowExpandReactions, reactionsDefaultOpen]);\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <PickerMain>\n      <Reactions />\n      <ExpandedPickerContent renderAll={renderAll} />\n    </PickerMain>\n  );\n}\n\nfunction ExpandedPickerContent({ renderAll }: { renderAll: boolean }) {\n  if (!renderAll) {\n    return null;\n  }\n\n  return (\n    <>\n      <Header />\n      <Body />\n      <Preview />\n    </>\n  );\n}\n\n// eslint-disable-next-line complexity\nexport default React.memo(EmojiPicker, compareConfig);\n", "import * as React from 'react';\n\nexport default class ErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError() {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    // eslint-disable-next-line no-console\n    console.error('Emoji Picker React failed to render:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null;\n    }\n\n    return this.props.children;\n  }\n}\n", "import * as React from 'react';\n\nimport { EmojiStyle } from '../../types/exposedTypes';\n\nimport { GetEmojiUrl } from './BaseEmojiProps';\nimport { ViewOnlyEmoji } from './ViewOnlyEmoji';\n\nexport function ExportedEmoji({\n  unified,\n  size = 32,\n  emojiStyle = EmojiStyle.APPLE,\n  lazyLoad = false,\n  getEmojiUrl,\n  emojiUrl\n}: {\n  unified: string;\n  emojiStyle?: EmojiStyle;\n  size?: number;\n  lazyLoad?: boolean;\n  getEmojiUrl?: GetEmojiUrl;\n  emojiUrl?: string;\n}) {\n  if (!unified && !emojiUrl && !getEmojiUrl) {\n    return null;\n  }\n\n  return (\n    <ViewOnlyEmoji\n      unified={unified}\n      size={size}\n      emojiStyle={emojiStyle}\n      lazyLoad={lazyLoad}\n      getEmojiUrl={emojiUrl ? () => emojiUrl : getEmojiUrl}\n    />\n  );\n}\n", "import * as React from 'react';\n\nimport EmojiPickerReact from './EmojiPickerReact';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { PickerConfig } from './config/config';\nimport {\n  MutableConfigContext,\n  useDefineMutableConfig\n} from './config/mutableConfig';\n\nexport { ExportedEmoji as Emoji } from './components/emoji/ExportedEmoji';\n\nexport {\n  EmojiStyle,\n  SkinTones,\n  Theme,\n  Categories,\n  EmojiClickData,\n  SuggestionMode,\n  SkinTonePickerLocation\n} from './types/exposedTypes';\n\nexport interface PickerProps extends PickerConfig {}\n\nexport default function EmojiPicker(props: PickerProps) {\n  const MutableConfigRef = useDefineMutableConfig({\n    onEmojiClick: props.onEmojiClick,\n    onReactionClick: props.onReactionClick,\n    onSkinToneChange: props.onSkinToneChange,\n  });\n\n  return (\n    <ErrorBoundary>\n      <MutableConfigContext.Provider value={MutableConfigRef}>\n        <EmojiPickerReact {...props} />\n      </MutableConfigContext.Provider>\n    </ErrorBoundary>\n  );\n}\n"], "mappings": ";;;;;;;;;;;AAAO,SAAS,QAAW,GAAiB;AAC1C,SAAO,CAAC,EAAE,OAAO,CAAkB;AACrC;ACAO,SAAS,iBAAiB,UAA2B;AAC1D,SAAO,SAAS,WAAW,GAAG;AAChC;AAEO,SAAS,iBAAiB,UAA2B;AAC1D,SACE,SAAS,QAAQ,MAChB,aAAa,OACX,SAAS,SAAS,KAAK,SAAS,SAAS,SAAS,MAAM,GAAG,CAAC,CAAC,KAC9D,yBAAyB,QAAQ;AAEvC;AAEO,SAAS,gBACd,UACA,OACiB;AACjB,UACG,SAAS,KAAK,KAAK,OAAO,UAAU,aACrC,CAAC,eAAe,QAAQ,KACxB,CAAC,iBAAiB,QAAQ,KAC1B,CAAC,aAAa,QAAQ;AAE1B;AAEO,SAAS,aAAa,UAA2B;AACtD,SAAO,SAAS,WAAW,QAAQ;AACrC;AAEO,SAAS,cAAc,UAA2B;AACvD,SAAO,aAAa;AACtB;AAEO,SAAS,eAAe,UAA2B;AACxD,SAAO,aAAa;AACtB;AAEO,SAAS,SAAS,OAAiC;AACxD,SAAO,QAAQ,OAAO;AACxB;AAMO,SAAS,yBACd,OACuB;AACvB,SAAO,SAAS,KAAK,MAAM,MAAM,WAAW,GAAG,KAAK,iBAAiB,KAAK;AAC5E;ACnDO,SAAS,WAAW,KAAgB,YAAoB,IAAY;AACzE,SAAO,IAAI,OAAO,OAAO,EAAE,KAAK,SAAS;AAC3C;ACDO,SAAS,WAAW,QAAgB,MAAsB;AAC/D,MAAI,OAAO;AACX,MAAI,KAAK,WAAW;AAAG,WAAO,KAAK,SAAS;AAC5C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,OAAO,KAAK,WAAW,CAAC;AAC9B,YAAQ,QAAQ,KAAK,OAAO;AAC5B,WAAO,OAAO;EAChB;AACA,SAAO,GAAG,UAAU,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;AAC/C;ACTO,SAAS,oBAAoB,UAAkB,OAAuB;AAC3E,MAAI,aAAa,WAAW;AAC1B,WAAO,IAAI,KAAK;EAClB;AAEA,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAqB;AACnD,SAAO,IAAI,QAAQ,mBAAmB,OAAO,EAAE,YAAY;AAC7D;AAEO,SAAS,eAAe,UAAkB,OAAuB;AACtE,SAAO,GAAG,QAAQ,IAAI,KAAK;AAC7B;AAEO,SAAS,QAAQ,KAAqB;AAC3C,SAAO,MAAM,IAAI,GAAG,KAAK;AAC3B;AAEO,SAAS,aAAa,MAAc,MAAsB;AAC/D,SAAO,OAAO,GAAG,IAAI;EAAK,IAAI,KAAK;AACrC;ACXO,IAAM,OAAN,MAAM,MAAK;EAKhB,YACU,OACD,UACA,OACC,UACR;AAJQ,SAAA,QAAA;AACD,SAAA,WAAA;AACA,SAAA,QAAA;AACC,SAAA,WAAA;AAER,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS,eAAe,UAAU,KAAK;AAC5C,UAAM,mBAAmB,KAAK,SAAS,cAAc;MACnD,KAAK,SAAS;IAChB;AACA,SAAK,OAAO,KAAK,SAAS,gBACrB,KAAK,SAAS,iBACf,WAAW,KAAK,MAAM,MAAM,KAAK,MAAM;AAC3C,SAAK,MAAM,WAAW,CAAC,KAAK,QAAQ,kBAAkB,KAAK,IAAI,CAAC;EAClE;EAEO,WAAmB;AACxB,QAAI,YAAY,eAAe,KAAK,SAAS,eAAe;MAC1D,OAAO,KAAK;IACd,CAAC;AAED,gBAAY,eAAe,KAAK,SAAS,gBAAgB;MACvD,MAAM;IACR,CAAC;AAED,WAAO,GAAG,SAAS,KAAK,MAAK,QAAQ,KAAK,UAAU,KAAK,KAAK,CAAC;EACjE;EAEA,OAAO,QAAQ,UAAkB,OAAuB;AACtD,UAAM,sBAAsB,gBAAgB,QAAQ;AACpD,WACE;MACE;MACA,oBAAoB,UAAU,KAAK;IACrC,IAAI;EAER;AACF;AAEO,SAAS,eACd,WACA,EAAE,OAAO,IAAI,QAAQ,GAAG,IAAuC,CAAC,GACxD;AACR,QAAM,SAAS,UAAU,OAAO,CAACA,YAAW,YAAY;AACtD,QAAI,iBAAiB,OAAO,GAAG;AAC7B,aAAOA,aAAY;IACrB;AAEA,QAAI,yBAAyB,OAAO,GAAG;AACrC,aAAOA,aAAY,QAAQ,MAAM,CAAC;IACpC;AAEA,WAAO,WAAW,CAACA,YAAW,OAAO,GAAG,GAAG;EAG7C,GAAG,IAAI;AAGP,SAAO,WAAW,CAAC,QAAQ,QAAQ,KAAK,CAAC,GAAG,GAAG;AACjD;AAEO,IAAM,WAAN,MAAM,UAAS;EAMpB,YACU,OACR,YAA2B,MAC3B;IACE;IACA;EACF,IAGI,CAAC,GACL;AATQ,SAAA,QAAA;AANV,SAAO,gBAA0B,CAAC;AAClC,SAAO,iBAAgC;AACvC,SAAO,YAA2B;AAClC,SAAO,iBAA2B,CAAC;AAajC,SAAK,gBAAgB,gBAAgB,QAAQ,aAAa,IAAI,CAAC;AAC/D,SAAK,iBAAiB,iBAAiB,QAAQ,cAAc,IAAI,CAAC;AAClE,SAAK,SAAS,SAAS;EACzB;EAEQ,SAAS,WAAoC;AACnD,QAAI,CAAC,WAAW;AACd,aAAO;IACT;AAEA,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,YAAY;AACjB,WAAK,iBAAiB;QACpB,KAAK,MAAM;;QAEX,YAAY,KAAK,MAAM;MACzB;IACF;AAEA,WAAO;EACT;EAEA,IAAI,gBAAyB;AAC3B,WAAO,KAAK,cAAc,SAAS,KAAK,KAAK,eAAe,SAAS;EACvE;EAEA,SAAS,WAA6B;AACpC,WAAO,IAAI,UAAS,KAAK,OAAO,WAAW;MACzC,eAAe,KAAK;MACpB,gBAAgB,KAAK;IACvB,CAAC;EACH;EAEA,gBAAgB,cAAgC;AAC9C,WAAO,IAAI,UAAS,KAAK,OAAO,KAAK,gBAAgB;MACnD,gBAAgB,KAAK;MACrB,eAAe,KAAK,cAAc,OAAO,YAAY;IACvD,CAAC;EACH;EAEA,iBAAiB,eAAiC;AAChD,WAAO,IAAI,UAAS,KAAK,OAAO,KAAK,gBAAgB;MACnD,eAAe,KAAK;MACpB,gBAAgB,KAAK,eAAe,OAAO,aAAa;IAC1D,CAAC;EACH;EAEA,WAAW,UAAkB,OAAqB;AAChD,WAAO,IAAI,KAAK,KAAK,OAAO,UAAU,OAAO,IAAI;EACnD;AACF;AC9IO,IAAM,QAAN,MAAY;EAYjB,YACS,MACC,UACR;AAFO,SAAA,OAAA;AACC,SAAA,WAAA;AAVV,SAAQ,eAA6B,CAAC;AAGtC,SAAQ,gBAAwC,CAAC;AACjD,SAAQ,QAAgB;AACxB,SAAO,QAAQ;AAOb,SAAK,KAAK,WAAW,IAAI;AAEzB,SAAK,WAAW,KAAK,eAAe;EACtC;EAEA,WAAmB;AACjB,WAAO,KAAK;EACd;EAEA,OAAO,KAAmB;AACxB,SAAK,QAAQ,aAAa,KAAK,OAAO,GAAG;EAC3C;EAEA,QAAc;AACZ,SAAK;AAEL,QAAI,CAAC,KAAK,UAAU;AAClB;IACF;AAEA,SAAK,SAAS,YAAY,KAAK;EACjC;EAEA,YAAqB;AACnB,WAAO,CAAC,CAAC,KAAK;EAChB;EAEA,iBAA+C;AAE7C,QACE,OAAO,aAAa,eACpB,KAAK,UAAU;IAEf,KAAK,aAAa,MAClB;AACA,aAAO,KAAK;IACd;AAEA,UAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,aAAS,OAAO;AAChB,aAAS,KAAK,KAAK;AACnB,KAAC,KAAK,YAAY,SAAS,MAAM,YAAY,QAAQ;AACrD,WAAO;EACT;EAEA,QAAQ,MAAoB;AAC1B,UAAM,cAAc,KAAK,cAAc,KAAK,GAAG;AAE/C,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO;IACT;AAEA,SAAK,cAAc,KAAK,GAAG,IAAI,KAAK;AACpC,SAAK,aAAa,KAAK,IAAI,IAAI,CAAC,KAAK,UAAU,KAAK,KAAK;AAEzD,SAAK,OAAO,KAAK,SAAS,CAAC;AAC3B,WAAO,KAAK;EACd;AACF;AC/EO,SAAS,MACd,KACA,IACM;AACN,aAAW,OAAO,KAAK;AACrB,OAAG,IAAI,KAAK,GAAG,IAAI,GAAG,CAAC;EACzB;AACF;ACLO,SAAS,MAAM,MAAyB;AAC7C,QAAM,UAAU,KAAK,OAAO,CAACC,UAAmB,QAAQ;AACtD,QAAI,eAAe,KAAK;AACtBA,eAAQ,KAAK,GAAG,GAAG;IACrB,WAAW,OAAO,QAAQ,UAAU;AAClCA,eAAQ,KAAK,GAAG;IAClB,WAAW,MAAM,QAAQ,GAAG,GAAG;AAC7BA,eAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;IACzB,WAAW,OAAO,QAAQ,UAAU;AAElC,aAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC5C,YAAI,OAAO;AACTA,mBAAQ,KAAK,GAAG;QAClB;MACF,CAAC;IACH;AAEA,WAAOA;EACT,GAAG,CAAC,CAAa;AAEjB,SAAO,WAAW,SAAS,GAAG,EAAE,KAAK;AACvC;ACEO,SAAS,YACd,MACA,UACmB;AACnB,QAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ;AAEtC,SAAO;IACL;IACA,UAAU,MAAM,SAAS,KAAK,KAAK;IACnC,WAAW,MAAM,UAAU,KAAK,KAAK;EACvC;AAEA,WAAS,OAAyBC,SAA6B;AAC7D,UAAM,eAAgC,CAAC;AAEvC,yBAAqB,OAAOA,SAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;MACvD,CAAC,CAAC,WAAWA,UAAQ,QAAQ,MAAM;AACjC,sBAAc,OAAOA,UAAkB,QAAQ,EAAE;UAC/C,CAAC,cAAc;AACb,2BAAe,WAAgB,SAAS;UAC1C;QACF;MACF;IACF;AAKA,UAAM,MAAM;AAEZ,WAAO;AAEP,aAAS,eAAeC,OAAS,WAAmB;AAClD,mBAAaA,KAA6B,IACxC,aAAaA,KAA6B,KAAK,oBAAI,IAAY;AACjE,mBAAaA,KAA6B,EAAE,IAAI,SAAS;IAC3D;EACF;AACF;AAIA,SAAS,qBACP,OACAD,SACA,UACA;AACA,QAAM,SAA4C,CAAC;AAEnD,QAAMA,SAAQ,CAAC,KAAa,UAAU;AACpC,QAAI,iBAAiB,GAAG,GAAG;AACzB,aAAO;QACL;QACA;QACA,SAAS,gBAAgB,GAAG;MAC9B,EAAE,QAAQ,CAAC,SAAS,OAAO,KAAK,IAAI,CAAC;IACvC;AAIA,WAAO,KAAK,CAAC,KAAKA,QAAO,GAAG,GAAG,SAAS,SAAS,GAAG,CAAC,CAAC;EACxD,CAAC;AAED,SAAO;AACT;AAEA,SAAS,cACP,OACAA,SACA,UACU;AACV,QAAM,SAAmB,oBAAI,IAAY;AAEzC,QAAMA,SAAQ,CAAC,UAAU,UAAU;AACjC,QAAI,MAA8B,CAAC;AAGnC,QAAI,iBAAiB,QAAQ,GAAG;AAC9B,YAAM;QACJ;QACA;QACA,SAAS,iBAAiB,QAAQ;MACpC;IAEF,WAAW,cAAc,QAAQ,GAAG;AAClC,YAAM,QAAQ,KAAoB;IACpC,WAAW,aAAa,QAAQ,GAAG;AACjC,YAAM,iBAAiB,OAAO,OAAiB,UAAU,QAAQ;IAGnE,WAAW,eAAe,QAAQ,GAAG;AACnC,YAAM,kBAAkB,OAAO,OAA6B,QAAQ;IAGtE,WAAW,gBAAgB,UAAU,KAAK,GAAG;AAC3C,YAAM,OAAO,SAAS,WAAW,UAAU,KAAK;AAChD,YAAM,QAAQ,IAAI;AAClB,aAAO,IAAI,KAAK,IAAI;IACtB;AAEA,WAAO,aAAa,KAAK,MAAM;EACjC,CAAC;AAED,SAAO;AACT;AAEA,SAAS,aAAa,MAA8B,IAAiB;AACnE,OAAK,QAAQ,CAAC,cAAc,GAAG,IAAI,SAAS,CAAC;AAC7C,SAAO;AACT;AAGA,SAAS,kBACP,OACAA,SACA,UACA;AACA,QAAM,UAAoB,oBAAI,IAAY;AAE1C,QAAM,YAAsB,CAAC;AAC7B,QAAMA,SAAQ,CAAC,UAAkB,UAAU;AACzC,QAAI,gBAAgB,UAAU,KAAK,GAAG;AACpC,gBAAU,KAAK,KAAK,QAAQ,UAAU,KAAK,CAAC;AAC5C;IACF;AACA,UAAM,MAAM,cAAc,OAAO,SAAS,CAAC,GAAG,QAAQ;AACtD,iBAAa,KAAK,OAAO;EAC3B,CAAC;AAED,MAAI,CAAC,SAAS,gBAAgB;AAC5B,WAAO;EACT;AAEA,MAAI,UAAU,QAAQ;AACpB,UAAM,SAAS,UAAU,KAAK,GAAG;AACjC,UAAM;MACJ,GAAG,eAAe,SAAS,eAAe;QACxC,OAAO,SAAS;MAClB,CAAC,CAAC,KAAK,MAAM;IACf;EACF;AAEA,UAAQ,IAAI,SAAS,cAAc;AACnC,SAAO;AACT;AAEA,SAAS,iBACP,OACAA,SACA,YACA,UACA;AACA,QAAM,OAAO,aAAa,IAAI;AAI9B,QAAM,SAAS,cAAc,OAAOA,SAAQ,QAAQ;AAEpD,QAAM,OAAO,GAAG;AAEhB,SAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1LA,IAAYE;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAAA,gBAAAA,IAAAA;AACAA,EAAAA,YAAAA,cAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACAA,EAAAA,YAAAA,iBAAAA,IAAAA;AACAA,EAAAA,YAAAA,oBAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,WAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACAA,EAAAA,YAAAA,aAAAA,IAAAA;AACAA,EAAAA,YAAAA,MAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,iBAAAA,IAAAA;AACAA,EAAAA,YAAAA,WAAAA,IAAAA;AACAA,EAAAA,YAAAA,WAAAA,IAAAA;AACF,GArBYA,eAAAA,aAAU,CAAA,EAAA;SAuBNC,cAAWA;oCAAIC,aAAwB,IAAAC,MAAAC,IAAA,GAAAC,OAAA,GAAAA,OAAAD,MAAAC,QAAA;AAAxBH,eAAwBG,IAAA,IAAAC,UAAAD,IAAA;;AACrD,SAAOH,WAAWK,IAAI,SAAAC,GAAC;AAAA,WAAA,MAAQA;GAAG,EAAEC,KAAK,EAAE;AAC7C;ACpBO,IAAMC,aAAaC,YAAY,OAAO,IAAI;AAEjD,IAAMC,SAAS;EACbC,SAAS;EACTC,SAAS;EACTC,eAAe;EACfC,YAAY;EACZC,UAAU;;AAGL,IAAMC,eAAeR,WAAWS,OAAO;EAC5CP,QAAMQ,SAAA;IACJ,KAAKpB,WAAWY;KACbA,MAAM;CAEZ;AAEM,IAAMS,qBAAiBC,mBAAW,SAASD,kBAAcA;AAC9D,aACEC,4BAAAA,SAAAA;IACEC,0BAAwB;IACxBC,yBAAyB;MAAEC,QAAQf,WAAWgB,SAAQ;;;AAG5D,CAAC;AAEM,IAAMC,0BAA0BjB,WAAWS,OAAO;EACvD,aAAa;IACX,uCAAuC;MACrCS,aAAa;QACX,UAAU;UACRd,SAAS;UACTe,qBAAqB;;;MAGzBC,gBAAcV,SAAA;QACZ,KAAKpB,WAAW8B;SACblB,MAAM;;IAGb,mCAAmC;MACjCmB,qBAAqBnB;;;EAGzBoB,mBAAmB;IACjBC,YAAY;;EAEd,kBAAkB;IAChBD,mBAAmB;MACjBE,QAAQ;MACRC,OAAO;MACPrB,SAAS;MACTC,eAAe;MACfE,UAAU;;;EAGd,6CAA6C;IAC3CW,aAAa;MACX,UAAU;QACRd,SAAS;QACTe,qBAAqB;;MAEvB,gBAAgB;QACdf,SAAS;QACTe,qBAAqB;;;IAGzBE,qBAAmBX,SAAA;MACjB,KAAK;OACFR,MAAM;;CAGd;AAED,SAAgBwB,SAASC,KAAaC,OAAa;;AACjD,SAAO;IACL,oBAAiBC,gBAAA,CAAA,GAAAA,cACdF,GAAG,IAAGC,OAAKC;IAEd,oBAAiBC,gBAAA,CAAA,GAAAA,cACdH,GAAG,IAAG;MACL,uCAAuCC;OACxCE;;AAGP;ACvFA,SAAgBC,cAAcC,MAAoBC,MAAkB;;AAClE,MAAMC,oBAAgBC,qBAAGH,KAAKI,iBAAY,OAAAD,qBAAI,CAAA;AAC9C,MAAME,oBAAgBC,qBAAGL,KAAKG,iBAAY,OAAAE,qBAAI,CAAA;AAC9C,SACEN,KAAKO,SAASN,KAAKM,QACnBP,KAAKQ,iBAAiBP,KAAKO,gBAC3BR,KAAKS,yBAAyBR,KAAKQ,wBACnCT,KAAKU,sBAAsBT,KAAKS,qBAChCV,KAAKW,sBAAsBV,KAAKU,qBAChCX,KAAKY,oBAAoBX,KAAKW,mBAC9BZ,KAAKa,sBAAsBZ,KAAKY,qBAChCb,KAAKc,oBAAoBb,KAAKa,mBAC9Bd,KAAKe,eAAed,KAAKc,cACzBf,KAAKgB,UAAUf,KAAKe,SACpBhB,KAAKiB,wBAAwBhB,KAAKgB,uBAClCjB,KAAKkB,mBAAmBjB,KAAKiB,kBAC7BlB,KAAKmB,cAAclB,KAAKkB,aACxBnB,KAAKR,WAAWS,KAAKT,UACrBQ,KAAKP,UAAUQ,KAAKR,SACpBO,KAAKoB,UAAUnB,KAAKmB,SACpBpB,KAAKqB,mBAAmBpB,KAAKoB,kBAC7BrB,KAAKsB,2BAA2BrB,KAAKqB,0BACrCpB,iBAAiBqB,WAAWlB,iBAAiBkB;AAEjD;AC3BO,IAAMC,oBAAoB;EAC/B;EACA;EACA;EACA;EACA;EACA;EACA;;AAAQ;ICIEC;CAAZ,SAAYA,iBAAc;AACxBA,EAAAA,gBAAAA,QAAAA,IAAAA;AACAA,EAAAA,gBAAAA,UAAAA,IAAAA;AACF,GAHYA,mBAAAA,iBAAc,CAAA,EAAA;AAK1B,IAAYC;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,UAAAA,IAAAA;AACF,GANYA,eAAAA,aAAU,CAAA,EAAA;AAQtB,IAAYC;CAAZ,SAAYA,QAAK;AACfA,EAAAA,OAAAA,MAAAA,IAAAA;AACAA,EAAAA,OAAAA,OAAAA,IAAAA;AACAA,EAAAA,OAAAA,MAAAA,IAAAA;AACF,GAJYA,UAAAA,QAAK,CAAA,EAAA;AAMjB,IAAYC;CAAZ,SAAYA,YAAS;AACnBA,EAAAA,WAAAA,SAAAA,IAAAA;AACAA,EAAAA,WAAAA,OAAAA,IAAAA;AACAA,EAAAA,WAAAA,cAAAA,IAAAA;AACAA,EAAAA,WAAAA,QAAAA,IAAAA;AACAA,EAAAA,WAAAA,aAAAA,IAAAA;AACAA,EAAAA,WAAAA,MAAAA,IAAAA;AACF,GAPYA,cAAAA,YAAS,CAAA,EAAA;AASrB,IAAYC;CAAZ,SAAYA,aAAU;AACpBA,EAAAA,YAAAA,WAAAA,IAAAA;AACAA,EAAAA,YAAAA,QAAAA,IAAAA;AACAA,EAAAA,YAAAA,gBAAAA,IAAAA;AACAA,EAAAA,YAAAA,gBAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,eAAAA,IAAAA;AACAA,EAAAA,YAAAA,YAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,SAAAA,IAAAA;AACAA,EAAAA,YAAAA,OAAAA,IAAAA;AACF,GAXYA,eAAAA,aAAU,CAAA,EAAA;AAatB,IAAYC;CAAZ,SAAYA,yBAAsB;AAChCA,EAAAA,wBAAAA,QAAAA,IAAAA;AACAA,EAAAA,wBAAAA,SAAAA,IAAAA;AACF,GAHYA,2BAAAA,yBAAsB,CAAA,EAAA;;AChDlC,IAAMC,oBAAkC,CACtCF,WAAWG,WACXH,WAAWI,QACXJ,WAAWK,gBACXL,WAAWM,gBACXN,WAAWO,YACXP,WAAWQ,eACXR,WAAWS,YACXT,WAAWU,SACXV,WAAWW,SACXX,WAAWY,KAAK;AAGX,IAAMC,kBAAkC;EAC7CC,MAAM;EACNC,UAAUf,WAAWG;;AAQvB,IAAMa,oBAAgBC,oBAAA,CAAA,GAAAA,kBACnBjB,WAAWG,SAAS,IAAG;EACtBY,UAAUf,WAAWG;EACrBW,MAAM;GACPG,kBACAjB,WAAWI,MAAM,IAAG;EACnBW,UAAUf,WAAWI;EACrBU,MAAM;GACPG,kBACAjB,WAAWK,cAAc,IAAG;EAC3BU,UAAUf,WAAWK;EACrBS,MAAM;GACPG,kBACAjB,WAAWM,cAAc,IAAG;EAC3BS,UAAUf,WAAWM;EACrBQ,MAAM;GACPG,kBACAjB,WAAWO,UAAU,IAAG;EACvBQ,UAAUf,WAAWO;EACrBO,MAAM;GACPG,kBACAjB,WAAWQ,aAAa,IAAG;EAC1BO,UAAUf,WAAWQ;EACrBM,MAAM;GACPG,kBACAjB,WAAWS,UAAU,IAAG;EACvBM,UAAUf,WAAWS;EACrBK,MAAM;GACPG,kBACAjB,WAAWU,OAAO,IAAG;EACpBK,UAAUf,WAAWU;EACrBI,MAAM;GACPG,kBACAjB,WAAWW,OAAO,IAAG;EACpBI,UAAUf,WAAWW;EACrBG,MAAM;GACPG,kBACAjB,WAAWY,KAAK,IAAG;EAClBG,UAAUf,WAAWY;EACrBE,MAAM;GACPG;AAGH,SAAgBC,qBACdC,WAA8C;AAE9C,SAAOjB,kBAAkBlE,IAAI,SAAA+E,UAAQ;AACnC,WAAAlE,SAAA,CAAA,GACKmE,iBAAiBD,QAAQ,GACxBI,aAAaA,UAAUJ,QAAQ,KAAKI,UAAUJ,QAAQ,CAAC;GAE9D;AACH;AAEA,SAAgBK,2BAA2BL,UAAwB;AACjE,SAAOA,SAASA;AAClB;AAEA,SAAgBM,+BAA+BN,UAAwB;AACrE,SAAOA,SAASD;AAClB;AAWA,SAAgBQ,sBACdC,sBACAJ,WAAAA;;MADAI,yBAAAA,QAAAA;AAAAA,2BAA2C,CAAA;;AAAE,MAC7CJ,cAAAA,QAAAA;AAAAA,gBAAqC,CAAA;;AAErC,MAAMK,QAAQ,CAAA;AAEd,MAAIL,UAAUM,mBAAmB7B,eAAe8B,QAAQ;AACtDF,UAAMxB,WAAWG,SAAS,IAAIU;;AAGhC,MAAMc,OAAOT,qBAAqBM,KAAK;AACvC,MAAI,GAAAI,wBAACL,yBAAoB,QAApBK,sBAAsBlC,SAAQ;AACjC,WAAOiC;;AAGT,SAAOJ,qBAAqBvF,IAAI,SAAA+E,UAAQ;AACtC,QAAI,OAAOA,aAAa,UAAU;AAChC,aAAOc,wBAAwBd,UAAUS,MAAMT,QAAQ,CAAC;;AAG1D,WAAAlE,SAAA,CAAA,GACKgF,wBAAwBd,SAASA,UAAUS,MAAMT,SAASA,QAAQ,CAAC,GACnEA,QAAQ;GAEd;AACH;AAEA,SAASc,wBACPd,UACAe,UAAAA;MAAAA,aAAAA,QAAAA;AAAAA,eAA2B,CAAA;;AAE3B,SAAOC,OAAOC,OAAOhB,iBAAiBD,QAAQ,GAAGe,QAAQ;AAC3D;AChIA,IAAMG,gBACJ;AACF,IAAMC,mBACJ;AACF,IAAMC,kBACJ;AACF,IAAMC,iBACJ;AAEF,SAAgBC,OAAOnD,YAAsB;AAC3C,UAAQA,YAAU;IAChB,KAAKW,WAAWyC;AACd,aAAOH;IACT,KAAKtC,WAAW0C;AACd,aAAOH;IACT,KAAKvC,WAAW2C;AACd,aAAON;IACT,KAAKrC,WAAW4C;IAChB;AACE,aAAOR;;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA,IAAMS,qBAAqB,CACzB3C,UAAU4C,SACV5C,UAAU6C,OACV7C,UAAU8C,cACV9C,UAAU+C,QACV/C,UAAUgD,aACVhD,UAAUiD,IAAI;AAGT,IAAMC,iBAAiBlB,OAAOmB,QAAQnD,SAAS,EAAEoD,OACtD,SAACC,KAAGC,MAAA;MAAGvF,MAAGuF,KAAA,CAAA,GAAEtF,QAAKsF,KAAA,CAAA;AACfD,MAAIrF,KAAK,IAAID;AACb,SAAOsF;AACT,GACA,CAAA,CAA4B;AAGvB,IAAME,kBAGTZ,mBAAmBS,OACrB,SAACI,QAAQC,UAAQ;AAAA,MAAAC;AAAA,SACf1B,OAAOC,OAAOuB,SAAME,iBAAA,CAAA,GAAAA,eACjBD,QAAQ,IAAGA,UAAQC,eAAA;AACpB,GACJ,CAAA,CAAE;ACzBJ,IAAYC;CAAZ,SAAYA,kBAAe;AACzBA,EAAAA,iBAAAA,MAAAA,IAAAA;AACAA,EAAAA,iBAAAA,SAAAA,IAAAA;AACAA,EAAAA,iBAAAA,YAAAA,IAAAA;AACAA,EAAAA,iBAAAA,UAAAA,IAAAA;AACAA,EAAAA,iBAAAA,QAAAA,IAAAA;AACF,GANYA,oBAAAA,kBAAe,CAAA,EAAA;ACCpB,IAAMC,yBAAoC,CAAA;AAEjDC,WAAW,WAAA;AACTC,YAAUV,OAAO,SAACW,aAAaC,OAAK;AAClCC,eAAWD,KAAK;AAChB,WAAOD;KACNH,sBAAmC;AACxC,CAAC;AAID,SAAgBK,WAAWD,OAAgB;AACzC,MAAME,mBAAmBC,WAAWH,KAAK,EACtCI,KAAI,EACJjI,KAAK,EAAE,EACPkI,YAAW,EACXC,QAAQ,gBAAgB,EAAE,EAC1BC,MAAM,EAAE;AAEXL,mBAAiBM,QAAQ,SAAAC,OAAI;;AAC3Bb,2BAAuBa,KAAI,KAACC,wBAAGd,uBAAuBa,KAAI,MAAC,OAAAC,wBAAI,CAAA;AAE/Dd,2BAAuBa,KAAI,EAAEE,aAAaX,KAAK,CAAC,IAAIA;GACrD;AACH;SCfgBG,WAAWH,OAAe;;AACxC,UAAAY,wBAAOZ,MAAML,gBAAgB5C,IAAI,MAAC,OAAA6D,wBAAI,CAAA;AACxC;AAEA,SAAgBC,QAAQb,OAAgB;AACtC,SAAOc,WAAWd,MAAML,gBAAgBoB,QAAQ,CAAC;AACnD;AAEA,SAAgBC,UAAUhB,OAAgB;AACxC,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,SAAOG,WAAWH,KAAK,EAAE,CAAC;AAC5B;AAEA,SAAgBiB,uBAAuBC,SAAe;AACpD,MAAMC,QAAQD,QAAQX,MAAM,GAAG;AAC/B,MAAAa,gBAAmBD,MAAME,OAAO,GAAG,CAAC,GAA7B5B,WAAQ2B,cAAA,CAAA;AAEf,MAAI7B,gBAAgBE,QAAQ,GAAG;AAC7B,WAAO0B,MAAMhJ,KAAK,GAAG;;AAGvB,SAAO+I;AACT;AAEA,SAAgBP,aAAaX,OAAkBP,UAAiB;;AAC9D,MAAMyB,UAAUlB,MAAML,gBAAgBuB,OAAO;AAE7C,MAAI,CAACzB,YAAY,CAAC6B,mBAAmBtB,KAAK,GAAG;AAC3C,WAAOkB;;AAGT,UAAAK,wBAAOC,sBAAsBxB,OAAOP,QAAQ,MAAC,OAAA8B,wBAAIL;AACnD;AAEA,SAAgBO,iBAAiBzE,UAAoB;;AAEnD,UAAA0E,mBAAOC,UAAM,OAAA,SAANA,OAAS3E,QAAQ,MAAC,OAAA0E,mBAAI,CAAA;AAC/B;AAGA,SAAgBE,kBACdV,SACA/F,YAAsB;AAEtB,SAAA,KAAUmD,OAAOnD,UAAU,IAAI+F,UAAO;AACxC;AAEA,SAAgBW,gBAAgB7B,OAAgB;;AAC9C,UAAA8B,yBAAO9B,MAAML,gBAAgBoC,UAAU,MAAC,OAAAD,yBAAI,CAAA;AAC9C;AAEA,SAAgBR,mBAAmBtB,OAAgB;AACjD,SAAO6B,gBAAgB7B,KAAK,EAAErE,SAAS;AACzC;AAEA,SAAgB6F,sBACdxB,OACAP,UAAiB;AAEjB,SAAOA,WACHoC,gBAAgB7B,KAAK,EAAEgC,KAAK,SAAAC,WAAS;AAAA,WAAIA,UAAUC,SAASzC,QAAQ;OACpEkB,aAAaX,KAAK;AACxB;AAEA,SAAgBmC,eAAejB,SAAgB;AAC7C,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAIkB,mBAAmBlB,OAAO,GAAG;AAC/B,WAAOkB,mBAAmBlB,OAAO;;AAGnC,MAAMmB,kBAAkBpB,uBAAuBC,OAAO;AACtD,SAAOkB,mBAAmBC,eAAe;AAC3C;AAEO,IAAMvC,YAAwB9B,OAAOsE,OAAOX,MAAM,EAAEvB,KAAI;AAE/D,SAAgBmC,gBAAgB/H,cAA2B;AACzDmH,SAAO1F,WAAWI,MAAM,EAAEV,SAAS;AAEnCnB,eAAagG,QAAQ,SAAAR,OAAK;AACxB,QAAMwC,YAAYC,qBAAqBzC,KAAK;AAE5C2B,WAAO1F,WAAWI,MAAM,EAAEqG,KAAKF,SAAkB;AAEjD,QAAIJ,mBAAmBI,UAAU7C,gBAAgBuB,OAAO,CAAC,GAAG;AAC1D;;AAGFpB,cAAU4C,KAAKF,SAAS;AACxBJ,uBAAmBI,UAAU7C,gBAAgBuB,OAAO,CAAC,IAAIsB;AACzDvC,eAAWuC,SAAS;GACrB;AACH;AAEA,SAASC,qBAAqBzC,OAAkB;;AAC9C,SAAAV,OAAA,CAAA,GAAAA,KACGK,gBAAgB5C,IAAI,IAAGiD,MAAM2C,MAAM1K,IAAI,SAAA8E,MAAI;AAAA,WAAIA,KAAKsD,YAAW;MAAGf,KAClEK,gBAAgBuB,OAAO,IAAGlB,MAAM4C,GAAGvC,YAAW,GAAEf,KAChDK,gBAAgBoB,QAAQ,IAAG,KAAGzB,KAC9BK,gBAAgBkD,MAAM,IAAG7C,MAAM6C,QAAMvD;AAE1C;AAEA,IAAM8C,qBAEF,CAAA;AAEJvC,WAAW,WAAA;AACTC,YAAUV,OAAO,SAACU,YAAWgD,OAAK;AAChChD,IAAAA,WAAUa,aAAamC,KAAK,CAAC,IAAIA;AAEjC,QAAIxB,mBAAmBwB,KAAK,GAAG;AAC7BjB,sBAAgBiB,KAAK,EAAEtC,QAAQ,SAAAyB,WAAS;AACtCnC,QAAAA,WAAUmC,SAAS,IAAIa;OACxB;;AAGH,WAAOhD;KACNsC,kBAAkB;AACvB,CAAC;AAED,SAAgBW,2BAA2B7B,SAAe;AACxD,MAAA8B,iBAA8B9B,QAAQX,MAAM,GAAG,GAAtC0C,oBAAiBD,eAAA,CAAA;AAC1B,SAAOrE,mBAAmBuD,SAASe,iBAAiB,IAChDA,oBACA;AACN;ACxHA,IAAMC,uBAAuB,CAAC,aAAa,aAAa,WAAW;AAE5D,IAAMC,6BAA6B;AACnC,IAAMC,kCAAkC;AACxC,IAAMC,wBACX;AACK,IAAMC,kCACX,aAAaD;AACR,IAAME,wCACX,eAAeF;AAEjB,SAAgBG,YACdC,YAAAA;;MAAAA,eAAAA,QAAAA;AAAAA,iBAA2B,CAAA;;AAE3B,MAAM7F,OAAO8F,iBAAgB;AAE7B,MAAMC,gBAAgB3F,OAAOC,OAC3BL,KAAK+F,gBAAaC,wBAClBH,WAAWE,kBAAa,OAAAC,wBAAI,CAAA,CAAE;AAEhC,MAAMC,SAAS7F,OAAOC,OAAOL,MAAM6F,UAAU;AAE7C,MAAMK,aAAavG,sBAAsBkG,WAAWK,YAAY;IAC9DpG,gBAAgBmG,OAAOxI;GACxB;AAEDwI,SAAOE,aAAavD,QAAQ,SAAAR,OAAK;AAC/B6D,WAAOG,cAAcC,IAAIjE,KAAK;GAC/B;AAEDuC,mBAAe2B,uBAACL,OAAOrJ,iBAAY,OAAA0J,uBAAI,CAAA,CAAE;AAEzC,MAAMxI,yBAAyBmI,OAAOpI,iBAClCS,uBAAuBiI,UACvBN,OAAOnI;AAEX,SAAA5C,SAAA,CAAA,GACK+K,QAAM;IACTC;IACAH;IACAjI;;AAEJ;AAEA,SAAgBgI,mBAAgBA;AAC9B,SAAO;IACLxI,iBAAiB;IACjB4I,YAAY3G,qBAAoB;IAChC5B,WAAW;IACXf,cAAc,CAAA;IACdQ,iBAAiBgB,UAAU4C;IAC3BzD,YAAYW,WAAW4C;IACvB9D,cAAc;IACdwJ,aAAaxC;IACbhI,QAAQ;IACR0B,gBAAgB;IAChBqI,eAAa7K,SAAA,CAAA,GACRuL,iBAAiB;IAEtB5I,gBAAgB;IAChBX,mBAAmBqI;IACnBpI,mBAAmBoI;IACnBzH,wBAAwBQ,uBAAuBoI;IAC/CrJ,mBAAmB;IACnBO,OAAO,CAAA;IACPH,qBAAqBQ,eAAe0I;IACpCnJ,OAAOW,MAAM8C;IACbmF,eAAe,IAAIQ,IAAYtB,oBAAoB;IACnDrJ,OAAO;IACPgB,sBAAsB;IACtB4J,WAAW7I;IACXjB,MAAM;IACN+J,sBAAsB;IACtBX,cAAc,CAAA;;AAElB;AAqCA,IAAMM,oBAAmC;EACvCM,cAAc;EACdC,gBAAgB;EAChBC,aAAa;;;AC5Hf,IAAMC,oBAAgB9L,4BACpB0K,iBAAgB,CAAE;AAGpB,SAAgBqB,qBAAoBzF,MAAA;MAAG0F,WAAQ1F,KAAR0F,UAAanB,SAAMoB,8BAAA3F,MAAA4F,SAAA;AACxD,MAAMC,eAAeC,aAAavB,MAAM;AAExC,aACE7K,4BAAC8L,cAAcO,UAAQ;IAACrL,OAAOmL;KAC5BH,QAAQ;AAGf;AAEA,SAAgBI,aAAavB,QAAoB;;AAC/C,MAAAyB,sBAAwCtM,uBAAe,WAAA;AAAA,WACrDwK,YAAYK,MAAM;MADbsB,eAAYG,gBAAA,CAAA,GAAEC,kBAAeD,gBAAA,CAAA;AAIpCtM,8BAAgB,WAAA;AACd,QAAImB,cAAcgL,cAActB,MAAM,GAAG;AACvC;;AAEF0B,oBAAgB/B,YAAYK,MAAM,CAAC;KAGlC,EAAAK,uBACDL,OAAOrJ,iBAAY,OAAA,SAAnB0J,qBAAqBvI,QACrBkI,OAAOlJ,MACPkJ,OAAOjJ,cACPiJ,OAAOhJ,sBACPgJ,OAAO/I,mBACP+I,OAAO9I,mBACP8I,OAAO7I,iBACP6I,OAAO5I,mBACP4I,OAAO3I,iBACP2I,OAAO1I,YACP0I,OAAOzI,OACPyI,OAAOxI,qBACPwI,OAAOvI,gBACPuI,OAAOtI,WACPsI,OAAOjK,QACPiK,OAAOhK,OACPgK,OAAOpI,gBACPoI,OAAOnI,wBACPmI,OAAOa,oBAAoB,CAC5B;AAED,SAAOS;AACT;AAEA,SAAgBK,kBAAeA;AAC7B,aAAOxM,yBAAiB8L,aAAa;AACvC;AC1DO,IAAMW,uBAAuBzM,aAAAA,QAAM0M,cAExC,CAAA,CAA2C;AAE7C,SAAgBC,mBAAgBA;AAC9B,MAAMC,gBAAgB5M,aAAAA,QAAM6M,WAAWJ,oBAAoB;AAC3D,SAAOG;AACT;AAEA,SAAgBE,uBACdjC,QAAqB;AAErB,MAAMkC,mBAAmB/M,aAAAA,QAAMgN,OAAsB;IACnDC,cAAcpC,OAAOoC,gBAAgBC;IACrCC,iBAAiBtC,OAAOsC,mBAAmBtC,OAAOoC;IAClDG,kBAAkBvC,OAAOuC,oBAAoBF;GAC9C;AAEDlN,eAAAA,QAAMqN,UAAU,WAAA;AACdN,qBAAiBO,QAAQL,eAAepC,OAAOoC,gBAAgBC;AAC/DH,qBAAiBO,QAAQH,kBACvBtC,OAAOsC,mBAAmBtC,OAAOoC;KAClC,CAACpC,OAAOoC,cAAcpC,OAAOsC,eAAe,CAAC;AAEhDnN,eAAAA,QAAMqN,UAAU,WAAA;AACdN,qBAAiBO,QAAQF,mBAAmBvC,OAAOuC,oBAAoBF;KACtE,CAACrC,OAAOuC,gBAAgB,CAAC;AAE5B,SAAOL;AACT;AAEA,SAASG,YAASA;AAAAA;ACjBlB,IAAYK;CAAZ,SAAYA,qBAAkB;AAC5BA,EAAAA,oBAAAA,WAAAA,IAAAA;AACAA,EAAAA,oBAAAA,QAAAA,IAAAA;AACF,GAHYA,uBAAAA,qBAAkB,CAAA,EAAA;AAK9B,SAAgBC,6BAA0BA;;AACxC,MAAAC,mBAAiDjB,gBAAe,GAAxD1K,oBAAiB2L,iBAAjB3L,mBAAmBC,oBAAiB0L,iBAAjB1L;AAC3B,UAAA2L,QACE,CAAC5L,mBAAmBC,iBAAiB,EAAEiH,KACrC,SAAA2E,GAAC;AAAA,WAAIA,MAAMxD;SACZ,OAAAuD,QAAIvD;AAET;AAEA,SAAgByD,2BAAwBA;AACtC,MAAAC,oBAA4BrB,gBAAe,GAAnCxK,kBAAe6L,kBAAf7L;AACR,SAAOA;AACT;AAEA,SAAgB8L,0BAAuBA;AACrC,MAAAC,oBAAiCvB,gBAAe,GAAxCd,uBAAoBqC,kBAApBrC;AACR,SAAOA;AACT;AAEA,SAAgBsC,6BAA0BA;AACxC,MAAAC,oBAA8BzB,gBAAe,GAArCvK,oBAAiBgM,kBAAjBhM;AACR,SAAOA;AACT;AAEA,SAAgBiM,sBAAmBA;AACjC,MAAAC,oBAAuB3B,gBAAe,GAA9BrK,aAAUgM,kBAAVhM;AACR,SAAOA;AACT;AAEA,SAAgBiM,2BAAwBA;AACtC,MAAAC,oBAA4B7B,gBAAe,GAAnCtK,kBAAemM,kBAAfnM;AACR,SAAOA;AACT;AAEA,SAAgBoM,sBAAmBA;AACjC,MAAAC,oBAAuB/B,gBAAe,GAA9B1B,aAAUyD,kBAAVzD;AACR,SAAOA;AACT;AAEA,SAAgB0D,wBAAqBA;AACnC,MAAAC,oBAAyBjC,gBAAe,GAAhChL,eAAYiN,kBAAZjN;AACR,SAAOA;AACT;AAEA,SAAgBkN,gBAAaA;AAC3B,MAAAC,oBAAiBnC,gBAAe,GAAxB7K,OAAIgN,kBAAJhN;AACR,SAAOA;AACT;AAEA,SAAgBiN,sBACdC,kBAAoC;;AAEpC,MAAAC,oBAAoBnC,iBAAgB,GAA5BW,UAAOwB,kBAAPxB;AAER,MAAMyB,WAAOzI,OACVuI,qBAAqBtB,mBAAmByB,YACrC1B,QAAQH,kBACRG,QAAQL,iBAAY,OAAA3G,OAAKgH,QAAQL;AAEvC,SAAO8B,WAAY,WAAA;EAAA;AACrB;AAEA,SAAgBE,4BAAyBA;AAEvC,MAAAC,qBAAoBvC,iBAAgB,GAA5BW,UAAO4B,mBAAP5B;AAER,SAAOA,QAAQF,oBAAqB,WAAA;EAAA;AACtC;AAEA,SAAgB+B,mBAAgBA;AAC9B,MAAAC,qBAA0B5C,gBAAe,GAAjC7B,gBAAayE,mBAAbzE;AACR,SAAOA;AACT;AAEA,SAAgB0E,iBAAcA;AAC5B,MAAAC,qBAAkB9C,gBAAe,GAAzBpK,QAAKkN,mBAALlN;AAER,SAAOA;AACT;AAEA,SAAgBmN,+BAA4BA;AAC1C,MAAAC,qBAAgChD,gBAAe,GAAvCnK,sBAAmBmN,mBAAnBnN;AACR,SAAOA;AACT;AAEA,SAAgBoN,0BAAuBA;AACrC,MAAAC,qBAA2BlD,gBAAe,GAAlClK,iBAAcoN,mBAAdpN;AACR,SAAOA;AACT;AAEA,SAAgBqN,qBAAkBA;AAChC,MAAAC,qBAAsBpD,gBAAe,GAA7BjK,YAASqN,mBAATrN;AACR,SAAOA;AACT;AAEA,SAAgBsN,iBAAcA;AAC5B,MAAAC,qBAAiCtD,gBAAe,GAAxC5L,SAAMkP,mBAANlP,QAAQC,QAAKiP,mBAALjP,OAAO2B,QAAKsN,mBAALtN;AACvB,SAAA1C,SAAA;IAASc,QAAQmP,aAAanP,MAAM;IAAGC,OAAOkP,aAAalP,KAAK;KAAM2B,KAAK;AAC7E;AAEA,SAAgBwN,yBAAsBA;AACpC,MAAAC,qBAAiCzD,gBAAe,GAAxC3K,uBAAoBoO,mBAApBpO;AACR,SAAOA;AACT;AAEA,SAAgBqO,wBAAqBA;AACnC,MAAAC,qBAAyB3D,gBAAe,GAAhC5K,eAAYuO,mBAAZvO;AACR,SAAOA;AACT;AAEA,SAAgBwO,0BAAuBA;AACrC,MAAAC,qBAA2B7D,gBAAe,GAAlC/J,iBAAc4N,mBAAd5N;AACR,SAAOA;AACT;AAEA,SAAgB6N,kCAA+BA;AAC7C,MAAAC,qBAAmC/D,gBAAe,GAA1C9J,yBAAsB6N,mBAAtB7N;AACR,SAAOA;AACT;AAEA,SAAgB8N,mBAAgBA;AAC9B,MAAAC,qBAA0BjE,gBAAe,GAAjCxB,gBAAayF,mBAAbzF;AACR,SAAOA;AACT;AAEA,SAAgB0F,qBAAkBA;AAChC,MAAAC,qBAAsBnE,gBAAe,GAA7Bf,YAASkF,mBAATlF;AACR,SAAOA;AACT;AAEA,SAAgBmF,uBAAoBA;AAIlC,MAAAC,qBAAwBrE,gBAAe,GAA/BpB,cAAWyF,mBAAXzF;AACR,SAAOA;AACT;AAEA,SAAS2E,aAAae,iBAAiC;AACrD,SAAO,OAAOA,oBAAoB,WAC3BA,kBAAe,OAClBA;AACN;AAEA,SAAgBC,uBAAuBC,oBAA0B;AAC/D,MAAMC,aAAaD,qBAAqB;AACxC,MAAME,WAAWF,qBAAqB;AAEtC,MAAIC,YAAY;AACd,WAAOC,WACH3G,sCAAsCjD,QACpC,MACA0J,mBAAmBG,SAAQ,CAAE,IAE/B7G;;AAGN,SAAOF;AACT;SCzLgBgH,kBACdC,cACAC,OAAAA;MAAAA,UAAAA,QAAAA;AAAAA,YAAgB;;AAEhB,MAAAC,gBAA0BC,uBAAYH,YAAY,GAA3CI,QAAKF,UAAA,CAAA,GAAEG,WAAQH,UAAA,CAAA;AACtB,MAAMI,YAAQ3E,qBAAsB,IAAI;AAExC,WAAS4E,kBAAkB5Q,OAAQ;AACjC,WAAO,IAAI6Q,QAAW,SAAAC,SAAO;;AAC3B,UAAIH,MAAMrE,SAAS;AACjByE,qBAAaJ,MAAMrE,OAAO;;AAG5BqE,YAAMrE,WAAO0E,UAAGC,WAAM,OAAA,SAAND,QAAQnL,WAAW,WAAA;AACjC6K,iBAAS1Q,KAAK;AACd8Q,gBAAQ9Q,KAAK;SACZsQ,KAAK;KACT;;AAGH,SAAO,CAACG,OAAOG,iBAAiB;AAClC;SCrBgBM,qBAAkBA;AAC9B,MAAMlH,gBAAgBwF,iBAAgB;AACtC,SAAO,SAAC7I,eAAoB;AAAA,WAAKqD,cAAcmH,IAAIxK,aAAY;;AACjE;SCQcyK,sBAAmBA;AACjC,MAAMC,0BAAsBrF,qBAAgC,CAAA,CAAE;AAC9D,MAAMsF,qBAAqBpC,sBAAqB;AAEhD,aAAOqC,sBAAQ,WAAA;AACb,QAAM3Q,eAAekG,WAAU,KAAIwK,kBAAoB;AAEvD,QAAI,CAACA,sBAAsBE,OAAOC,MAAM7Q,YAAY,GAAG;AACrD,aAAOyQ,oBAAoB/E;;AAG7B,WAAOxG,UAAUV,OAAO,SAACsM,kBAAkB1L,OAAK;AAC9C,UAAI2L,oBAAoB3L,OAAOpF,YAAY,GAAG;AAC5C8Q,yBAAiB/K,aAAaX,KAAK,CAAC,IAAI;;AAG1C,aAAO0L;OACNL,oBAAoB/E,OAAO;KAC7B,CAACgF,kBAAkB,CAAC;AACzB;AAEA,SAAgBM,uBAAoBA;AAClC,MAAMF,mBAAmBN,oBAAmB;AAC5C,MAAMS,kBAAkBX,mBAAkB;AAE1C,SAAO,SAASY,kBAAkB9L,OAAgB;AAChD,QAAMkB,UAAUD,uBAAuBN,aAAaX,KAAK,CAAC;AAE1D,WAAO+L,QAAQL,iBAAiBxK,OAAO,KAAK2K,gBAAgB3K,OAAO,CAAC;;AAExE;AAEA,SAASyK,oBACP3L,OACAgM,gBAAsB;AAEtB,SAAOnL,QAAQb,KAAK,IAAIgM;AAC1B;SC/CgBC,mBACdC,UAAuD;AAEvD7F,8BAAU,WAAA;AACR6F,aAAS,IAAI;KACZ,CAACA,QAAQ,CAAC;AACf;SCMgBC,sBAAqB7M,MAAA;MAAG0F,WAAQ1F,KAAR0F;AACtC,MAAM0G,mBAAmBN,oBAAmB;AAC5C,MAAMpQ,kBAAkB4L,yBAAwB;AAChD,MAAM/L,uBAAuBmO,uBAAsB;AAGnD,MAAMoD,gBAAYpT,qBAA0B4G,sBAAsB;AAClE,MAAMyM,uBAAmBrT,qBAAsB,KAAK;AACpD,MAAMsT,uBAAmBtT,qBAAsB,KAAK;AACpD,MAAMuT,0BAAsBvT,qBAC1B0S,gBAAgB;AAGlB,MAAMc,uBAAuBpC,kBAAkBqC,KAAKC,IAAG,GAAI,GAAG;AAC9D,MAAMC,aAAavC,kBAAkB,IAAI,GAAG;AAC5C,MAAMwC,2BAAuBpC,uBAAkB,KAAK;AACpD,MAAMqC,qBAAiBrC,uBAAoBxP,eAAe;AAC1D,MAAM8R,0BAAsBtC,uBAA8B,IAAI;AAC9D,MAAMuC,kCAA8BvC,uBAAsB,oBAAIhG,IAAG,CAAE;AACnE,MAAMwI,gCAA4BxC,uBAA2B,IAAI;AACjE,MAAMyC,yBAAqBzC,uBAAS3P,oBAAoB;AACxD,MAAA0P,gBAAkDC,uBAAS,KAAK,GAAzD0C,oBAAiB3C,UAAA,CAAA,GAAE4C,uBAAoB5C,UAAA,CAAA;AAE9C0B,qBAAmBkB,oBAAoB;AAEvC,aACEnU,4BAACoU,cAAc/H,UAAQ;IACrBrL,OAAO;MACL8S;MACAD;MACAR;MACAC;MACAC;MACAS;MACAD;MACAX;MACAc;MACAP;MACAC;MACAJ;MACAS;;KAGDjI,QAAQ;AAGf;AAIA,IAAMoI,oBAAgBpU,4BAcnB;EACD8T,qBAAqB,CAAC,MAAM,WAAA;EAAA,CAAQ;EACpCD,gBAAgB,CAAC7Q,UAAU4C,SAAS,WAAA;EAAA,CAAQ;EAC5CyN,kBAAkB;IAAE/F,SAAS;;EAC7BgG,kBAAkB;IAAEhG,SAAS;;EAC7BiG,qBAAqB;IAAEjG,SAAS,CAAA;;EAChC0G,2BAA2B,CAAC,MAAM,WAAA;EAAA,CAAQ;EAC1CD,6BAA6B,CAAC,oBAAIvI,IAAG,GAAI,WAAA;EAAA,CAAQ;EACjD4H,WAAW;IAAE9F,SAAS,CAAA;;EACtB4G,mBAAmB;EACnBP,YAAY,CAAC,IAAI,WAAA;AAAA,WAAM,IAAI9B,QAAgB,WAAA;AAAA,aAAMwC;;;EACjDT,sBAAsB,CAAC,OAAO,WAAA;EAAA,CAAQ;EACtCJ,sBAAsB,CAACC,KAAKC,IAAG,GAAI,WAAA;EAAA,CAAQ;EAC3CO,oBAAoB,CAAC,OAAO,WAAA;EAAA,CAAQ;CACrC;AAMD,SAAgBK,eAAYA;AAC1B,MAAAC,wBAAsBvU,yBAAiBoU,aAAa,GAA5ChB,YAASmB,kBAATnB;AACR,SAAOA;AACT;AAEA,SAAgBoB,sBAAmBA;AACjC,MAAAC,yBAA6BzU,yBAAiBoU,aAAa,GAAnDf,mBAAgBoB,mBAAhBpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,sBAAmBA;AACjC,MAAAC,yBAA6B3U,yBAAiBoU,aAAa,GAAnDd,mBAAgBqB,mBAAhBrB;AACR,SAAOA;AACT;AAEA,SAAgBsB,wBAAqBA;AACnC,MAAAC,yBAA+B7U,yBAAiBoU,aAAa,GAArDH,qBAAkBY,mBAAlBZ;AACR,SAAOA;AACT;AAEA,SAAgBa,qBAAkBA;AAChC,MAAAC,yBAAuB/U,yBAAiBoU,aAAa,GAA7CT,aAAUoB,mBAAVpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,yBAAsBA;AAIpC,MAAAC,yBAA2BjV,yBAAiBoU,aAAa,GAAjDP,iBAAcoB,mBAAdpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,iCAA8BA;AAC5C,MAAAC,yBAAwCnV,yBAAiBoU,aAAa,GAA9DL,8BAA2BoB,mBAA3BpB;AACR,SAAOA;AACT;AAEA,SAAgBqB,uBAAoBA;AAClC,MAAAC,yBAA8BrV,yBAAiBoU,aAAa,GAApDF,oBAAiBmB,mBAAjBnB;AACR,SAAOA;AACT;AAEA,SAAgBoB,+BAA4BA;AAC1C,MAAAC,yBAAsCvV,yBAAiBoU,aAAa,GAA5DJ,4BAAyBuB,mBAAzBvB;AACR,SAAOA;AACT;AAEA,SAAgBwB,0BAAuBA;AACrC,MAAAC,0BAAiCzV,yBAAiBoU,aAAa,GAAvDR,uBAAoB6B,oBAApB7B;AACR,SAAOA;AACT;AAEA,SAKgB8B,qBAAkBA;AAChC,MAAAC,0BAAiC3V,yBAAiBoU,aAAa,GAAvDZ,uBAAoBmC,oBAApBnC;AAER,MAAOoC,mBAAwCpC,qBAAoB,CAAA,GAA1CqC,qBAAsBrC,qBAAoB,CAAA;AACnE,SAAO,CACLoC,kBACA,SAASE,kBAAeA;AACtBD,uBAAmBpC,KAAKC,IAAG,CAAE;GAC9B;AAEL;SCrKwBqC,kBAAeA;AACrC,MAAAC,sBAAqBlB,mBAAkB,GAAhCnB,aAAUqC,oBAAA,CAAA;AAEjB,SAAO,CAAC,CAACrC;AACX;SCJgBsC,aAAaC,SAAwB;AACnD,MAAI,CAACA,SAAS;AACZ;;AAGFC,wBAAsB,WAAA;AACpBD,YAAQE,MAAK;GACd;AACH;AAEA,SAAgBC,wBAAwBH,SAAwB;AAC9D,MAAI,CAACA,QAAS;AAEd,MAAM9U,OAAO8U,QAAQI;AAErBL,eAAa7U,IAAI;AACnB;AAEA,SAAgBmV,wBAAwBL,SAAwB;AAC9D,MAAI,CAACA,QAAS;AAEd,MAAM7U,OAAO6U,QAAQM;AAErBP,eAAa5U,IAAI;AACnB;AAEA,SAAgBoV,uBAAuBP,SAAwB;AAC7D,MAAI,CAACA,QAAS;AAEd,MAAMQ,QAAQR,QAAQS;AAEtBV,eAAaS,KAAK;AACpB;SChCgBE,mBAAgBA;AAC9B,SAAOC,SAASC;AAClB;SCCgBC,0BAAyBzQ,MAAA;MACvC0F,WAAQ1F,KAAR0F;AAIA,MAAMgL,oBAAgBhX,qBAA0B,IAAI;AACpD,MAAMiX,uBAAmBjX,qBAA0B,IAAI;AACvD,MAAMkX,cAAUlX,qBAA6B,IAAI;AACjD,MAAMmX,qBAAiBnX,qBAA+B,IAAI;AAC1D,MAAMoX,wBAAoBpX,qBAA6B,IAAI;AAC3D,MAAMqX,4BAAwBrX,qBAA6B,IAAI;AAC/D,MAAMsX,yBAAqBtX,qBAA6B,IAAI;AAC5D,MAAMuX,mBAAevX,qBAA+B,IAAI;AAExD,aACEA,4BAACwX,kBAAkBnL,UAAQ;IACzBrL,OAAO;MACLiW;MACAC;MACAG;MACAL;MACAG;MACAC;MACAE;MACAC;;KAGDvL,QAAQ;AAGf;AAiBA,IAAMwL,wBAAoBxX,4BAAiC;EACzDiX,sBAAkBjX,wBAAe;EACjCkX,aAASlX,wBAAe;EACxBqX,2BAAuBrX,wBAAe;EACtCgX,mBAAehX,wBAAe;EAC9BmX,oBAAgBnX,wBAAe;EAC/BoX,uBAAmBpX,wBAAe;EAClCsX,wBAAoBtX,wBAAe;EACnCuX,kBAAcvX,wBAAe;CAC9B;AAED,SAASyX,gBAAaA;AACpB,aAAOzX,yBAAiBwX,iBAAiB;AAC3C;AAEA,SAAgBE,mBAAgBA;AAC9B,SAAOD,cAAa,EAAG,eAAe;AACxC;AAEA,SAAgBE,sBAAmBA;AACjC,SAAOF,cAAa,EAAG,kBAAkB;AAC3C;AAEA,SAAgBG,yBAAsBA;AACpC,MAAMX,mBAAmBU,oBAAmB;AAC5C,SAAO,SAACE,QAAuB;AAC7B,QAAIA,WAAW,QAAQZ,iBAAiB3J,YAAY,MAAM;AACxD2I,mBAAagB,iBAAiB3J,OAAO;;AAGvC2J,qBAAiB3J,UAAUuK;;AAE/B;AAEA,SAAgBC,aAAUA;AACxB,SAAOL,cAAa,EAAG,SAAS;AAClC;AAEA,SAAgBM,kBAAeA;AAC7B,SAAON,cAAa,EAAG,cAAc;AACvC;AAEA,SAAgBO,oBAAiBA;AAC/B,SAAOP,cAAa,EAAG,gBAAgB;AACzC;AAEA,SAAgBQ,uBAAoBA;AAClC,SAAOR,cAAa,EAAG,mBAAmB;AAC5C;AAEA,SAAgBS,2BAAwBA;AACtC,SAAOT,cAAa,EAAG,uBAAuB;AAChD;AAEA,SAAgBU,wBAAqBA;AACnC,SAAOV,cAAa,EAAG,oBAAoB;AAC7C;SC7FgBW,SAASC,MAAuBC,KAAAA;MAAAA,QAAAA,QAAAA;AAAAA,UAAc;;AAC5D,MAAMC,WAAWC,gBAAgBH,IAAI;AAErC,MAAI,CAACE,UAAU;AACb;;AAGFpC,wBAAsB,WAAA;AACpBoC,aAASE,YAAYH;GACtB;AACH;AAEA,SAAgBI,SAASL,MAAuBM,IAAU;AACxD,MAAMJ,WAAWC,gBAAgBH,IAAI;AAErC,MAAI,CAACE,UAAU;AACb;;AAGFpC,wBAAsB,WAAA;AACpBoC,aAASE,YAAYF,SAASE,YAAYE;GAC3C;AACH;AAEA,SAAgBC,cAAWA;AACzB,MAAM1B,UAAUY,WAAU;AAE1B,aAAOe,0BACL,SAACP,KAAW;AACVnC,0BAAsB,WAAA;AACpB,UAAIe,QAAQ5J,SAAS;AACnB4J,gBAAQ5J,QAAQmL,YAAYH;;KAE/B;KAEH,CAACpB,OAAO,CAAC;AAEb;AAEA,SAAgB4B,sBAAsB9R,OAAsB;AAC1D,MAAI,CAACA,SAAS,CAAC+R,mBAAmB/R,KAAK,GAAG;AACxC;;AAGF,MAAIA,MAAMgS,QAAQra,YAAYD,WAAWua,eAAe,CAAC,GAAG;AAC1D;;AAGF,MAAMC,aAAaC,kBAAkBnS,KAAK;AAC1C,MAAM2R,KAAKS,2BAA2BpS,KAAK;AAC3C0R,WAASQ,YAAY,EAAEG,oBAAoBC,gBAAgBtS,KAAK,CAAC,IAAI2R,GAAG;AAC1E;SC1CgBY,uBAAuBC,QAAuB;AAC5D,MAAMxS,QAAQyS,kBAAkBD,MAAM;AACtCvD,eAAajP,KAAK;AAClB8R,wBAAsB9R,KAAK;AAC7B;AAEA,SAAgB0S,+BAA+BF,QAAuB;AACpE,MAAMG,aAAaF,kBAAkBD,MAAM;AAE3CvD,eAAa0D,UAAU;AACvBA,gBAAU,OAAA,SAAVA,WAAYC,MAAK;AACnB;AAEA,SAAgBC,sBAAsBL,QAAuB;AAC3DvD,eAAa6D,iBAAiBN,MAAM,CAAC;AACvC;AAEA,SAAgBO,sBAAsB7D,SAAwB;AAC5D,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAM7U,OAAO2Y,iBAAiB9D,OAAO;AAErC,MAAI,CAAC7U,MAAM;AACT,WAAOkY,uBAAuBU,aAAa/D,OAAO,CAAC;;AAGrDD,eAAa5U,IAAI;AACjByX,wBAAsBzX,IAAI;AAC5B;AAEA,SAAgB6Y,sBAAsBhE,SAAwB;AAC5D,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAM9U,OAAO+Y,iBAAiBjE,OAAO;AAErC,MAAI,CAAC9U,MAAM;AACT,WAAOyY,sBAAsBO,aAAalE,OAAO,CAAC;;AAGpDD,eAAa7U,IAAI;AACjB0X,wBAAsB1X,IAAI;AAC5B;AAEA,SAAgBiZ,0BACdnE,SACAoE,QAAkB;AAElB,MAAI,CAACpE,SAAS;AACZ;;AAGF,MAAM9U,OAAOmZ,qBAAqBrE,OAAO;AAEzC,MAAI,CAAC9U,MAAM;AACT,WAAOkZ,OAAM;;AAGfrE,eAAa7U,IAAI;AACjB0X,wBAAsB1X,IAAI;AAC5B;AAEA,SAAgBoZ,4BAA4BtE,SAAwB;AAClE,MAAI,CAACA,SAAS;AACZ;;AAGF,MAAM7U,OAAOoZ,uBAAuBvE,OAAO;AAE3C,SAAOD,aAAa5U,IAAI;AAC1B;AAEA,SAASkZ,qBAAqBrE,SAAoB;AAChD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,MAAMwE,kBAAkBC,uBAAuBzE,OAAO;AACtD,MAAMlS,WAAWsV,gBAAgBoB,eAAe;AAChD,MAAME,aAAaC,kBAAkBH,iBAAiBxE,OAAO;AAC7D,MAAM4E,MAAMC,UAAUL,iBAAiBxE,OAAO;AAC9C,MAAM8E,aAAaC,kBAAkBP,iBAAiBxE,OAAO;AAE7D,MAAI4E,QAAQ,GAAG;AACb,QAAMI,sBAAsBd,aAAapW,QAAQ;AAEjD,QAAI,CAACkX,qBAAqB;AACxB,aAAO;;AAGT,WAAOC;MACLC,iBAAiBF,mBAAmB;MACpC;;MACAF;MACAJ;IAAU;;AAId,SAAOS,oBACLD,iBAAiBV,eAAe,GAChCI,KACAE,YACAJ,UAAU;AAEd;AAEA,SAASH,uBAAuBvE,SAAoB;AAClD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,MAAMwE,kBAAkBC,uBAAuBzE,OAAO;AACtD,MAAMlS,WAAWsV,gBAAgBoB,eAAe;AAChD,MAAME,aAAaC,kBAAkBH,iBAAiBxE,OAAO;AAC7D,MAAM4E,MAAMC,UAAUL,iBAAiBxE,OAAO;AAC9C,MAAM8E,aAAaC,kBAAkBP,iBAAiBxE,OAAO;AAC7D,MAAI,CAACoF,WAAWZ,iBAAiBxE,OAAO,GAAG;AACzC,QAAMqF,sBAAsBtB,aAAajW,QAAQ;AAEjD,QAAI,CAACuX,qBAAqB;AACxB,aAAO;;AAGT,WAAOJ,gBACLC,iBAAiBG,mBAAmB,GACpC,GACAP,YACAJ,UAAU;;AAId,MAAMY,gBAAgBC,oBACpBL,iBAAiBV,eAAe,GAChCI,KACAE,YACAJ,UAAU;AAGZ,SAAOY;AACT;SC/JgBE,yBAAsBA;AACpC,MAAAC,wBAA8CrG,6BAA4B,GAAnE2D,kBAAe0C,sBAAA,CAAA,GAAEC,qBAAkBD,sBAAA,CAAA;AAC1C,MAAAE,wBAA8CrG,wBAAuB,GAA9DsG,kBAAeD,sBAAA,CAAA,GAAEE,qBAAkBF,sBAAA,CAAA;AAE1C,MAAMG,0BAAsBnD,0BAAY,WAAA;AACtC,QAAII,iBAAiB;AACnB2C,yBAAmB,IAAI;;AAGzB,QAAIE,iBAAiB;AACnBC,yBAAmB,KAAK;;KAEzB,CACD9C,iBACA6C,iBACAF,oBACAG,kBAAkB,CACnB;AAED,SAAOC;AACT;AAEA,SAAgBC,oBAAiBA;AAC/B,MAAAC,yBAA0B5G,6BAA4B,GAA/C2D,kBAAeiD,uBAAA,CAAA;AACtB,MAAAC,yBAA0B3G,wBAAuB,GAA1CsG,kBAAeK,uBAAA,CAAA;AAEtB,SAAO,SAASC,iBAAcA;AAC5B,WAAO,CAAC,CAACnD,mBAAmB6C;;AAEhC;SC/BgBO,uBAAoBA;AAClC,MAAMC,mBAAmB5H,oBAAmB;AAC5C,SAAO,SAAS6H,oBAAiBA;AAC/BD,qBAAiBhP,UAAU;;AAE/B;AAEA,SAAgBkP,oBAAiBA;AAC/B,MAAMF,mBAAmB5H,oBAAmB;AAC5C,SAAO,SAAS+H,iBAAcA;AAC5BH,qBAAiBhP,UAAU;;AAE/B;AAEA,SAAgBoP,uBAAoBA;AAClC,MAAMJ,mBAAmB5H,oBAAmB;AAC5C,SAAO,SAASiI,oBAAiBA;AAC/B,WAAOL,iBAAiBhP;;AAE5B;AAEA,SAAgBsP,iBAAcA;AAC5B,MAAM1F,UAAUY,WAAU;AAC1B,MAAM2E,iBAAiBD,kBAAiB;AACxC,MAAMG,oBAAoBD,qBAAoB;AAE9CrP,8BAAU,WAAA;AACR,QAAMwP,UAAU3F,QAAQ5J;AACxBuP,eAAO,OAAA,SAAPA,QAASC,iBAAiB,aAAaC,aAAa;MAClDC,SAAS;KACV;AAED,aAASD,cAAWA;AAClB,UAAIJ,kBAAiB,GAAI;AACvBF,uBAAc;;;AAGlB,WAAO,WAAA;AACLI,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,aAAaF,WAAW;;KAEtD,CAAC7F,SAASuF,gBAAgBE,iBAAiB,CAAC;AACjD;SCrCgBO,sBAAmBA;AACjC,MAAM/F,iBAAiBa,kBAAiB;AAExC,aAAOa,0BAAY,WAAA;AACjB5C,iBAAakB,eAAe7J,OAAO;KAClC,CAAC6J,cAAc,CAAC;AACrB;AAEA,SAAgBgG,yBAAsBA;AACpC,MAAM/F,oBAAoBa,qBAAoB;AAE9C,aAAOY,0BAAY,WAAA;AACjB,QAAI,CAACzB,kBAAkB9J,SAAS;AAC9B;;AAGFmJ,2BAAuBW,kBAAkB9J,OAAO;KAC/C,CAAC8J,iBAAiB,CAAC;AACxB;AAEA,SAAgBgG,6BAA0BA;AACxC,MAAM/F,wBAAwBa,yBAAwB;AAEtD,aAAOW,0BAAY,WAAA;AACjB,QAAI,CAACxB,sBAAsB/J,SAAS;AAClC;;AAGFmJ,2BAAuBY,sBAAsB/J,OAAO;KACnD,CAAC+J,qBAAqB,CAAC;AAC5B;ACvBA,SAASgG,kBAAeA;AACtB,MAAMjK,YAAYkB,aAAY;AAE9B,SAAO,SAASgJ,UACdC,QAA6D;AAE7D,QAAI,OAAOA,WAAW,YAAY;AAChC,aAAOD,UAAUC,OAAOnK,UAAU9F,OAAO,CAAC;;AAG5C8F,cAAU9F,UAAUiQ;;AAExB;AAEA,SAAgBC,iBAAcA;AAC5B,MAAMC,cAAcC,eAAc;AAClC,MAAMvG,iBAAiBa,kBAAiB;AACxC,MAAM2F,mBAAmBT,oBAAmB;AAE5C,SAAO,SAASU,cAAWA;AACzB,QAAIzG,eAAe7J,SAAS;AAC1B6J,qBAAe7J,QAAQtM,QAAQ;;AAGjCyc,gBAAY,EAAE;AACdE,qBAAgB;;AAEpB;AAEA,SAAgBE,kBAAeA;AAC7B,MAAM1G,iBAAiBa,kBAAiB;AACxC,MAAMyF,cAAcC,eAAc;AAElC,SAAO,SAASI,aAAaC,KAAW;AACtC,QAAI5G,eAAe7J,SAAS;AAC1B6J,qBAAe7J,QAAQtM,QAAK,KAAMmW,eAAe7J,QAAQtM,QAAQ+c;AACjEN,kBAAYO,wBAAwB7G,eAAe7J,QAAQtM,KAAK,CAAC;WAC5D;AACLyc,kBAAYO,wBAAwBD,GAAG,CAAC;;;AAG9C;AAEA,SAAgBE,YAASA;AACvB,MAAM9G,iBAAiBa,kBAAiB;AACxC,MAAM5E,YAAYkB,aAAY;AAC9B,MAAM4J,eAAeb,gBAAe;AACpC,MAAMI,cAAcC,eAAc;AAElC,MAAA1H,sBAAqBlB,mBAAkB,GAAhCnB,aAAUqC,oBAAA,CAAA;AACjB,MAAMmI,sBAAsBC,uBAC1BhL,UAAU9F,SACVqG,UAAU;AAGZ,SAAO;IACL0K;IACA1K;IACAwD;IACAgH;;AAGF,WAASE,SAASC,YAAkB;AAClC,QAAMC,SAASnL,UAAU9F;AAEzB,QAAMkR,YAAYF,WAAWjX,YAAW;AAExC,QAAIkX,UAAM,QAANA,OAASC,SAAS,KAAKA,UAAU7b,UAAU,GAAG;AAChD,aAAO8a,YAAYe,SAAS;;AAG9B,QAAMC,eAAeC,iBAAiBF,WAAWD,MAAM;AAEvD,QAAI,CAACE,cAAc;AAGjB,aAAOhB,YAAYe,SAAS;;AAG9BN,iBAAa,SAAA5Q,SAAO;AAAA,UAAA5G;AAAA,aAClB1B,OAAOC,OAAOqI,UAAO5G,iBAAA,CAAA,GAAAA,eAClB8X,SAAS,IAAGG,2BAA2BF,cAAcD,SAAS,GAAC9X,eAAA;;AAGpE+W,gBAAYe,SAAS;;AAEzB;AAEA,SAASd,iBAAcA;AACrB,MAAAkB,uBAA0B9J,mBAAkB,GAAnC+J,gBAAaD,qBAAA,CAAA;AACtB,MAAM5H,gBAAgBU,iBAAgB;AAEtC,SAAO,SAAS+F,YAAY9J,YAAkB;AAC5CwC,0BAAsB,WAAA;AACpB0I,oBAAclL,aAAaA,cAAU,OAAA,SAAVA,WAAYtM,YAAW,IAAKsM,UAAU,EAAEmL,KACjE,WAAA;AACE1G,iBAASpB,cAAc1J,SAAS,CAAC;OAClC;KAEJ;;AAEL;AAEA,SAASqR,2BACPhW,SACAoW,SAAe;AAEf,MAAMC,WAAuB,CAAA;AAE7B,WAAW9W,WAAWS,SAAQ;AAC5B,QAAM3B,QAAQ2B,QAAOT,OAAO;AAE5B,QAAI+W,SAASjY,OAAO+X,OAAO,GAAG;AAC5BC,eAAS9W,OAAO,IAAIlB;;;AAIxB,SAAOgY;AACT;AAEA,SAASC,SAASjY,OAAkB+X,SAAe;AACjD,SAAO5X,WAAWH,KAAK,EAAEkY,KAAK,SAAAnb,MAAI;AAAA,WAAIA,KAAKmF,SAAS6V,OAAO;;AAC7D;AAEA,SAAgBI,qBAAkBA;AAChC,MAAAC,gBAA4B9K,aAAY,GAAvBiK,SAAMa,cAAf9R;AACR,MAAA+R,uBAAqBvK,mBAAkB,GAAhCnB,aAAU0L,qBAAA,CAAA;AAEjB,SAAO,SAAAnX,SAAO;AAAA,WAAIoX,4BAA4BpX,SAASqW,QAAQ5K,UAAU;;AAC3E;AAEA,SAAS2L,4BACPpX,SACAqW,QACA5K,YAAkB;;AAElB,MAAI,CAAC4K,UAAU,CAAC5K,YAAY;AAC1B,WAAO;;AAGT,SAAO,GAAA4L,qBAAChB,OAAO5K,UAAU,MAAC,QAAlB4L,mBAAqBrX,OAAO;AACtC;AAIA,SAASwW,iBACPK,SACAS,MAAuC;AAEvC,MAAI,CAACA,MAAM;AACT,WAAO;;AAGT,MAAIA,KAAKT,OAAO,GAAG;AACjB,WAAOS,KAAKT,OAAO;;AAGrB,MAAMU,qBAAqBza,OAAO0a,KAAKF,IAAI,EACxCG,KAAK,SAACC,GAAGC,GAAC;AAAA,WAAKA,EAAEld,SAASid,EAAEjd;KAC5BqG,KAAK,SAAAjI,KAAG;AAAA,WAAIge,QAAQ7V,SAASnI,GAAG;;AAEnC,MAAI0e,oBAAoB;AACtB,WAAOD,KAAKC,kBAAkB;;AAGhC,SAAO;AACT;AAEA,SAAgBzB,wBAAwBD,KAAW;AACjD,MAAI,CAACA,OAAO,OAAOA,QAAQ,UAAU;AACnC,WAAO;;AAGT,SAAOA,IAAI+B,KAAI,EAAGzY,YAAW;AAC/B;AAEA,SAAS+W,uBACP2B,aACApM,YAAkB;;AAElB,MAAI,EAACoM,eAAW,QAAXA,YAAcpM,UAAU,GAAG,QAAO;AAEvC,MAAM3C,uBACJgP,kBAAAhb,OAAOmB,QAAQ4Z,eAAW,OAAA,SAAXA,YAAcpM,UAAU,CAAC,MAAC,OAAA,SAAzCqM,gBAA2Crd,WAAU;AAEvD,SAAOoO,uBAAuBC,kBAAkB;AAClD;SCtMwBiP,wBAAqBA;AAC3C,MAAMC,sBAAsBtI,uBAAsB;AAClD,MAAA+D,wBAAoCrG,6BAA4B,GAAvD6K,0BAAuBxE,sBAAA,CAAA;AAEhC,SAAO,SAASC,mBAAmB1F,SAAwB;AACzD,QAAAkK,oBAAgBC,iBAAiBnK,OAAO,GAAjClP,QAAKoZ,kBAAA,CAAA;AAEZ,QAAIpZ,OAAO;AACTkZ,0BAAoBhK,OAAO;AAC3BiK,8BAAwBnZ,KAAK;;;AAGnC;SCLgBsZ,wBAAqBA;AACnC,MAAMC,+BAA+BjQ,gCAA+B;AAEpE,SAAOiQ,iCAAiCrd,uBAAuBoI;AACjE;AAEA,SAAgBkV,yBAAsBA;AACpC,MAAMD,+BAA+BjQ,gCAA+B;AAEpE,SAAOiQ,iCAAiCrd,uBAAuBiI;AACjE;ACyBA,IAAKsV;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAAA,WAAAA,IAAAA;AACAA,EAAAA,gBAAAA,SAAAA,IAAAA;AACAA,EAAAA,gBAAAA,WAAAA,IAAAA;AACAA,EAAAA,gBAAAA,YAAAA,IAAAA;AACAA,EAAAA,gBAAAA,QAAAA,IAAAA;AACAA,EAAAA,gBAAAA,OAAAA,IAAAA;AACAA,EAAAA,gBAAAA,OAAAA,IAAAA;AACF,GARKA,mBAAAA,iBAAc,CAAA,EAAA;AAUnB,SAAgBC,wBAAqBA;AACnCC,8BAA2B;AAC3BC,+BAA4B;AAC5BC,kCAA+B;AAC/BC,sCAAmC;AACnCC,wBAAqB;AACvB;AAEA,SAASJ,8BAA2BA;AAClC,MAAM3J,gBAAgBU,iBAAgB;AACtC,MAAMkG,cAAcJ,eAAc;AAClC,MAAMpF,YAAWQ,YAAW;AAC5B,MAAMzB,iBAAiBa,kBAAiB;AACxC,MAAM2F,mBAAmBT,oBAAmB;AAC5C,MAAMd,iBAAiBH,kBAAiB;AACxC,MAAMM,oBAAoBF,qBAAoB;AAE9C,MAAML,sBAAsBN,uBAAsB;AAElD,MAAMsF,gBAAYzO,sBAChB,WAAA;AAAA,WACE,SAASyO,WAAUC,OAAoB;AACrC,UAAQlgB,MAAQkgB,MAARlgB;AAERwb,wBAAiB;AACjB,cAAQxb,KAAG;QAET,KAAK0f,eAAeS;AAClBD,gBAAME,eAAc;AACpB,cAAI/E,eAAc,GAAI;AACpBJ,gCAAmB;AACnB;;AAEF4B,sBAAW;AACXxF,UAAAA,UAAS,CAAC;AACVuF,2BAAgB;AAChB;;;KAGR,CACEvF,WACAwF,aACA5B,qBACA2B,kBACAvB,gBACAG,iBAAiB,CAClB;AAGHlP,8BAAU,WAAA;AACR,QAAMC,UAAU0J,cAAc1J;AAE9B,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQwP,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACL1T,cAAQ2P,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAChK,eAAeG,gBAAgBiB,WAAU4I,SAAS,CAAC;AACzD;AAEA,SAASJ,+BAA4BA;AACnC,MAAMQ,sBAAsBjE,uBAAsB;AAClD,MAAMnG,gBAAgBU,iBAAgB;AACtC,MAAMR,UAAUY,WAAU;AAC1B,MAAMX,iBAAiBa,kBAAiB;AACxC,MAAA6D,wBAAoCrG,wBAAuB,GAAlD6L,0BAAuBxF,sBAAA,CAAA;AAChC,MAAMyF,wBAAwBC,yBAAwB;AACtD,MAAMC,qBAAqBlB,sBAAqB;AAEhD,MAAMU,gBAAYzO,sBAChB,WAAA;AAAA,WACE,SAASyO,WAAUC,OAAoB;AACrC,UAAQlgB,MAAQkgB,MAARlgB;AAER,cAAQA,KAAG;QACT,KAAK0f,eAAegB;AAClB,cAAI,CAACD,oBAAoB;AACvB;;AAEFP,gBAAME,eAAc;AACpBE,kCAAwB,IAAI;AAC5BD,8BAAmB;AACnB;QACF,KAAKX,eAAeiB;AAClBT,gBAAME,eAAc;AACpBG,gCAAqB;AACrB;QACF,KAAKb,eAAekB;AAClBV,gBAAME,eAAc;AACpBzH,yCAA+BxC,QAAQ5J,OAAO;AAC9C;;;KAGR,CACE8T,qBACAE,uBACAD,yBACAnK,SACAsK,kBAAkB,CACnB;AAGHnU,8BAAU,WAAA;AACR,QAAMC,UAAU6J,eAAe7J;AAE/B,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQwP,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACL1T,cAAQ2P,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAChK,eAAeG,gBAAgB6J,SAAS,CAAC;AAC/C;AAEA,SAASH,kCAA+BA;AACtC,MAAMzJ,oBAAoBa,qBAAoB;AAC9C,MAAM0F,mBAAmBT,oBAAmB;AAC5C,MAAM/F,iBAAiBa,kBAAiB;AACxC,MAAMsJ,wBAAwBC,yBAAwB;AACtD,MAAApF,yBAA4B3G,wBAAuB,GAA5CoM,SAAMzF,uBAAA,CAAA,GAAE0F,YAAS1F,uBAAA,CAAA;AACxB,MAAM2F,sBAAsBtB,uBAAsB;AAClD,MAAMgB,qBAAqBlB,sBAAqB;AAChD,MAAMyB,SAASC,UAAS;AAExB,MAAMhB,gBAAYzO,sBAChB,WAAA;AAAA;;MAEE,SAASyO,WAAUC,OAAoB;AACrC,YAAQlgB,MAAQkgB,MAARlgB;AAER,YAAIygB,oBAAoB;AACtB,kBAAQzgB,KAAG;YACT,KAAK0f,eAAewB;AAClBhB,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBuE,gCAAkBvE,gBAAgB;AAClC;YACF,KAAK8C,eAAegB;AAClBR,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBwE,gCAAiB;AACjB;YACF,KAAK1B,eAAeiB;AAClBT,oBAAME,eAAc;AACpB,kBAAIS,QAAQ;AACVC,0BAAU,KAAK;;AAEjBP,oCAAqB;AACrB;YACF;AACES,qBAAOd,KAAK;AACZ;;;AAIN,YAAIa,qBAAqB;AACvB,kBAAQ/gB,KAAG;YACT,KAAK0f,eAAe2B;AAClBnB,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBuE,gCAAkBvE,gBAAgB;AAClC;YACF,KAAK8C,eAAeiB;AAClBT,oBAAME,eAAc;AACpB,kBAAI,CAACS,QAAQ;AACX,uBAAOjE,iBAAgB;;AAEzBwE,gCAAiB;AACjB;YACF;AACEJ,qBAAOd,KAAK;AACZ;;;;;KAIV,CACEW,QACAjE,kBACAkE,WACAP,uBACAS,QACAD,qBACAN,kBAAkB,CACnB;AAGHnU,8BAAU,WAAA;AACR,QAAMC,UAAU8J,kBAAkB9J;AAElC,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQwP,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACL1T,cAAQ2P,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAC5J,mBAAmBD,gBAAgByK,QAAQZ,SAAS,CAAC;AAC3D;AAEA,SAASF,sCAAmCA;AAC1C,MAAMnD,mBAAmBT,oBAAmB;AAC5C,MAAM7F,wBAAwBa,yBAAwB;AACtD,MAAMhB,UAAUY,WAAU;AAC1B,MAAMiK,SAASC,UAAS;AAExB,MAAMhB,gBAAYzO,sBAChB,WAAA;AAAA,WACE,SAASyO,WAAUC,OAAoB;AACrC,UAAQlgB,MAAQkgB,MAARlgB;AAER,cAAQA,KAAG;QACT,KAAK0f,eAAe2B;AAClBnB,gBAAME,eAAc;AACpBxD,2BAAgB;AAChB;QACF,KAAK8C,eAAegB;AAClBR,gBAAME,eAAc;AACpB5K,kCAAwBK,iBAAgB,CAAE;AAC1C;QACF,KAAK6J,eAAewB;AAClBhB,gBAAME,eAAc;AACpB9K,kCAAwBO,iBAAgB,CAAE;AAC1C;QACF,KAAK6J,eAAeiB;AAClBT,gBAAME,eAAc;AACpB5H,iCAAuBrC,QAAQ5J,OAAO;AACtC;QACF;AACEyU,iBAAOd,KAAK;AACZ;;;KAGR,CAAC/J,SAASyG,kBAAkBoE,MAAM,CAAC;AAGrC1U,8BAAU,WAAA;AACR,QAAMC,UAAU+J,sBAAsB/J;AAEtC,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQwP,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACL1T,cAAQ2P,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAC3J,uBAAuBH,SAAS8J,SAAS,CAAC;AAChD;AAEA,SAASD,wBAAqBA;AAC5B,MAAM7J,UAAUY,WAAU;AAC1B,MAAMuK,eAAeC,gBAAe;AACpC,MAAM1G,qBAAqBqE,sBAAqB;AAChD,MAAM7D,iBAAiBH,kBAAiB;AACxC,MAAMD,sBAAsBN,uBAAsB;AAElD,MAAMqG,SAASC,UAAS;AAExB,MAAMhB,gBAAYzO,sBAChB,WAAA;AAAA;;MAEE,SAASyO,WAAUC,OAAoB;AACrC,YAAQlgB,MAAQkgB,MAARlgB;AAER,YAAM+V,gBAAgByL,iBAAiB3L,iBAAgB,CAAE;AAEzD,gBAAQ7V,KAAG;UACT,KAAK0f,eAAegB;AAClBR,kBAAME,eAAc;AACpBpH,kCAAsBjD,aAAa;AACnC;UACF,KAAK2J,eAAewB;AAClBhB,kBAAME,eAAc;AACpBjH,kCAAsBpD,aAAa;AACnC;UACF,KAAK2J,eAAeiB;AAClBT,kBAAME,eAAc;AACpB,gBAAI/E,eAAc,GAAI;AACpBJ,kCAAmB;AACnB;;AAEFxB,wCAA4B1D,aAAa;AACzC;UACF,KAAK2J,eAAe2B;AAClBnB,kBAAME,eAAc;AACpB,gBAAI/E,eAAc,GAAI;AACpBJ,kCAAmB;AACnB;;AAEF3B,sCAA0BvD,eAAeuL,YAAY;AACrD;UACF,KAAK5B,eAAe+B;AAClBvB,kBAAME,eAAc;AACpBvF,+BAAmBqF,MAAMpJ,MAAqB;AAC9C;UACF;AACEkK,mBAAOd,KAAK;AACZ;;;;KAGR,CACEoB,cACAN,QACAnG,oBACAQ,gBACAJ,mBAAmB,CACpB;AAGH3O,8BAAU,WAAA;AACR,QAAMC,UAAU4J,QAAQ5J;AAExB,QAAI,CAACA,SAAS;AACZ;;AAGFA,YAAQwP,iBAAiB,WAAWkE,SAAS;AAE7C,WAAO,WAAA;AACL1T,cAAQ2P,oBAAoB,WAAW+D,SAAS;;KAEjD,CAAC9J,SAAS8J,SAAS,CAAC;AACzB;AAEA,SAASO,2BAAwBA;AAC/B,MAAMkB,0BAA0BrF,2BAA0B;AAC1D,MAAMsF,eAAe3M,gBAAe;AACpC,MAAMmB,UAAUY,WAAU;AAE1B,aAAOe,0BACL,SAASyI,wBAAqBA;AAC5B,QAAIoB,cAAc;AAChB,aAAOnJ,uBAAuBrC,QAAQ5J,OAAO;;AAE/C,WAAOmV,wBAAuB;KAEhC,CAACvL,SAASuL,yBAAyBC,YAAY,CAAC;AAEpD;AAEA,SAASJ,kBAAeA;AACtB,MAAM3E,mBAAmBT,oBAAmB;AAC5C,MAAMuF,0BAA0BrF,2BAA0B;AAC1D,MAAMsF,eAAe3M,gBAAe;AAEpC,aAAO8C,0BACL,SAAS8J,gBAAaA;AACpB,QAAID,cAAc;AAChB,aAAO/E,iBAAgB;;AAEzB,WAAO8E,wBAAuB;KAEhC,CAAC9E,kBAAkB+E,cAAcD,uBAAuB,CAAC;AAE7D;AAEA,SAASP,kBAAkBU,UAAoB;AAC7C,MAAMC,kBAAkBjM,iBAAgB;AAExC,MAAI,CAACiM,iBAAiB;AACpB;;AAGF,MAAI,CAACC,sBAAsBD,eAAe,GAAG;AAC3CD,aAAQ;;AAGVrM,0BAAwBsM,eAAe;AACzC;AAEA,SAASV,oBAAiBA;AACxB,MAAMU,kBAAkBjM,iBAAgB;AAExC,MAAI,CAACiM,iBAAiB;AACpB;;AAGFxM,0BAAwBwM,eAAe;AACzC;AAEA,SAASb,YAASA;AAChB,MAAMlE,eAAeD,gBAAe;AACpC,MAAMF,mBAAmBT,oBAAmB;AAC5C,MAAMza,iBAAiB2N,wBAAuB;AAC9C,MAAM4L,sBAAsBN,uBAAsB;AAElD,SAAO,SAASqG,OAAOd,OAAoB;AACzC,QAAQlgB,MAAQkgB,MAARlgB;AAER,QAAIgiB,YAAY9B,KAAK,KAAKxe,gBAAgB;AACxC;;AAGF,QAAI1B,IAAIiiB,MAAM,oBAAoB,GAAG;AACnC/B,YAAME,eAAc;AACpBnF,0BAAmB;AACnB2B,uBAAgB;AAChBG,mBAAa/c,GAAG;;;AAGtB;AAEA,SAASgiB,YAAY9B,OAAoB;AACvC,MAAQgC,UAA6BhC,MAA7BgC,SAASC,UAAoBjC,MAApBiC,SAASC,SAAWlC,MAAXkC;AAE1B,SAAOF,WAAWC,WAAWC;AAC/B;SCzdgBC,aACdhY,aACApE,OACA7E,YAAsB;AAEtB,MAAI,CAAC6E,OAAO;AACV;;AAGF,MAAI7E,eAAeW,WAAWugB,QAAQ;AACpC;;AAGF,MAAMnb,UAAUP,aAAaX,KAAK;AAElC,MAAIsc,eAAenR,IAAIjK,OAAO,GAAG;AAC/B;;AAGFW,kBAAgB7B,KAAK,EAAEQ,QAAQ,SAACyB,WAAS;AACvC,QAAMsa,WAAWnY,YAAYnC,WAAW9G,UAAU;AAClDqhB,iBAAaD,QAAQ;GACtB;AAEDD,iBAAerY,IAAI/C,OAAO;AAC5B;AAEO,IAAMob,iBAA8B,oBAAI9X,IAAG;AAElD,SAASgY,aAAaC,KAAW;AAC/B,MAAMC,QAAQ,IAAIC,MAAK;AACvBD,QAAME,MAAMH;AACd;SC3BgBI,aAAUA;AACxB,MAAM3M,UAAUY,WAAU;AAC1B,MAAM3V,aAAa+L,oBAAmB;AACtC,MAAM9C,cAAcwF,qBAAoB;AAExCvD,8BAAU,WAAA;AACR,QAAIlL,eAAeW,WAAWugB,QAAQ;AACpC;;AAGF,QAAMxG,UAAU3F,QAAQ5J;AAExBuP,eAAO,OAAA,SAAPA,QAASC,iBAAiB,WAAWgH,OAAO;AAE5C,WAAO,WAAA;AACLjH,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,WAAW6G,OAAO;;AAGjD,aAASA,QAAQ7C,OAAiB;AAChC,UAAM8C,SAASxB,iBAAiBtB,MAAMpJ,MAAqB;AAE3D,UAAI,CAACkM,QAAQ;AACX;;AAGF,UAAA3D,oBAAgBC,iBAAiB0D,MAAM,GAAhC/c,QAAKoZ,kBAAA,CAAA;AAEZ,UAAI,CAACpZ,OAAO;AACV;;AAGF,UAAIsB,mBAAmBtB,KAAK,GAAG;AAC7Boc,qBAAahY,aAAapE,OAAO7E,UAAU;;;KAG9C,CAAC+U,SAAS/U,YAAYiJ,WAAW,CAAC;AACvC;;ACtBO,IAAM4Y,uBAAuB;AAEpC,SAAwBC,WAAU3d,MAAA;MAAG0F,WAAQ1F,KAAR0F;AACnC,aACEhM,4BAACmT,uBAAqB,UACpBnT,4BAACkkB,mBAAiB,MAAElY,QAAQ,CAAqB;AAGvD;AAQA,SAASkY,kBAAiBC,OAAA;;MAAGnY,WAAQmY,MAARnY;AAC3B,MAAAoY,wBAAwBxP,sBAAqB,GAAtCyP,gBAAaD,sBAAA,CAAA;AACpB,MAAMhiB,QAAQiN,eAAc;AAC5B,MAAMiV,mBAAmBvO,gBAAe;AACxC,MAAMiB,gBAAgBU,iBAAgB;AACtC,MAAMnV,YAAYoN,mBAAkB;AACpC,MAAMnN,QAAQqN,eAAc;AAE5B6Q,wBAAqB;AACrBmD,aAAU;AAEV,MAAAU,QAAyC/hB,SAAS,CAAA,GAA1C3B,QAAK0jB,MAAL1jB,OAAOD,SAAM2jB,MAAN3jB,QAAW4jB,aAAUvY,8BAAAsY,OAAArY,WAAA;AAEpC,aACElM,4BAAAA,SAAAA;IACEuC,WAAWkiB,GACTC,OAAOC,MACPD,OAAOE,eACPxiB,UAAUW,MAAMkD,QAAQye,OAAOG,WAC/BziB,UAAUW,MAAM+hB,QAAQJ,OAAOK,gBAAaC,MAAA,CAAA,GAAAA,IAEzCtmB,WAAWumB,YAAY,IAAGX,kBAAgBU,MAE7CX,iBAAiBK,OAAOQ,eACxB3iB,SAAS;IAEX4iB,KAAKnO;IACLxU,OAAK1C,SAAA,CAAA,GACA0kB,YACC,CAACH,iBAAiB;MAAEzjB;MAAQC;KAAO;KAGxCmL,QAAQ;AAGf;AAEA,IAAMoZ,YAAY;EAChB,yCACE;EACF,wCACE;EACF,yBAAyB;EACzB,oBAAoB;EACpB,wBAAwB;EACxB,wBAAwB;EACxB,+BAA+B;EAC/B,iCAAiC;EACjC,6BAA6B;EAC7B,kBAAkB;EAClB,4BAA4B;EAC5B,sCACE;EACF,yCACE;EACF,oCACE;EACF,qCACE;;AAGJ,IAAMV,SAAStlB,WAAWS,OAAO;EAC/B8kB,MAAM;IACJ,KAAK,CAAC,YAAYjmB,WAAW2mB,WAAW;IACxCC,UAAU;IACV/lB,SAAS;IACTgmB,eAAe;IACfC,aAAa;IACbC,aAAa;IACbC,cAAc;IACdC,aAAa;IACbC,iBAAiB;IACjBjmB,UAAU;IACVgB,YAAY;IACZ,KAAK;MACHklB,WAAW;MACXC,YAAY;;;EAGhBlB,eAAe;IACb,MAAM;MACJ,yBAAyB;MACzB,wBAAwB;MACxB,wCAAwC;MACxC,wBAAwB;MACxB,oBAAoB;MACpB,+BAA+B;MAC/B,6BAA6B;MAC7B,kBAAkB;MAClB,4BAA4B;MAC5B,oCAAoC;MACpC,qCAAqC;MAErC,4BAA4B;MAE5B,8BAA8B;;MAG9B,6BAA6B;MAC7B,wBAAwB;;MAGxB,iDACE;MACF,iCAAiC;;MAGjC,sCAAsC;MACtC,8BAA8B;MAC9B,oCAAoC;MACpC,6BAA6B;MAC7B,iCAAiC;MACjC,wCAAwC;MACxC,kCAAkC;;MAGlC,yCAAyC;;MAGzC,uCAAuC;MACvC,yCAAyC;;MAGzC,wBAAwB;MACxB,2BAA2B;MAC3B,8BAA8B;MAC9B,8BAA8B;MAC9B,4BAA4B;;MAG5B,0BAA0B;;MAG1B,iCAAiC;MACjC,mCAAmC;MACnC,gCAAgC;MAChC,+BAAkCZ,uBAAoB;;MAGtD,oBAAoB;MACpB,uBAAuB;MACvB,wBACE;MACF,2BAA2B;MAC3B,yCAAyC;MACzC,+CAA+C;;MAG/C,gCAAgC;MAChC,6CAA6C;MAC7C,gCAAgC;MAChC,uCAAuC;MACvC,yBAAyB;;MAGzB,cAAc;MACd,8CAA8C;MAC9C,8BAA8B;MAC9B,yBAAyB;MACzB,6BAA6B;MAC7B,6CAA6C;MAC7C,6BAA6B;MAC7B,oCAAoC;MACpC,sCAAsC;MACtC,kCAAkC;MAClC,uBAAuB;MACvB,iCAAiC;MACjC,2CAA2C;MAC3C,8CAA8C;MAC9C,yCAAyC;MACzC,0CAA0C;;;EAG9Ce,eAAe;IACb,KAAKrmB,WAAWqnB;IAChB,uCAAuC;MACrC,MAAMX;;;EAGVP,WAAW;IACT,KAAKnmB,WAAWmmB;IAChB,MAAMO;;EAERF,eAAe;IACb,KAAK;IACLtkB,QAAQ;IACRrB,SAAS;IACTqmB,iBAAiB;;IAEjBI,gBAAgB;IAChB,MAAM;MACJ,8BAA8B;;;CAGnC;SCrOe/K,kBACdzB,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAM+P,cAAczM,OAAO0M,sBAAqB,EAAGrlB;AACnD,MAAMslB,eAAejQ,QAAQgQ,sBAAqB,EAAGrlB;AACrD,SAAOulB,KAAKC,MAAMJ,cAAcE,YAAY;AAC9C;AAEA,SAAgBtL,kBACdrB,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAMiQ,eAAejQ,QAAQgQ,sBAAqB,EAAGrlB;AACrD,MAAMylB,cAAcpQ,QAAQgQ,sBAAqB,EAAGK;AACpD,MAAMC,aAAahN,OAAO0M,sBAAqB,EAAGK;AAElD,SAAOH,KAAKC,OAAOC,cAAcE,cAAcL,YAAY;AAC7D;AAEA,SAAgBpL,UACdvB,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAMuQ,iBAAgBvQ,QAAQgQ,sBAAqB,EAAGtlB;AACtD,MAAM8lB,aAAaxQ,QAAQgQ,sBAAqB,EAAG5N;AACnD,MAAMqO,YAAYnN,OAAO0M,sBAAqB,EAAG5N;AACjD,SAAO8N,KAAKQ,OAAOF,aAAaC,aAAaF,cAAa;AAC5D;AAEA,SAAgBnL,WACd9B,QACAtD,SAAwB;AAExB,MAAI,CAACsD,UAAU,CAACtD,SAAS;AACvB,WAAO;;AAGT,MAAMuQ,iBAAgBvQ,QAAQgQ,sBAAqB,EAAGtlB;AACtD,MAAM8lB,aAAaxQ,QAAQgQ,sBAAqB,EAAG5N;AACnD,MAAMqO,YAAYnN,OAAO0M,sBAAqB,EAAG5N;AACjD,MAAMuO,eAAerN,OAAO0M,sBAAqB,EAAGtlB;AAEpD,SAAOwlB,KAAKQ,MAAMF,aAAaC,YAAYF,cAAa,IAAII;AAC9D;AAEA,SAASC,eACPC,UACAjM,KACAkM,eAAqB;AAErB,MAAIlM,QAAQ,IAAI;AACd,QAAMmM,UAAUb,KAAKC,OAAOU,SAASpkB,SAAS,KAAKqkB,aAAa;AAChE,QAAME,oBAAoBD,UAAUD;AACpC,QAAMG,mBAAmBJ,SAASpkB,SAAS;AAC3C,WAAOokB,SAASK,MAAMF,mBAAmBC,mBAAmB,CAAC;;AAG/D,SAAOJ,SAASK,MAAMtM,MAAMkM,gBAAgBlM,MAAM,KAAKkM,aAAa;AACtE;AAEA,SAASK,mBACPC,aACAC,YACAP,eAAqB;AAErB,MAAMQ,UAAUD,aAAa;AAE7B,MAAIC,UAAUR,gBAAgBM,YAAY3kB,QAAQ;AAChD,WAAO,CAAA;;AAGT,SAAOmkB,eAAeQ,aAAaE,SAASR,aAAa;AAC3D;AAEA,SAAgB7L,gBACd4L,UACAjM,KACAkM,eACApM,YAAkB;AAElB,MAAM6M,cAAcX,eAAeC,UAAUjM,KAAKkM,aAAa;AAE/D,SAAOS,YAAY7M,UAAU,KAAK6M,YAAYA,YAAY9kB,SAAS,CAAC,KAAK;AAC3E;AAEA,SAAgB8Y,oBACd6L,aACAC,YACAP,eACAU,OAAa;AAEb,MAAMC,kBAAkBN,mBACtBC,aACAC,YACAP,aAAa;AAIf,SACEW,gBAAgBD,KAAK,KACrBC,gBAAgBA,gBAAgBhlB,SAAS,CAAC,KAC1C;AAEJ;AAEA,SAAgB0Y,oBACdiM,aACAC,YACAP,eACAU,OAAa;AAEb,MAAME,kBAAkBd,eACtBQ,aACAC,aAAa,GACbP,aAAa;AAIf,SACEY,gBAAgBF,KAAK,KACrBE,gBAAgBA,gBAAgBjlB,SAAS,CAAC,KAC1C;AAEJ;AAEA,SAAgBklB,+BACdrO,QACAuN,UACAe,4BAA0B;MAA1BA,+BAA0B,QAAA;AAA1BA,iCAA6B;;AAE7B,MAAI,CAACtO,UAAU,CAACuN,SAASpkB,QAAQ;AAC/B,WAAO;;AAGT,MAAMgkB,YAAYnN,OAAO0M,sBAAqB,EAAG5N;AACjD,MAAMyP,eAAevO,OAAO0M,sBAAqB,EAAG8B;AACpD,MAAMC,qBAAqBtB,YAAYuB,eAAe1O,MAAM;AAE5D,MAAM2O,kBAAkBpB,SAAS/d,KAAK,SAAAkN,SAAO;AAC3C,QAAMwQ,aAAaxQ,QAAQgQ,sBAAqB,EAAG5N;AACnD,QAAM8P,gBAAgBlS,QAAQgQ,sBAAqB,EAAG8B;AACtD,QAAMK,0BACJnS,QAAQoS,eAAeR;AAEzB,QAAMS,4BAA4B7B,aAAa2B;AAC/C,QAAMG,+BACJJ,gBAAgBC;AAElB,QAAIE,4BAA4BN,oBAAoB;AAClD,aAAO;;AAGT,WACGM,6BAA6B5B,aAC5B4B,6BAA6BR,gBAC9BS,gCAAgC7B,aAC/B6B,gCAAgCT;GAErC;AAED,SAAOI,mBAAmB;AAC5B;AAEA,SAAgBrF,sBAAsB5M,SAAoB;AACxD,SAAO,CAAC,CAACA,QAAQM;AACnB;AAEA,SAAS0R,eAAeO,YAAuB;AAC7C,MAAMC,SAAS7pB,MAAM8pB,KACnBF,WAAWG,iBAAiBjqB,YAAYD,WAAWmqB,KAAK,CAAC,CAAC;AAG5D,WAAAC,KAAA,GAAAC,UAAoBL,QAAMI,KAAAC,QAAApmB,QAAAmmB,MAAE;AAAvB,QAAMD,QAAKE,QAAAD,EAAA;AACd,QAAMloB,SAASioB,MAAM3C,sBAAqB,EAAGtlB;AAE7C,QAAIA,SAAS,GAAG;AACd,aAAOA;;;AAIX,SAAOojB;AACT;AC5LO,IAAMgF,sBAAmB,WAAYrqB,YAAYD,WAAWsI,KAAK;AACjE,IAAMiiB,uBAAuB,CAClCD,qBACArqB,YAAYD,WAAWwqB,OAAO,GAAC,UACvBvqB,YAAYD,WAAWY,MAAM,IAAC,GAAA,EACtCH,KAAK,EAAE;AAET,SAAgBojB,iBACd4G,cAA6B;;AAE7B,UAAAC,wBAAOD,gBAAY,OAAA,SAAZA,aAAcnQ,QAAQgQ,mBAAmB,MAAC,OAAAI,wBAAI;AACvD;AAEA,SAQgB/I,iBACdnK,SAAwB;AAExB,MAAMmT,kBAAkBC,gCAAgCpT,OAAO;AAC/D,MAAMhO,UAAUqhB,wBAAwBrT,OAAO;AAE/C,MAAI,CAACmT,iBAAiB;AACpB,WAAO,CAAA;;AAGT,MAAMriB,QAAQmC,eAAejB,WAAO,OAAPA,UAAWmhB,eAAe;AAEvD,MAAI,CAACriB,OAAO;AACV,WAAO,CAAA;;AAGT,SAAO,CAACA,OAAOkB,OAAiB;AAClC;AAEA,SAAgBshB,eAAetT,SAAwB;;AACrD,SAAOnD,SACLmD,WAAO,OAAA,SAAPA,QAASuT,QAAQT,mBAAmB,OAClC9S,WAAO,OAAA,UAAAwT,wBAAPxT,QAASyT,kBAAa,OAAA,SAAtBD,sBAAwBD,QAAQT,mBAAmB,EAAC;AAE1D;AAEA,SAagBvC,cAAcvQ,SAAwB;;AACpD,UAAA0T,wBAAO1T,WAAO,OAAA,SAAPA,QAASoS,iBAAY,OAAAsB,wBAAI;AAClC;AAEA,SAAgBC,mBAAmB3T,SAAwB;AACzD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,MAAM6N,SAASxB,iBAAiBrM,OAAO;AACvC,MAAMlS,WAAWsV,gBAAgByK,MAAM;AAGvC,MAAM+F,cAAczQ,oBAAoBrV,QAAQ;AAEhD,SAAO+lB,iBAAiBhG,MAAM,IAAIgG,iBAAiB/lB,QAAQ,IAAI8lB;AACjE;AAEA,SAAgBzQ,oBAAoBrV,UAAyB;;AAC3D,MAAI,CAACA,UAAU;AACb,WAAO;;AAGT,MAAMgmB,uBAAuBhmB,SAASimB,cACpCtrB,YAAYD,WAAWgc,eAAe,CAAC;AAGzC,WACEwP,wBAAClmB,YAAQ,OAAA,SAARA,SAAUskB,iBAAY,OAAA4B,wBAAI,OAACC,wBAAKH,wBAAoB,OAAA,SAApBA,qBAAsB1B,iBAAY,OAAA6B,wBAAI;AAE3E;AAEA,SAAgBpR,mBAAmB/R,OAAsB;AACvD,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,SACEoS,2BAA2BpS,KAAK,IAChCqS,oBAAoBC,gBAAgBtS,KAAK,CAAC;AAE9C;AAEA,SAAgBwR,gBAAgBH,MAAqB;AACnD,MAAI,CAACA,KAAM,QAAO;AAElB,SAAOA,KAAKoR,QAAQ9qB,YAAYD,WAAWwa,UAAU,CAAC,IAClDb,OACAA,KAAK4R,cAActrB,YAAYD,WAAWwa,UAAU,CAAC;AAC3D;AAEA,SAAgBE,2BAA2BpS,OAAsB;;AAC/D,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,SAAO6iB,mBAAmB7iB,KAAK,MAACojB,yBAAAC,qBAAIlR,kBAAkBnS,KAAK,MAAC,OAAA,SAAxBqjB,mBAA0B5R,cAAS,OAAA2R,wBAAI;AAC7E;AAEA,SAAgBjR,kBAAkBjD,SAAwB;;AACxD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAGT,UAAAoU,mBAAOpU,QAAQ8C,QAAQra,YAAYD,WAAWwa,UAAU,CAAC,MAAC,OAAAoR,mBAAI;AAChE;AAEA,SAAgBC,mBAAmBrU,SAAwB;AACzD,MAAM6N,SAASxB,iBAAiBrM,OAAO;AACvC,MAAMlS,WAAWsV,gBAAgByK,MAAM;AAEvC,SAAOyG,kBAAkBzG,MAAM,IAAIyG,kBAAkBxmB,QAAQ;AAC/D;AAEA,SAAS+lB,iBAAiB7T,SAAwB;;AAChD,UAAAuU,qBAAOvU,WAAO,OAAA,SAAPA,QAASwU,cAAS,OAAAD,qBAAI;AAC/B;AAEA,SAASD,kBAAkBtU,SAAwB;;AACjD,UAAAyU,sBAAOzU,WAAO,OAAA,SAAPA,QAAS0U,eAAU,OAAAD,sBAAI;AAChC;AAEA,SAAgBpB,wBAAwBviB,OAAsB;;AAC5D,UAAA6jB,qBAAOC,kBAAkBvI,iBAAiBvb,KAAK,GAAG,SAAS,MAAC,OAAA6jB,qBAAI;AAClE;AAEA,SAAgBvB,gCACdtiB,OAAsB;AAEtB,MAAMkB,UAAUqhB,wBAAwBviB,KAAK;AAE7C,MAAIkB,SAAS;AACX,WAAOD,uBAAuBC,OAAO;;AAEvC,SAAO;AACT;AAEA,SAAgB6iB,2BACd/jB,OAAsB;AAEtB,MAAI,CAACA,OAAO;AACV,WAAO;MACLkB,SAAS;MACTmhB,iBAAiB;;;AAIrB,SAAO;IACLnhB,SAASqhB,wBAAwBviB,KAAK;IACtCqiB,iBAAiBC,gCAAgCtiB,KAAK;;AAE1D;AAEA,SAAS8jB,kBACP5U,SACAnV,KAAW;;AAEX,UAAAiqB,sBAAOC,eAAe/U,OAAO,EAAEnV,GAAG,MAAC,OAAAiqB,sBAAI;AACzC;AAEA,SAASC,eAAe/U,SAAwB;;AAC9C,UAAAgV,mBAAOhV,WAAO,OAAA,SAAPA,QAASiV,YAAO,OAAAD,mBAAI,CAAA;AAC7B;AAEA,SAAgBE,eAAelV,SAAoB;AACjD,SAAOA,QAAQmV,UAAUC,SAAS5sB,WAAWwqB,OAAO;AACtD;AAEA,SAAgBqC,SAASrV,SAAwB;AAC/C,MAAI,CAACA,QAAS,QAAO;AAErB,SAAOA,QAAQmV,UAAUC,SAAS5sB,WAAWY,MAAM;AACrD;AAEA,SAAgB8b,iBAAiB5B,QAAuB;AACtD,MAAI,CAACA,QAAQ;AACX,WAAO,CAAA;;AAGT,SAAO3a,MAAM8pB,KACXnP,OAAOoP,iBAAiBK,oBAAoB,CAAC;AAEjD;AAEA,SAAgBnP,iBAAiB5D,SAAwB;AACvD,MAAI,CAACA,QAAS,QAAO;AAErB,MAAMpP,aAAYsU,iBAAiBlF,OAAO;AAC1C,MAAAsV,mBAAe1kB,WAAUsgB,MAAM,EAAE,GAA1BqE,OAAID,iBAAA,CAAA;AACX,MAAI,CAACC,MAAM;AACT,WAAO;;AAGT,MAAI,CAACL,eAAeK,IAAI,GAAG;AACzB,WAAOtR,iBAAiBsR,IAAI;;AAG9B,SAAOA;AACT;AAEA,SAAgBzR,iBAAiB9D,SAAoB;AACnD,MAAM7U,OAAO6U,QAAQM;AAErB,MAAI,CAACnV,MAAM;AACT,WAAOoY,kBAAkBQ,aAAa/D,OAAO,CAAC;;AAGhD,MAAI,CAACkV,eAAe/pB,IAAI,GAAG;AACzB,WAAO2Y,iBAAiB3Y,IAAI;;AAG9B,SAAOA;AACT;AAEA,SAAgB8Y,iBAAiBjE,SAAoB;AACnD,MAAM9U,OAAO8U,QAAQI;AAErB,MAAI,CAAClV,MAAM;AACT,WAAO0Y,iBAAiBM,aAAalE,OAAO,CAAC;;AAG/C,MAAI,CAACkV,eAAehqB,IAAI,GAAG;AACzB,WAAO+Y,iBAAiB/Y,IAAI;;AAG9B,SAAOA;AACT;AAEA,SAAgBqY,kBAAkBD,QAAuB;AACvD,MAAI,CAACA,QAAQ;AACX,WAAO;;AAGT,MAAM1S,aAAYsU,iBAAiB5B,MAAM;AAEzC,SAAOqO,+BAA+BrO,QAAQ1S,YAAW,GAAG;AAC9D;AAEA,SAAgBsT,aAAalE,SAAwB;AACnD,MAAMlS,WAAWsV,gBAAgBpD,OAAO;AAExC,MAAI,CAAClS,UAAU;AACb,WAAO;;AAGT,MAAM5C,OAAO4C,SAASsS;AAEtB,MAAI,CAAClV,MAAM;AACT,WAAO;;AAGT,MAAImqB,SAASnqB,IAAI,GAAG;AAClB,WAAOgZ,aAAahZ,IAAI;;AAG1B,SAAOA;AACT;AAEA,SAAgB6Y,aAAa/D,SAAwB;AACnD,MAAMlS,WAAWsV,gBAAgBpD,OAAO;AAExC,MAAI,CAAClS,UAAU;AACb,WAAO;;AAGT,MAAM3C,OAAO2C,SAASwS;AAEtB,MAAI,CAACnV,MAAM;AACT,WAAO;;AAGT,MAAIkqB,SAASlqB,IAAI,GAAG;AAClB,WAAO4Y,aAAa5Y,IAAI;;AAG1B,SAAOA;AACT;AAEA,SAAgBiY,gBAAgBpD,SAAwB;AACtD,MAAI,CAACA,SAAS;AACZ,WAAO;;AAET,SAAOA,QAAQ8C,QAAQra,YAAYD,WAAWsF,QAAQ,CAAC;AACzD;AAEA,SAAgB2W,uBAAuBzE,SAAwB;AAC7D,MAAI,CAACA,SAAS;AACZ,WAAO;;AAET,SAAOA,QAAQ8C,QACbra,YAAYD,WAAWgc,eAAe,CAAC;AAE3C;SCnUgBgR,iBAAiBxjB,SAAe;AAC9C,SAAOA,QACJX,MAAM,GAAG,EACTtI,IAAI,SAAA0sB,KAAG;AAAA,WAAIC,OAAOC,cAAcC,SAASH,KAAK,EAAE,CAAC;KACjDxsB,KAAK,EAAE;AACZ;ACAA,IAAM4sB,mBAAmB;AAUzB,SAAgBC,aAAaC,MAAqB;AAChD,MAAI;AAAA,QAAAja,SAAAka,uBAAAC;AACF,QAAI,GAAAna,UAACC,WAAM,QAAND,QAAQoa,eAAc;AACzB,aAAO,CAAA;;AAET,QAAMC,SAASC,KAAKC,OAAKL,yBAAAC,WACvBla,WAAM,OAAA,SAANka,SAAQC,aAAaI,QAAQT,gBAAgB,MAAC,OAAAG,wBAAI,IAAI;AAGxD,QAAID,SAASppB,eAAe0I,UAAU;AACpC,aAAO8gB,OAAO1M,KAAK,SAACC,GAAGC,GAAC;AAAA,eAAKA,EAAE4M,QAAQ7M,EAAE6M;;;AAG3C,WAAOJ;WACPK,SAAM;AACN,WAAO,CAAA;;AAEX;AAEA,SAAgBC,aAAa3lB,OAAkBP,UAAmB;AAChE,MAAM4lB,SAASL,aAAY;AAE3B,MAAM9jB,UAAUP,aAAaX,OAAOP,QAAQ;AAC5C,MAAM4iB,kBAAkB1hB,aAAaX,KAAK;AAE1C,MAAI4lB,WAAWP,OAAOrjB,KAAK,SAAA1C,MAAA;AAAA,QAAYumB,IAACvmB,KAAV4B;AAAO,WAAU2kB,MAAM3kB;;AAErD,MAAI4kB;AAEJ,MAAIF,UAAU;AACZE,eAAW,CAACF,QAAQ,EAAEG,OAAOV,OAAO9N,OAAO,SAAAyO,GAAC;AAAA,aAAIA,MAAMJ;MAAS;SAC1D;AACLA,eAAW;MACT1kB;MACA+kB,UAAU5D;MACVoD,OAAO;;AAETK,eAAQ,CAAIF,QAAQ,EAAAG,OAAKV,MAAM;;AAGjCO,WAASH;AAETK,WAASnqB,SAASyjB,KAAK8G,IAAIJ,SAASnqB,QAAQ,EAAE;AAE9C,MAAI;AAAA,QAAAwqB;AACF,KAAAA,WAAAlb,WAAM,OAAA,SAANkb,SAAQf,aAAagB,QAAQrB,kBAAkBO,KAAKe,UAAUP,QAAQ,CAAC;WAEvEQ,UAAM;;AAGV;SCzDgBC,iBACdvpB,UAA+C;AAE/C,SAAOA,SAASA,aAAaf,WAAWI;AAC1C;AAEA,SAAgBmqB,cAAcxmB,OAAyB;AACrD,SAAOA,MAAM6C,WAAWwK;AAC1B;SCoBgBoZ,qBACdC,cACA7e,kBAAoC;AAEpC,MAAM8e,wBAAoB3gB,qBAAM;AAChC,MAAM4O,qBAAqBqE,sBAAqB;AAChD,MAAM5M,mBAAmBmB,oBAAmB;AAC5C,MAAAmH,wBAAoCrG,6BAA4B,GAAvD6K,0BAAuBxE,sBAAA,CAAA;AAChC,MAAMK,sBAAsBN,uBAAsB;AAClD,MAAAkS,wBAAyB5Y,uBAAsB,GAAxCnB,iBAAc+Z,sBAAA,CAAA;AACrB,MAAM3gB,eAAe2B,sBAAsBC,gBAAgB;AAC3D,MAAAgf,sBAA4BnY,mBAAkB,GAArCI,kBAAe+X,oBAAA,CAAA;AACxB,MAAMziB,cAAcwF,qBAAoB;AACxC,MAAMkd,mBAAmB5f,oBAAmB;AAE5C,MAAM6f,cAAU/tB,0BACd,SAAS+tB,SAAQ9M,OAAiB;AAChC,QAAI5N,iBAAiB/F,SAAS;AAC5B;;AAGF0O,wBAAmB;AAEnB,QAAAgS,kBAAyBC,eAAehN,KAAK,GAAtCja,QAAKgnB,gBAAA,CAAA,GAAE9lB,UAAO8lB,gBAAA,CAAA;AAErB,QAAI,CAAChnB,SAAS,CAACkB,SAAS;AACtB;;AAGF,QAAMgmB,gBACJnkB,2BAA2B7B,OAAO,KAAK2L;AAEzCiC,oBAAe;AACf6W,iBAAa3lB,OAAOknB,aAAa;AACjCjhB,iBACEkhB,iBAAiBnnB,OAAOknB,eAAeJ,kBAAkB1iB,WAAW,GACpE6V,KAAK;KAGT,CACEpN,gBACAmI,qBACA3I,kBACApG,cACA6I,iBACA1K,aACA0iB,gBAAgB,CACjB;AAGH,MAAMM,kBAAcpuB,0BAClB,SAASouB,aAAYnN,OAAiB;;AACpC,QAAI0M,kBAAkBrgB,SAAS;AAC7ByE,mBAAa4b,kBAAkBrgB,OAAO;;AAGxC,QAAA+gB,mBAAgBJ,eAAehN,KAAK,GAA7Bja,QAAKqnB,iBAAA,CAAA;AAEZ,QAAI,CAACrnB,SAAS,CAACsB,mBAAmBtB,KAAK,GAAG;AACxC;;AAGF2mB,sBAAkBrgB,WAAO0E,UAAGC,WAAM,OAAA,SAAND,QAAQnL,WAAW,WAAA;AAC7CwM,uBAAiB/F,UAAU;AAC3BqgB,wBAAkBrgB,UAAU+G;AAC5B2H,0BAAmB;AACnBJ,yBAAmBqF,MAAMpJ,MAAqB;AAC9CsI,8BAAwBnZ,KAAK;OAC5B,GAAG;KAER,CACEqM,kBACA2I,qBACAJ,oBACAuE,uBAAuB,CACxB;AAEH,MAAMmO,gBAAYtuB,0BAChB,SAASsuB,aAASA;AAChB,QAAIX,kBAAkBrgB,SAAS;AAC7ByE,mBAAa4b,kBAAkBrgB,OAAO;AACtCqgB,wBAAkBrgB,UAAU+G;eACnBhB,iBAAiB/F,SAAS;AAOnC6I,4BAAsB,WAAA;AACpB9C,yBAAiB/F,UAAU;OAC5B;;KAGL,CAAC+F,gBAAgB,CAAC;AAGpBhG,8BAAU,WAAA;AACR,QAAI,CAACqgB,aAAapgB,SAAS;AACzB;;AAEF,QAAMihB,eAAeb,aAAapgB;AAClCihB,iBAAazR,iBAAiB,SAASiR,SAAS;MAC9C/Q,SAAS;KACV;AAEDuR,iBAAazR,iBAAiB,aAAasR,aAAa;MACtDpR,SAAS;KACV;AACDuR,iBAAazR,iBAAiB,WAAWwR,WAAW;MAClDtR,SAAS;KACV;AAED,WAAO,WAAA;AACLuR,sBAAY,OAAA,SAAZA,aAActR,oBAAoB,SAAS8Q,OAAO;AAClDQ,sBAAY,OAAA,SAAZA,aAActR,oBAAoB,aAAamR,WAAW;AAC1DG,sBAAY,OAAA,SAAZA,aAActR,oBAAoB,WAAWqR,SAAS;;KAEvD,CAACZ,cAAcK,SAASK,aAAaE,SAAS,CAAC;AACpD;AAEA,SAASL,eAAehN,OAAiB;AACvC,MAAMpJ,SAASoJ,SAAK,OAAA,SAALA,MAAOpJ;AACtB,MAAI,CAAC2R,eAAe3R,MAAM,GAAG;AAC3B,WAAO,CAAA;;AAGT,SAAOwI,iBAAiBxI,MAAM;AAChC;AAEA,SAASsW,iBACPnnB,OACA6M,gBACAia,kBACA1iB,aAAwB;AAExB,MAAMzB,QAAQxC,WAAWH,KAAK;AAE9B,MAAIwmB,cAAcxmB,KAAK,GAAG;AACxB,QAAMkB,WAAUP,aAAaX,KAAK;AAClC,WAAO;MACL6M;MACA7M,OAAOkB;MACPsmB,aAAW,SAAAA,cAAAA;AACT,eAAOxnB,MAAM6C;;MAEf4kB,UAAUznB,MAAM6C;MAChB6kB,UAAU;MACV/kB;MACAzB,SAAAA;MACAD,wBAAwBC;;;AAG5B,MAAMA,UAAUP,aAAaX,OAAO6M,cAAc;AAElD,SAAO;IACLA;IACA7M,OAAO0kB,iBAAiBxjB,OAAO;IAC/BsmB,aAAW,SAAAA,YAACrsB,YAAAA;UAAAA,eAAAA,QAAAA;AAAAA,qBAAyB2rB,oBAAgB,OAAhBA,mBAAoBhrB,WAAW4C;;AAClE,aAAO0F,YAAYlD,SAAS/F,UAAU;;IAExCssB,UAAUrjB,YAAYlD,SAAS4lB,oBAAgB,OAAhBA,mBAAoBhrB,WAAW4C,KAAK;IACnEgpB,UAAU;IACV/kB;IACAzB;IACAD,wBAAwBN,aAAaX,KAAK;;AAE9C;SC9LgB2nB,OAAOC,OAAY;AACjC,aACE5uB,4BAAAA,UAAAA,OAAAA,OAAAA;IACE6uB,MAAK;KACDD,OAAK;IACTrsB,WAAWkiB,GAAGC,SAAOX,QAAQ6K,MAAMrsB,SAAS;MAE3CqsB,MAAM5iB,QAAQ;AAGrB;AAEA,IAAM0Y,WAAStlB,WAAWS,OAAO;EAC/BkkB,QAAQ;IACN,KAAK;IACL+K,QAAQ;IACRC,QAAQ;IACRC,YAAY;IACZC,SAAS;;CAEZ;SCVeC,qBAAoB5oB,MAAA;;MAClCa,cAAUb,KAAVa,YACAe,UAAO5B,KAAP4B,SACA5I,UAAMgH,KAANhH,QACAkB,iBAAc8F,KAAd9F,gBAAc2uB,sBAAA7oB,KACd8oB,gBAAAA,iBAAcD,wBAAA,SAAG,OAAIA,qBACrBE,gBAAa/oB,KAAb+oB,eACArjB,WAAQ1F,KAAR0F,UACAzJ,YAAS+D,KAAT/D,WAAS+sB,oBAAAhpB,KACTipB,cAAAA,eAAYD,sBAAA,SAAG,QAAKA;AAEpB,aACEtvB,4BAAC2uB,QAAM;IACLpsB,WAAWkiB,GACTC,SAAO1d,OACP1H,WAAUM,aAAaN,QACvBkB,kBAAkBH,wBAAwBG,iBAAcwkB,MAAA,CAAA,GAAAA,IAErDtmB,WAAWwqB,OAAO,IAAG,CAAC5pB,WAAU,CAACkB,gBAAcwkB,MAElD,CAAC,EAAEqK,iBAAiBD,mBAAmB1K,SAAO2K,eAC9CE,gBAAgB7K,SAAO6K,cACvBhtB,SAAS;oBAEG2F;kBACFsnB,aAAaroB,WAAU;sBACnBA;KAEf6E,QAAQ;AAGf;AAEA,SAASwjB,aAAaroB,aAAoB;;AACxC,SAAOA,YAAW,CAAC,EAAE6b,MAAM,OAAO,KAACyM,eAC/BtoB,YAAW,CAAC,MAAC,OAAAsoB,eAAItoB,YAAW,CAAC,IAC7BA,YAAW,CAAC;AAClB;AAEA,IAAMud,WAAStlB,WAAWS,OAAO;EAC/BmH,OAAO;IACL,KAAKtI,WAAWsI;IAChBse,UAAU;IACVzkB,OAAO;IACPD,QAAQ;IACRilB,WAAW;IACXtmB,SAAS;IACTmwB,YAAY;IACZC,gBAAgB;IAChBC,UAAU;IACVC,WAAW;IACXnK,cAAc;IACd/lB,UAAU;IACVgB,YAAY;IACZ,UAAU;MACRilB,iBAAiB;;IAEnB,UAAU;MACRA,iBAAiB;;;EAGrB2J,cAAc;IACZP,YAAY;IACZ,UAAU;MACRpJ,iBAAiB;MACjBoJ,YAAY;;IAEd,UAAU;MACRpJ,iBAAiB;MACjBoJ,YAAY;;;EAGhBK,eAAe;IACb,KAAK3wB,WAAW4J;IAChB,UAAU;MACRwnB,SAAS;MACTvwB,SAAS;MACTsB,OAAO;MACPD,QAAQ;MACRmvB,OAAO;MACP/H,QAAQ;MACR1C,UAAU;MACV0K,YAAY;MACZC,aAAa;MACbC,WAAW;MACXC,cAAc;MACdC,QAAQ;;IAEV,gBAAgB;MACdD,cAAc;;;CAGnB;AChHM,IAAME,cAAcjxB,WAAWS,OAAO;EAC3CywB,UAAU;IACR,KAAK5xB,WAAW4xB;IAChBC,UAAU;;EAEZC,QAAQ;IACNC,WAAW;IACXC,aAAa;IACbnxB,SAAS;;CAEZ;SCLeoxB,SAAQrqB,MAAA;MACtB0B,aAAS1B,KAAT0B,WACAxF,QAAK8D,KAAL9D,OAAKouB,gBAAAtqB,KACLuqB,UAAAA,WAAQD,kBAAA,SAAG,QAAKA,eAChB/mB,SAAMvD,KAANuD,QACAinB,UAAOxqB,KAAPwqB,SACAvuB,YAAS+D,KAAT/D;AAUA,aACEvC,4BAAAA,OAAAA;IACE4jB,KAAK/Z;IACLknB,KAAK/oB;IACLzF,WAAWkiB,GAAGC,SAAOsM,WAAWX,YAAYC,UAAUD,YAAYG,QAAQjuB,SAAS;IACnF0uB,SAASJ,WAAW,SAAS;IAC7BC;IACAtuB;;AAGN;AAEA,IAAMkiB,WAAStlB,WAAWS,OAAO;EAC/BmxB,WAAW;IACT,KAAK;IACLpB,UAAU;IACVC,WAAW;IACXqB,UAAU;IACVC,WAAW;IACXC,SAAS;;CAEZ;SCrCeC,YAAW/qB,MAAA;MACzB4B,UAAO5B,KAAP4B,SACA1F,QAAK8D,KAAL9D,OACAD,YAAS+D,KAAT/D;AAMA,aACEvC,4BAAAA,QAAAA;IACEuC,WAAWkiB,GACTC,SAAO4M,aACPjB,YAAYG,QACZH,YAAYC,UACZ/tB,SAAS;oBAEG2F;IACd1F;KAECkpB,iBAAiBxjB,OAAO,CAAC;AAGhC;AAEA,IAAMwc,WAAStlB,WAAWS,OAAO;EAC/ByxB,aAAa;IACX,KAAK;IACLxL,YACE;IACFR,UAAU;IACViM,YAAY;IACZhB,UAAU;IACViB,WAAW;IACXf,WAAW;IACXC,aAAa;IACbe,eAAe;IACfL,SAAS;;CAEZ;SChCeM,cAAaprB,MAAA;MAC3BU,QAAKV,KAALU,OACAkB,UAAO5B,KAAP4B,SACA/F,aAAUmE,KAAVnE,YACAwvB,OAAIrrB,KAAJqrB,MACAd,WAAQvqB,KAARuqB,UAAQe,mBAAAtrB,KACR8E,aAAAA,cAAWwmB,qBAAA,SAAGhpB,oBAAiBgpB,kBAC/BrvB,YAAS+D,KAAT/D;AAEA,MAAAsvB,wBAAsC3c,+BAA8B,GAA3D4c,4BAAyBD,sBAAA,CAAA;AAElC,MAAMrvB,QAAQ,CAAA;AACd,MAAImvB,MAAM;AACRnvB,UAAM3B,QAAQ2B,MAAM5B,SAAS4B,MAAM+tB,WAAcoB,OAAI;;AAGvD,MAAMI,gBAAgB/qB,QAAQA,QAAQmC,eAAejB,OAAO;AAE5D,MAAI,CAAC6pB,eAAe;AAClB,WAAO;;AAGT,MAAIvE,cAAcuE,aAAa,GAAG;AAChC,eACE/xB,4BAAC2wB,UAAQ;MACPnuB;MACAwF,WAAWE;MACX/F,YAAYW,WAAWugB;MACvBwN;MACAhnB,QAAQkoB,cAAcloB;MACtBinB;MACAvuB;;;AAKN,aACEvC,4BAAAA,uBAAAA,MACGmC,eAAeW,WAAWugB,aACzBrjB,4BAACqxB,aAAW;IAACnpB;IAAkB1F;IAAcD;WAE7CvC,4BAAC2wB,UAAQ;IACPnuB;IACAwF,WAAWA,UAAU+pB,aAAa;IAClC5vB;IACA0uB;IACAhnB,QAAQuB,YAAYlD,SAAS/F,UAAU;IACvC2uB;IACAvuB;IAEH;AAIL,WAASuuB,UAAOA;AACdgB,8BAA0B,SAAA1wB,MAAI;AAAA,aAAI,IAAIoK,IAAIpK,IAAI,EAAE6J,IAAI/C,OAAO;;;AAE/D;SCpDgB8pB,eAAc1rB,MAAA;MAC5BU,QAAKV,KAALU,OACAkB,UAAO5B,KAAP4B,SACA5I,UAAMgH,KAANhH,QACAkB,iBAAc8F,KAAd9F,gBACA2B,aAAUmE,KAAVnE,YAAUgtB,sBAAA7oB,KACV8oB,gBAAAA,iBAAcD,wBAAA,SAAG,OAAIA,qBACrBwC,OAAIrrB,KAAJqrB,MACAd,WAAQvqB,KAARuqB,UACAzlB,cAAW9E,KAAX8E,aACA7I,YAAS+D,KAAT/D,WAAS+sB,oBAAAhpB,KACTipB,cAAAA,eAAYD,sBAAA,SAAG,QAAKA;AAEpB,MAAMD,gBAAgB/mB,mBAAmBtB,KAAK;AAE9C,aACEhH,4BAACkvB,sBAAoB;IACnBG;IACAD;IACA9vB,QAAQA;IACRkB;IACA2G,YAAYA,WAAWH,KAAK;IAC5BkB;IACAqnB;SAEAvvB,4BAAC0xB,eAAa;IACZxpB;IACAlB;IACA2qB;IACAxvB;IACA0uB;IACAzlB;IACA7I;IACA;AAGR;;SC/CgB0vB,UAAOA;AACrB,MAAA7N,wBAA6BxP,sBAAqB,GAAzCsd,mBAAgB9N,sBAAA,CAAA;AACzB,aACEpkB,4BAAC2uB,QAAM;kBACM;IACXwD,OAAM;IACNC,UAAU;IACV7vB,WAAWkiB,GAAGC,SAAO2N,QAAQ;IAC7BtE,SAAS,SAAAA,UAAAA;AAAA,aAAMmE,iBAAiB,KAAK;;;AAG3C;AAEA,IAAMxN,WAAStlB,WAAWS,OAAMC,SAAA;EAC9BuyB,UAAU;IACR9B,UAAU;IACVa,SAAS;IACTkB,OAAO;IACP5M,cAAc;IACd8L,WAAW;IACXD,YAAY;IACZ1wB,OAAO;IACPD,QAAQ;IACRrB,SAAS;IACTowB,gBAAgB;IAChBD,YAAY;IACZ/uB,YAAY;IACZ,UAAU;MACRmvB,SAAS;MACToB,UAAU;MACVC,WAAW;MACXoB,iBAAe,SAASC,OAAI;MAC5B5M,iBAAiB;MACjB6M,kBAAkB;MAClBC,gBAAgB;MAChBnyB,qBAAqB;;IAEvB,UAAU;MACR+xB,OAAO;MACP1M,iBAAiB;MACjB,UAAU;QACRrlB,qBAAqB;;;IAGzB,UAAU;MACR+xB,OAAO;MACP1M,iBAAiB;MACjB,UAAU;QACRrlB,qBAAqB;;;;AAG1B,GACEO,SAAS,YAAY;EACtB,UAAU;IAAEP,qBAAqB;;EACjC,gBAAgB;IAAEA,qBAAqB;;CACxC,CAAC,CACH;SC7CeoyB,YAASA;AACvB,MAAAvO,wBAAwBxP,sBAAqB,GAAtCge,gBAAaxO,sBAAA,CAAA;AACpB,MAAM7M,eAAeQ,gBAAe;AACpC,MAAMtM,YAAYiF,mBAAkB;AACpC+c,uBAAqBlW,cAAchK,mBAAmByB,SAAS;AAC/D,MAAM7M,aAAa+L,oBAAmB;AACtC,MAAMxC,uBAAuBoC,wBAAuB;AACpD,MAAM1C,cAAcwF,qBAAoB;AAExC,MAAI,CAACgiB,eAAe;AAClB,WAAO;;AAGT,aACE5yB,4BAAAA,MAAAA;IACEuC,WAAWkiB,GAAGC,SAAOmO,MAAM,CAACD,iBAAiBhzB,aAAaN,MAAM;IAChE6lB,KAAK5N;KAEJ9L,UAAUxM,IAAI,SAAA6zB,UAAQ;AAAA,eACrB9yB,4BAAAA,MAAAA;MAAIe,KAAK+xB;WACP9yB,4BAACgyB,gBAAc;MACbhrB,OAAOmC,eAAe2pB,QAAQ;MAC9B3wB;MACA+F,SAAS4qB;MACT1D,gBAAgB;MAChB7sB,WAAWkiB,GAAGC,SAAOqO,WAAW;MAChCxD,cAAY;MACZnkB;MACA;GAEL,GACAM,2BACC1L,4BAAAA,MAAAA,UACEA,4BAACiyB,SAAO,IAAA,CAAG,IAEX,IAAI;AAGd;AAEA,IAAMvN,WAAStlB,WAAWS,OAAO;EAC/BgzB,MAAM;IACJG,WAAW;IACXC,QAAQ;IACR7B,SAAS;IACT7xB,SAAS;IACTowB,gBAAgB;IAChBD,YAAY;IACZ9uB,QAAQ;;EAEVmyB,aAAa;IACX,UAAU;MACR7C,WAAW;;IAEb,UAAU;MACRA,WAAW;;IAEb,WAAW;MACTA,WAAW;;IAEbvvB,YAAY;;CAEf;SC5EeuyB,YAAYhc,SAAmB;AAC7C,MAAM8E,sBAAsBN,uBAAsB;AAElDrO,8BAAU,WAAA;AACR,QAAMwP,UAAU3F,QAAQ5J;AACxB,QAAI,CAACuP,SAAS;AACZ;;AAGFA,YAAQC,iBAAiB,UAAUqW,UAAU;MAC3CnW,SAAS;KACV;AAED,aAASmW,WAAQA;AACfnX,0BAAmB;;AAGrB,WAAO,WAAA;AACLa,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,UAAUkW,QAAQ;;KAEhD,CAACjc,SAAS8E,mBAAmB,CAAC;AACnC;SCrBgBoX,mBAAgBA;AAC9B,MAAAvB,wBAAiC3c,+BAA8B,GAAxDme,yBAAsBxB,sBAAA,CAAA;AAC7B,MAAMyB,kBAAkBnU,mBAAkB;AAE1C,SAAO,SAACnY,OAAgB;AACtB,QAAMkB,UAAUP,aAAaX,KAAK;AAElC,QAAMusB,eAAeF,uBAAuBlhB,IAAIjK,OAAO;AACvD,QAAMsrB,cAAcF,gBAAgBprB,OAAO;AAE3C,WAAO;MACLqrB;MACAC;MACAl0B,QAAQi0B,gBAAgBC;;;AAG9B;SCAgBC,cAAantB,MAAA;MAC3BotB,iBAAcptB,KAAdotB,gBACA1nB,WAAQ1F,KAAR0F,UACA1M,UAAMgH,KAANhH,QACAkB,iBAAc8F,KAAd9F;AAEA,MAAMwD,WAAWK,2BAA2BqvB,cAAc;AAC1D,MAAMC,eAAervB,+BAA+BovB,cAAc;AAElE,aACE1zB,4BAAAA,MAAAA;IACEuC,WAAWkiB,GACTC,SAAO1gB,UACP1E,WAAUM,aAAaN,QACvBkB,kBAAkBH,wBAAwBG,cAAc;iBAE/CwD;kBACC2vB;SAEZ3zB,4BAAAA,MAAAA;IAAIuC,WAAWkiB,GAAGC,SAAOmE,KAAK;KAAI8K,YAAY,OAC9C3zB,4BAAAA,OAAAA;IAAKuC,WAAWkiB,GAAGC,SAAOhK,eAAe;KAAI1O,QAAQ,CAAO;AAGlE;AAEA,IAAM0Y,WAAStlB,WAAWS,OAAO;EAC/BmE,UAAU;IACR,KAAKtF,WAAWsF;IAChB,4BAA4B;MAC1BzE,SAAS;;;EAGbmb,iBAAiB;IACf,KAAKhc,WAAWgc;IAChBnb,SAAS;IACTq0B,SAAS;IACTC,qBAAqB;IACrBlE,gBAAgB;IAChBsD,QAAQ;IACR3N,UAAU;;EAEZuD,OAAO;IACL,KAAKnqB,WAAWmqB;IAChB6G,YAAY;;IAEZ1J,gBAAgB;IAChBJ,iBAAiB;IACjB0M,OAAO;IACP/yB,SAAS;IACTgxB,UAAU;IACVuD,YAAY;IACZlzB,QAAQ;IACRqyB,QAAQ;IACR7B,SAAS;IACT9L,UAAU;IACVyO,eAAe;IACfzb,KAAK;IACLzX,OAAO;IACPuvB,QAAQ;;CAEX;AChFD,IAAI4D,gBAAgB;AAEpB,SAAgBC,mBAAgBA;AAC9B,MAAA3nB,sBAAkCtM,uBAAeg0B,aAAa,GAAvDE,YAAS5nB,gBAAA,CAAA,GAAE6nB,eAAY7nB,gBAAA,CAAA;AAE9BtM,8BAAgB,WAAA;AACdm0B,iBAAa,IAAI;AACjBH,oBAAgB;KACf,CAAA,CAAE;AAEL,SAAOE,aAAaF;AACtB;SCOgBI,UAAS9tB,MAAA;MAAGotB,iBAAcptB,KAAdotB;AAC1B,MAAA7F,sBAA2BnY,mBAAkB,GAAtCE,mBAAgBiY,oBAAA,CAAA;AACvB,MAAMqG,YAAYD,iBAAgB;AAClC,MAAMI,4BAA4B9kB,6BAA4B;AAC9D,MAAMnE,cAAcwF,qBAAoB;AACxC,MAAM0jB,gBAAYt0B;IAChB,WAAA;AAAA,UAAAu0B;AAAA,cAAAA,gBAAMvI,aAAaqI,yBAAyB,MAAC,OAAAE,gBAAI,CAAA;;;IAEjD,CAAC3e,kBAAkBye,yBAAyB;EAAC;AAE/C,MAAMlyB,aAAa+L,oBAAmB;AAEtC,MAAI,CAACgmB,WAAW;AACd,WAAO;;AAGT,aACEl0B,4BAACyzB,eAAa;IACZC;IACAlzB,gBAAc;IACdlB,QAAQg1B,UAAU3xB,WAAW;KAE5B2xB,UAAUr1B,IAAI,SAAAu1B,eAAa;AAC1B,QAAMxtB,QAAQmC,eAAeqrB,cAAcvH,QAAQ;AAEnD,QAAI,CAACjmB,OAAO;AACV,aAAO;;AAGT,eACEhH,4BAACgyB,gBAAc;MACb5C,gBAAgB;MAChBlnB,SAASssB,cAActsB;MACvB/F;MACA6E;MACAjG,KAAKyzB,cAActsB;MACnBkD;;GAGL,CAAC;AAGR;SCjCgBqpB,YAASA;AACvB,MAAM3pB,aAAawD,oBAAmB;AACtC,MAAMomB,gCAA4B10B,qBAAa,CAAC;AAEhD,aACEA,4BAAAA,MAAAA;IAAIuC,WAAWkiB,GAAGC,SAAOiQ,SAAS;KAC/B7pB,WAAW7L,IAAI,SAAAy0B,gBAAc;AAC5B,QAAM1vB,WAAWK,2BAA2BqvB,cAAc;AAE1D,QAAI1vB,aAAaf,WAAWG,WAAW;AACrC,iBAAOpD,4BAACo0B,WAAS;QAACrzB,KAAKiD;QAAU0vB;;;AAGnC,eACE1zB,4BAACA,uBAAc;MAACe,KAAKiD;WACnBhE,4BAAC40B,gBAAc;MACb5wB;MACA0vB;MACAgB;MACA;GAGP,CAAC;AAGR;AAEA,SAASE,eAActuB,MAAA;MACrBtC,WAAQsC,KAARtC,UACA0vB,iBAAcptB,KAAdotB,gBACAgB,4BAAyBpuB,KAAzBouB;AAMA,MAAMG,gBAAgBzB,iBAAgB;AACtC,MAAM9wB,iBAAiBmN,wBAAuB;AAC9C,MAAMtN,aAAa+L,oBAAmB;AACtC,MAAMgG,oBAAoBkB,qBAAoB;AAC9C,MAAAwY,wBAAyB5Y,uBAAsB,GAAxCnB,iBAAc+Z,sBAAA,CAAA;AACrB,MAAM9a,oBAAoBF,qBAAoB;AAC9C,MAAMxH,cAAcwF,qBAAoB;AACxC,MAAMwe,iBAAiB,CAACphB,2BAA0B;AAIlD,MAAM8mB,eACJ,CAAC5gB,qBAAqBwgB,0BAA0BpnB,UAAU,IACtD,CAAA,IACA7E,iBAAiBzE,QAAQ;AAE/B,MAAI8wB,aAAanyB,SAAS,GAAG;AAC3B+xB,8BAA0BpnB;;AAG5B,MAAIynB,gBAAgB;AAEpB,MAAMpsB,UAASmsB,aAAa71B,IAAI,SAAA+H,OAAK;AACnC,QAAMkB,UAAUP,aAAaX,OAAO6M,cAAc;AAClD,QAAAmhB,iBAA8CH,cAAc7tB,KAAK,GAAzDusB,eAAYyB,eAAZzB,cAAcC,cAAWwB,eAAXxB,aAAal0B,UAAM01B,eAAN11B;AAEnC,QAAM21B,eAAeniB,kBAAkB9L,KAAK;AAE5C,QAAI1H,WAAU21B,cAAc;AAC1BF;;AAGF,QAAIE,cAAc;AAChB,aAAO;;AAGT,eACEj1B,4BAACgyB,gBAAc;MACb5C;MACAruB,KAAKmH;MACLlB;MACAkB;MACA5I,QAAQi0B;MACR/yB,gBAAgBgzB;MAChBrxB;MACA0uB,UAAUvuB;MACV8I;;GAGL;AAED,aACEpL,4BAACyzB,eAAa;IACZC;;;IAGAp0B,QAAQy1B,kBAAkBpsB,QAAOhG;KAEhCgG,OAAM;AAGb;AAEA,IAAM+b,WAAStlB,WAAWS,OAAO;EAC/B80B,WAAW;IACT,KAAKj2B,WAAWi2B;IAChB3B,WAAW;IACXC,QAAQ;IACR7B,SAAS;;CAEZ;;ACtGD,IAAK8D;CAAL,SAAKA,YAAS;AACZA,EAAAA,WAAAA,WAAAA,IAAAA,IAAAA,CAAAA,IAAAA;AACAA,EAAAA,WAAAA,WAAAA,MAAAA,IAAAA,CAAAA,IAAAA;AACF,GAHKA,cAAAA,YAAS,CAAA,EAAA;AAMd,SAAgBC,uBAAoBA;AAClC,MAAMle,mBAAmBU,oBAAmB;AAC5C,MAAML,qBAAqBa,sBAAqB;AAChD,MAAAwD,wBAAgBrG,6BAA4B,GAArCtO,QAAK2U,sBAAA,CAAA;AACZ,MAAMxZ,aAAa+L,oBAAmB;AAEtC,MAAAknB,wBAAqCC,sBACnC/d,kBAAkB,GADZge,SAAMF,sBAANE,QAAQC,mBAAgBH,sBAAhBG;AAGhB,MAAMrV,sBAAsBtI,uBAAsB;AAClD,MAAM4d,kBAAkBC,gBAAgBne,kBAAkB;AAC1D,MAAMlM,cAAcwF,qBAAoB;AAExC,MAAMmT,SAASxB,iBAAiBtL,iBAAiB3J,OAAO;AAExD,MAAM4b,UAAUnW,QACd/L,SACE+c,UACAzb,mBAAmBtB,KAAK,KACxB+c,OAAOsH,UAAUC,SAAS5sB,WAAW4J,kBAAkB,CAAC;AAG5D+E,8BAAU,WAAA;AACR,QAAI,CAAC6b,SAAS;AACZ;;AAGF3P,2BAAuBjC,mBAAmBhK,OAAO;KAChD,CAACgK,oBAAoB4R,SAASjS,gBAAgB,CAAC;AAElD,MAAIqB,KAAKod;AAET,MAAI,CAACxM,WAAWjS,iBAAiB3J,SAAS;AACxC4S,wBAAoB,IAAI;SACnB;AACL5H,UAAMgd,OAAM;AACZI,mBAAeF,gBAAe;;AAGhC,aACEx1B,4BAAAA,OAAAA;IACEmlB,KAAK7N;IACL/U,WAAWkiB,GACTC,SAAOzL,iBACPsc,iBAAgB,MAAOL,UAAUS,QAAQjR,SAAOkR,YAChD1M,WAAWxE,SAAOwE,OAAO;IAE3B1mB,OAAO;MAAE8V;;KAER4Q,WAAWliB,QACR,CAACW,aAAaX,KAAK,CAAC,EACjB+lB,OAAOlkB,gBAAgB7B,KAAK,CAAC,EAC7BogB,MAAM,GAAG,CAAC,EACVnoB,IAAI,SAAAiJ,SAAO;AAAA,eACVlI,4BAACgyB,gBAAc;MACbjxB,KAAKmH;MACLlB;MACAkB;MACA/F;MACAitB,gBAAgB;MAChBhkB;;GAEH,IACH,UACJpL,4BAAAA,OAAAA;IAAKuC,WAAWkiB,GAAGC,SAAOmR,OAAO;IAAGrzB,OAAOkzB;IAAgB;AAGjE;AAEA,SAASD,gBAAgBne,oBAAgD;AACvE,MAAML,mBAAmBU,oBAAmB;AAC5C,SAAO,SAAS6d,kBAAeA;AAC7B,QAAMhzB,QAA6B,CAAA;AACnC,QAAI,CAAC8U,mBAAmBhK,SAAS;AAC/B,aAAO9K;;AAGT,QAAIyU,iBAAiB3J,SAAS;AAC5B,UAAMyW,SAASxB,iBAAiBtL,iBAAiB3J,OAAO;AAExD,UAAMsd,aAAaL,mBAAmBxG,MAAM;AAE5C,UAAI,CAACA,QAAQ;AACX,eAAOvhB;;AAITA,YAAM+jB,OAAOqE,cAAa7G,UAAM,OAAA,SAANA,OAAQ+R,eAAc;;AAGlD,WAAOtzB;;AAEX;AAEA,SAAS6yB,sBACP/d,oBAAgD;AAEhD,MAAML,mBAAmBU,oBAAmB;AAC5C,MAAMT,UAAUY,WAAU;AAC1B,MAAIie,YAAYb,UAAUc;AAE1B,SAAO;IACLT;IACAD;;AAGF,WAASC,mBAAgBA;AACvB,WAAOQ;;AAGT,WAAST,SAAMA;AACbS,gBAAYb,UAAUc;AACtB,QAAIC,iBAAiB;AAErB,QAAI,CAAC3e,mBAAmBhK,SAAS;AAC/B,aAAO;;AAGT,QAAM1M,SAAS6lB,cAAcnP,mBAAmBhK,OAAO;AAEvD,QAAI2J,iBAAiB3J,SAAS;AAAA,UAAA4oB;AAC5B,UAAMrZ,UAAU3F,QAAQ5J;AACxB,UAAMyW,SAASxB,iBAAiBtL,iBAAiB3J,OAAO;AAExD,UAAM6oB,eAAe1P,cAAc1C,MAAM;AAEzCkS,uBAAiBpM,mBAAmB9F,MAAM;AAE1C,UAAMtL,aAASyd,qBAAGrZ,WAAO,OAAA,SAAPA,QAASpE,cAAS,OAAAyd,qBAAI;AAExC,UAAIzd,YAAYwd,iBAAiBr1B,QAAQ;AACvCm1B,oBAAYb,UAAUS;AACtBM,0BAAkBE,eAAev1B;;;AAIrC,WAAOq1B,iBAAiBr1B;;AAE5B;AAEA,IAAM8jB,WAAStlB,WAAWS,OAAMC,SAAA;EAC9BmZ,iBAAiB;IACf,KAAKva,WAAWua;IAChBqM,UAAU;IACVyK,OAAO;IACPxJ,MAAM;IACN6K,SAAS;IACTgF,WAAW;IACX1Q,cAAc;IACdnmB,SAAS;IACTmwB,YAAY;IACZC,gBAAgB;IAChBnwB,SAAS;IACTE,YAAY;IACZD,eAAe;IACf6Y,KAAK;IACLyW,QAAQ;IACRnuB,QAAQ;IACRwvB,QAAQ;IACRpB,YAAY;IACZkB,WAAW;IACXvvB,YAAY;;EAEduoB,SAAS;IACP1pB,SAAS;IACTE,YAAY;IACZD,eAAe;IACfywB,WAAW;;EAEb0F,YAAY;IACV,KAAK;IACLS,iBAAiB;IACjBnG,WAAW;;EAEb,gBAAgB;IACd2F,SAAS;MACPvd,KAAK;MACL4X,WAAW;;;EAGf2F,SAAS;IACP,KAAK;IACL/F,SAAS;IACTxK,UAAU;IACVzkB,OAAO;IACPD,QAAQ;IACR6xB,kBAAkB;IAClB6D,oBAAoB;IACpB5D,gBAAgB;IAChBpa,KAAK;IACL4X,WAAW;IACXqC,iBAAe,SAASgE,cAAW;;AACpC,GACEz1B,SAAS,WAAW;EACrBw1B,oBAAoB;CACrB,CAAC,CACH;SC1NeE,OAAIA;AAClB,MAAMtf,UAAUY,WAAU;AAC1Bob,cAAYhc,OAAO;AACnBuW,uBAAqBvW,SAAS3J,mBAAmBkpB,MAAM;AACvD7Z,iBAAc;AAEd,aACE5c,4BAAAA,OAAAA;IACEuC,WAAWkiB,GAAGC,SAAOgS,MAAMr2B,wBAAwBK,iBAAiB;IACpEykB,KAAKjO;SAELlX,4BAACm1B,sBAAoB,IAAA,OACrBn1B,4BAACy0B,WAAS,IAAA,CAAG;AAGnB;AAEA,IAAM/P,WAAStlB,WAAWS,OAAO;EAC/B62B,MAAM;IACJ,KAAKh4B,WAAWwa;IAChByd,MAAM;IACNC,WAAW;IACXC,WAAW;IACXvR,UAAU;;CAEb;SCxCewR,8BACd/S,QACAlH,SAAwB;AAExB,MAAI,CAACkH,UAAU,CAAClH,SAAS;AACvB,WAAO;;AAGT,MAAMka,aAAahT,OAAOmC,sBAAqB;AAC/C,MAAM8Q,WAAWna,QAAQqJ,sBAAqB;AAG9C,SAAO8Q,SAASp2B,UAAUm2B,WAAWE,IAAID,SAASC;AACpD;SCEgBC,sBACdC,OACAC,iBAAmE;AAEnE,MAAMlgB,UAAUY,WAAU;AAC1B,MAAM6E,oBAAoBD,qBAAoB;AAC9C,MAAMD,iBAAiBD,kBAAiB;AAExCnP,8BAAU,WAAA;AACR,QAAI,CAAC8pB,OAAO;AACV;;AAEF,QAAMta,UAAU3F,QAAQ5J;AAExBuP,eAAO,OAAA,SAAPA,QAASC,iBAAiB,WAAWua,UAAU;MAC7Cra,SAAS;KACV;AAEDH,eAAO,OAAA,SAAPA,QAASC,iBAAiB,aAAawa,aAAa,IAAI;AAExDza,eAAO,OAAA,SAAPA,QAASC,iBAAiB,SAASya,SAAS,IAAI;AAEhD1a,eAAO,OAAA,SAAPA,QAASC,iBAAiB,YAAY0a,SAAS;MAC7Cxa,SAAS;KACV;AACDH,eAAO,OAAA,SAAPA,QAASC,iBAAiB,QAAQ0a,SAAS,IAAI;AAE/C,aAASD,QAAQE,GAAa;AAC5B,UAAM1T,SAASxB,iBAAiBkV,EAAE5f,MAAqB;AAEvD,UAAI,CAACkM,QAAQ;AACX,eAAOyT,QAAO;;AAGhB,UAAAE,wBAAqC3M,2BAA2BhH,MAAM,GAA9D7b,UAAOwvB,sBAAPxvB,SAASmhB,kBAAeqO,sBAAfrO;AAEjB,UAAI,CAACnhB,WAAW,CAACmhB,iBAAiB;AAChC,eAAOmO,QAAO;;AAGhBJ,sBAAgB;QACdlvB;QACAmhB;OACD;;AAEH,aAASmO,QAAQC,GAA2B;AAC1C,UAAIA,GAAG;AACL,YAAME,gBAAgBF,EAAEE;AAExB,YAAI,CAACpV,iBAAiBoV,aAAa,GAAG;AACpC,iBAAOP,gBAAgB,IAAI;;;AAI/BA,sBAAgB,IAAI;;AAEtB,aAASC,SAASI,GAAgB;AAChC,UAAIA,EAAE12B,QAAQ,UAAU;AACtBq2B,wBAAgB,IAAI;;;AAIxB,aAASE,YAAYG,GAAa;AAChC,UAAI9a,kBAAiB,GAAI;AACvB;;AAGF,UAAMoH,SAASxB,iBAAiBkV,EAAE5f,MAAqB;AAEvD,UAAIkM,QAAQ;AACV,YAAM6T,gBAAgBd,8BAA8B/S,QAAQlH,OAAO;AACnE,YAAMsZ,eAAepS,OAAOmC,sBAAqB,EAAGtlB;AACpD,YAAIg3B,gBAAgBzB,cAAc;AAChC,iBAAO0B,mCAAmC9T,QAAQqT,eAAe;;AAGnEnhB,qBAAa8N,MAAM;;;AAIvB,WAAO,WAAA;AACLlH,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,aAAaqa,WAAW;AACrDza,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,YAAYua,OAAO;AAChD3a,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,SAASsa,SAAS,IAAI;AACnD1a,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,QAAQua,SAAS,IAAI;AAClD3a,iBAAO,OAAA,SAAPA,QAASI,oBAAoB,WAAWoa,QAAQ;;KAEjD,CAACngB,SAASigB,OAAOC,iBAAiBza,mBAAmBF,cAAc,CAAC;AACzE;AAEA,SAASob,mCACP9T,QACAqT,iBAAmE;;AAEnE,MAAAU,yBAAqC/M,2BAA2BhH,MAAM,GAA9D7b,UAAO4vB,uBAAP5vB,SAASmhB,kBAAeyO,uBAAfzO;AAEjB,MAAI,CAACnhB,WAAW,CAACmhB,iBAAiB;AAChC;;AAGD,GAAA0O,wBAAAlhB,SAASC,kBAA6B,OAAA,SAAtCihB,sBAAwCC,QAAI,OAAA,SAA5CD,sBAAwCC,KAAI;AAE7CZ,kBAAgB;IACdlvB;IACAmhB;GACD;AACH;;ACtHA,IAAY4O;CAAZ,SAAYA,gBAAa;AACvBA,EAAAA,eAAAA,KAAAA,IAAAA;AACAA,EAAAA,eAAAA,QAAAA,IAAAA;AACF,GAHYA,kBAAAA,gBAAa,CAAA,EAAA;AAYzB,SAAwBC,KAAI5xB,MAAA;MAC1B0F,WAAQ1F,KAAR0F,UACAzJ,YAAS+D,KAAT/D,WAAS41B,aAAA7xB,KACT9D,OAAAA,QAAK21B,eAAA,SAAG,CAAA,IAAEA,YAAAC,iBAAA9xB,KACVyvB,WAAAA,YAASqC,mBAAA,SAAGH,cAAcI,MAAGD;AAE7B,aACEp4B,4BAAAA,OAAAA;IACEwC,OAAK1C,SAAA,CAAA,GAAO0C,KAAK;IACjBD,WAAWkiB,GAAGC,SAAOiS,MAAMp0B,WAAWmiB,SAAOqR,SAAS,CAAC;KAEtD/pB,QAAQ;AAGf;AAEA,IAAM0Y,WAAStlB,WAAWS,QAAMy4B,qBAAA;EAC9B3B,MAAM;IACJp3B,SAAS;;AACV,GAAA+4B,mBACAL,cAAcI,GAAG,IAAG;EACnB9S,eAAe;GAChB+S,mBACAL,cAAcM,MAAM,IAAG;EACtBhT,eAAe;GAChB+S,mBAAA;SCjCqB9V,MAAKlc,MAAA;MAAG/D,YAAS+D,KAAT/D,WAAS41B,aAAA7xB,KAAE9D,OAAAA,QAAK21B,eAAA,SAAG,CAAA,IAAEA;AACnD,aAAOn4B,4BAAAA,OAAAA;IAAKwC,OAAK1C,SAAA;MAAI62B,MAAM;OAAMn0B,KAAK;IAAID,WAAWkiB,GAAGliB,SAAS;;AACnE;SCHwBi2B,SAAQlyB,MAAA;MAAG0F,WAAQ1F,KAAR0F,UAAUzJ,YAAS+D,KAAT/D,WAAWC,QAAK8D,KAAL9D;AACtD,aACExC,4BAAAA,OAAAA;IAAKwC,OAAK1C,SAAA,CAAA,GAAO0C,OAAK;MAAE8iB,UAAU;;IAAc/iB;KAC7CyJ,QAAQ;AAGf;SCNwBysB,SAAQnyB,MAAA;MAAG0F,WAAQ1F,KAAR0F,UAAUzJ,YAAS+D,KAAT/D,WAAWC,QAAK8D,KAAL9D;AACtD,aACExC,4BAAAA,OAAAA;IAAKwC,OAAK1C,SAAA,CAAA,GAAO0C,OAAK;MAAE8iB,UAAU;;IAAc/iB;KAC7CyJ,QAAQ;AAGf;ACGA,SAAgB0sB,qBAAoBpyB,MAAA;MAClCsb,SAAMtb,KAANsb,QACAmM,UAAOznB,KAAPynB,SACA4K,WAAQryB,KAARqyB,UACAC,oBAAiBtyB,KAAjBsyB,mBACAp2B,QAAK8D,KAAL9D;AAEA,aACExC,4BAAC2uB,QAAM;IACLnsB;IACAurB;IACAxrB,WAAWkiB,GAAE,cACCmU,mBACZlU,SAAOmU,MACP,CAACjX,UAAU8C,SAAOoU,YAClBH,YAAYjU,SAAOqU,MAAM;IAE3B3G,UAAUxQ,SAAS,IAAI;oBACT+W;iCACWzyB,eAAe0yB,iBAA8B;;AAG5E;AAEA,IAAMlU,WAAStlB,WAAWS,OAAO;EAC/Bi5B,YAAY;IACVt5B,SAAS;IACT4wB,QAAQ;;EAEV2I,QAAQ;IACN,KAAK;IACL3I,QAAQ;IACR5wB,SAAS;;EAEXq5B,MAAM;IACJ,KAAK;IACL,UAAU;MACRzC,WAAW;;IAEb,UAAU;MACRA,WAAW;;IAEb,sBAAsB;MACpBxQ,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;IAEnB,oBAAoB;MAClBA,iBAAiB;;;CAGtB;ACvDD,IAAMoT,YAAY;AAMlB,SAAgBC,qBAAkBA;AAChC,aACEj5B,4BAACy4B,UAAQ;IAACj2B,OAAO;MAAE5B,QAAQo4B;;SACzBh5B,4BAACw4B,UAAQ;IAACh2B,OAAO;MAAEwlB,QAAQ;MAAG+H,OAAO;;SACnC/vB,4BAACk5B,gBAAc;IAACnD,WAAWoD,wBAAwBC;IAAY,CACtD;AAGjB;AAEA,SAAgBF,eAAc5yB,MAAA;4BAC5ByvB,WAAAA,YAASqC,mBAAA,SAAGe,wBAAwBE,aAAUjB;AAE9C,MAAMhhB,oBAAoBa,qBAAoB;AAC9C,MAAMqhB,aAAatrB,2BAA0B;AAC7C,MAAA6N,wBAA4BrG,wBAAuB,GAA5CoM,SAAM/F,sBAAA,CAAA,GAAEgG,YAAShG,sBAAA,CAAA;AACxB,MAAA+R,wBAA4C5Y,uBAAsB,GAA3DnB,iBAAc+Z,sBAAA,CAAA,GAAE2L,oBAAiB3L,sBAAA,CAAA;AACxC,MAAMxgB,mBAAmB6B,0BAAyB;AAClD,MAAM+M,sBAAsBN,uBAAsB;AAClD,MAAMiC,mBAAmBT,oBAAmB;AAE5C,MAAIoc,YAAY;AACd,WAAO;;AAGT,MAAME,YAAeR,YAAYrzB,mBAAmBhD,SAAM;AAE1D,MAAM82B,eAAe7X,SAAS4X,YAAYR,YAAY;AAEtD,MAAMU,WAAW3D,cAAcoD,wBAAwBC;AAEvD,aACEp5B,4BAACy4B,UAAQ;IACPl2B,WAAWkiB,GACTC,SAAOiV,WACPD,YAAYhV,SAAOgV,UACnB9X,UAAU8C,SAAO/iB,MACjB+3B,YAAY9X,UAAU8C,SAAOkV,cAAc;IAE7Cp3B,OACEk3B,WACI;MAAEG,WAAWJ;MAAc74B,QAAQ64B;QACnC;MAAEI,WAAWJ;;SAGnBz5B,4BAAAA,OAAAA;IAAKuC,WAAWkiB,GAAGC,SAAOoV,MAAM;IAAG3U,KAAK/N;KACrCzR,mBAAmB1G,IAAI,SAAC25B,mBAAmB5L,GAAC;AAC3C,QAAM+L,SAASH,sBAAsB/kB;AAErC,eACE7T,4BAAC04B,sBAAoB;MACnB33B,KAAK63B;MACLA;MACAhX;MACApf,OAAO;QACL0tB,WAAWzL,GACTiV,WAAQ,iBACW1M,KAAKpL,SAASoX,YAAY,KAAE,QAAA,iBAC5BhM,KAAKpL,SAASoX,YAAY,KAAE,OAC/CpX,UAAUmX,UAAU,YAAY;;MAGpCJ,UAAUI;MACVhL,SAAS,SAAAA,UAAAA;AACP,YAAInM,QAAQ;AACV2X,4BAAkBX,iBAAiB;AACnCxrB,2BAAiBwrB,iBAAiB;AAClCjb,2BAAgB;eACX;AACLkE,oBAAU,IAAI;;AAEhB7F,4BAAmB;;;GAI1B,CAAC,CACE;AAGZ;AAEA,IAAYmd;CAAZ,SAAYA,0BAAuB;AACjCA,EAAAA,yBAAAA,UAAAA,IAAAA;AACAA,EAAAA,yBAAAA,YAAAA,IAAAA;AACF,GAHYA,4BAAAA,0BAAuB,CAAA,EAAA;AAKnC,IAAMzU,WAAStlB,WAAWS,OAAO;EAC/B85B,WAAW;IACT,KAAK;IACL,MAAM;MACJ,wBAAwB;;IAE1Bp6B,SAAS;IACTmwB,YAAY;IACZC,gBAAgB;IAChBhvB,YAAY;IACZywB,SAAS;;EAEXsI,UAAU;IACRtI,SAAS;IACT1B,YAAY;IACZnK,eAAe;IACfG,cAAc;IACdqJ,QAAQ;;EAEV6K,gBAAgB;IACdxD,WAAW;;EAEbz0B,MAAM;;IAEJqkB,gBAAgB;IAChBgJ,YAAY;IACZ,qBAAqB;MACnBc,SAAS;MACTxK,UAAU;MACVhN,KAAK;MACLiO,MAAM;MACNwJ,OAAO;MACP/H,QAAQ;MACRtC,cAAc;MACdqJ,QAAQ;;;EAGZ+K,QAAQ;IACN,KAAK;IACLxU,UAAU;IACVzkB,OAAO;IACPD,QAAQ;IACR,YAAY;MACVC,OAAO;MACPtB,SAAS;MACTuvB,QAAQ;MACRpJ,cAAc;MACd9kB,QAAQ;MACR0kB,UAAU;MACVyK,OAAO;MACPpvB,YAAY;MACZyvB,QAAQ;MACRgG,WAAW;;;CAGhB;SChJe2D,UAAOA;AACrB,MAAMpvB,gBAAgBwE,iBAAgB;AACtC,MAAM2S,sBAAsBtB,uBAAsB;AAElD,MAAI,CAAC7V,cAAckB,aAAa;AAC9B,WAAO;;AAGT,aACE7L,4BAACk4B,MAAI;IACH31B,WAAWkiB,GAAGC,SAAOsV,SAAS35B,wBAAwBK,iBAAiB;SAEvEV,4BAACi6B,aAAW,IAAA,OACZj6B,4BAACwiB,OAAK,IAAA,GACLV,0BAAsB9hB,4BAACi5B,oBAAkB,IAAA,IAAM,IAAI;AAG1D;AAEA,SAAgBgB,cAAWA;;AACzB,MAAMtvB,gBAAgBwE,iBAAgB;AACtC,MAAAoC,gBAAwCC,uBAAuB,IAAI,GAA5D0oB,eAAY3oB,UAAA,CAAA,GAAE6lB,kBAAe7lB,UAAA,CAAA;AACpC,MAAMpP,aAAa+L,oBAAmB;AACtC,MAAAyN,wBAA+BrG,6BAA4B,GAApD6kB,uBAAoBxe,sBAAA,CAAA;AAC3B,MAAMvQ,cAAcwF,qBAAoB;AAExCsmB,wBAAsBvsB,cAAckB,aAAaurB,eAAe;AAEhE,MAAMpwB,QAAQmC,gBAAcixB,wBAC1BF,gBAAY,OAAA,SAAZA,aAAchyB,YAAO,OAAAkyB,wBAAIF,gBAAY,OAAA,SAAZA,aAAc7Q,eAAe;AAGxD,MAAMgR,OAAOrzB,SAAS,QAAQkzB,gBAAgB;AAE9C,aAAOl6B,4BAACs6B,gBAAc,IAAA;AAEtB,WAASA,iBAAcA;AACrB,QAAM3uB,eACJwuB,wBAAoB,OAApBA,uBAAwBhxB,eAAewB,cAAcgB,YAAY;AACnE,QAAI,CAACA,cAAc;AACjB,aAAO;;AAET,QAAM4uB,cAAcJ,uBAChBnyB,UAAUmyB,oBAAoB,IAC9BxvB,cAAciB;AAElB,eACE5L,4BAAAA,uBAAAA,UACEA,4BAAAA,OAAAA,MACGq6B,WACCr6B,4BAAC0xB,eAAa;MACZxpB,SAASgyB,gBAAY,OAAA,SAAZA,aAAchyB;MACvBlB;MACA7E;MACAwvB,MAAM;MACNvmB;MACA7I,WAAWkiB,GAAGC,SAAO1d,KAAK;SAE1B2E,mBACF3L,4BAAC0xB,eAAa;MACZxpB,SAASP,aAAagE,YAAY;MAClC3E,OAAO2E;MACPxJ;MACAwvB,MAAM;MACNvmB;MACA7I,WAAWkiB,GAAGC,SAAO1d,KAAK;SAE1B,IAAI,OAEVhH,4BAAAA,OAAAA;MAAKuC,WAAWkiB,GAAGC,SAAOmE,KAAK;OAC5BwR,OAAOryB,UAAUhB,KAAK,IAAIuzB,WAAW,CAClC;;AAId;AAOA,IAAM7V,WAAStlB,WAAWS,OAAO;EAC/Bm6B,SAAS;IACPtK,YAAY;IACZ8K,WAAW;IACX55B,QAAQ;IACRwwB,SAAS;IACT9L,UAAU;IACV8K,QAAQ;;EAEVvH,OAAO;IACLyJ,OAAO;IACP/B,UAAU;IACVa,SAAS;IACT2C,eAAe;;EAEjB/sB,OAAO;IACLoqB,SAAS;;CAEZ;SC9HeqJ,oBAAoBC,WAAyB;;AAC3D,UAAAC,wBAAOD,aAAS,OAAA,SAATA,UAAWE,aAAa,WAAW,MAAC,OAAAD,wBAAI;AACjD;SCIgBE,iCACdC,mBAA6C;AAE7C,MAAM5jB,UAAUY,WAAU;AAE1BzK,8BAAU,WAAA;AACR,QAAM0tB,oBAAoB,oBAAIC,IAAG;AACjC,QAAMne,UAAU3F,QAAQ5J;AACxB,QAAM2tB,WAAW,IAAIC,qBACnB,SAAA/0B,SAAO;AACL,UAAI,CAAC0W,SAAS;AACZ;;AAGF,eAAAse,YAAAC,gCAAoBj1B,OAAO,GAAAk1B,OAAA,EAAAA,QAAAF,UAAA,GAAAG,QAAE;AAAA,YAAlBC,QAAKF,MAAAr6B;AACd,YAAM4I,MAAK6wB,oBAAoBc,MAAM1jB,MAAM;AAC3CkjB,0BAAkBS,IAAI5xB,KAAI2xB,MAAME,iBAAiB;;AAGnD,UAAMC,SAAS78B,MAAM8pB,KAAKoS,iBAAiB;AAC3C,UAAMY,eAAeD,OAAOA,OAAO/4B,SAAS,CAAC;AAE7C,UAAIg5B,aAAa,CAAC,KAAK,GAAG;AACxB,eAAOb,kBAAkBa,aAAa,CAAC,CAAC;;AAG1C,eAAA7S,KAAA,GAAA8S,UAA0BF,QAAM5S,KAAA8S,QAAAj5B,QAAAmmB,MAAE;AAA7B,YAAA+S,aAAAD,QAAA9S,EAAA,GAAOlf,KAAEiyB,WAAA,CAAA,GAAEC,QAAKD,WAAA,CAAA;AACnB,YAAIC,OAAO;AACThB,4BAAkBlxB,EAAE;AACpB;;;OAIN;MACEmyB,WAAW,CAAC,GAAG,CAAC;KACjB;AAEHlf,eAAO,OAAA,SAAPA,QAAS+L,iBAAiBjqB,YAAYD,WAAWsF,QAAQ,CAAC,EAAEwD,QAAQ,SAAAw0B,IAAE;AACpEf,eAASgB,QAAQD,EAAE;KACpB;KACA,CAAC9kB,SAAS4jB,iBAAiB,CAAC;AACjC;SCxCgBoB,4BAAyBA;AACvC,MAAMhlB,UAAUY,WAAU;AAC1B,MAAMd,gBAAgBU,iBAAgB;AAEtC,SAAO,SAASykB,uBAAuBn4B,UAAgB;;AACrD,QAAI,CAACkT,QAAQ5J,SAAS;AACpB;;AAEF,QAAMotB,aAAS0B,mBAAGllB,QAAQ5J,YAAO,OAAA,SAAf8uB,iBAAiBnS,cAAa,iBAC/BjmB,WAAQ,IAAI;AAG7B,QAAI,CAAC02B,WAAW;AACd;;AAGF,QAAMhQ,YAAYgQ,UAAUhQ,aAAa;AAEzCtS,aAASpB,cAAc1J,SAASod,SAAS;;AAE7C;SCzBgB2R,4BAAyBA;AACvC,MAAMC,uBAAuB9tB,sBAAqB;AAElD,MAAI,CAAC8tB,sBAAsB;AACzB,WAAO;;AAGT,SAAOA,qBAAqB35B,WAAW;AACzC;;SCegB45B,eAAcj2B,MAAA;;MAC5Bk2B,mBAAgBl2B,KAAhBk2B,kBACAx4B,WAAQsC,KAARtC,UACAy4B,kBAAen2B,KAAfm2B,iBACA/I,iBAAcptB,KAAdotB,gBACA3F,UAAOznB,KAAPynB;AAEA,aACE/tB,4BAAC2uB,QAAM;IACLyD,UAAUqK,kBAAkB,IAAI;IAChCl6B,WAAWkiB,GACTC,SAAOgY,QACPr8B,wBAAwBC,aAAW,aACxB0D,WAAQghB,MAAA,CAAA,GAAAA,IAEhBtmB,WAAWq6B,MAAM,IAAGyD,kBAAgBxX,IAAA;IAGzC+I;kBACYzpB,+BAA+BovB,cAAc;qBAC1C8I;IACfG,MAAK;qBACS;;AAGpB;AAEA,IAAMC,sBAAsB;EAC1Br8B,qBAAqB;;AAEvB,IAAMs8B,gBAAgB;EACpBt8B,qBAAqB;;AAGvB,IAAMu8B,uBAAuB;EAC3B,4BAA4B;IAC1BJ,QAAQ;MACN,UAAUE;MACV,gBAAgBA;;;;AAKtB,IAAMlY,WAAStlB,WAAWS,OAAMC,SAAA;EAC9B48B,QAAQ;IACN,KAAK;IACLn9B,SAAS;IACToB,YAAY;IACZ2kB,UAAU;IACV1kB,QAAQ;IACRC,OAAO;IACP6xB,gBAAgB;IAChBzD,SAAS;IACTqH,oBAAoB;IACpB/D,iBAAe,SAASwK,gBAAa;IACrC,iBAAiB;MACfjN,SAAS;MACTxK,UAAU;MACVhN,KAAK;MACLiO,MAAM;MACNwJ,OAAO;MACP/H,QAAQ;MACR+G,QAAQ;MACRrJ,cAAc;;IAEhB,uBAAuB;MACrBsX,qBACE;;IAEJ,oBAAoB;MAClBA,qBACE;;IAEJ,wBAAwB;MACtBA,qBACE;;IAEJ,4BAA4B;MAC1BA,qBACE;;IAEJ,mBAAmB;MACjBA,qBACE;;IAEJ,wBAAwB;MACtBA,qBACE;;IAEJ,qBAAqB;MACnBA,qBACE;;IAEJ,4BAA4B;MAC1BA,qBAAqB;;IAEvB,qBAAqB;MACnBA,qBACE;;IAEJ,2BAA2B;MACzBA,qBACE;;;AAEL,GACEl8B,SAAS,UAAU+7B,aAAa,GAAC;EACpC,mBAAiB/8B,SAAA,CAAA,GACZg9B,oBAAoB;EAEzB,mBAAiBh9B,SAAA,CAAA,GACZg9B,oBAAoB;AACxB,CAAA,CACF;SCzHeG,qBAAkBA;AAChC,MAAA1rB,gBAA4CC,uBAAwB,IAAI,GAAjE0rB,iBAAc3rB,UAAA,CAAA,GAAEupB,oBAAiBvpB,UAAA,CAAA;AACxC,MAAM4qB,yBAAyBD,0BAAyB;AACxDrB,mCAAiCC,iBAAiB;AAClD,MAAMpY,eAAe3M,gBAAe;AAEpC,MAAMonB,mBAAmB7uB,oBAAmB;AAC5C,MAAM+I,wBAAwBa,yBAAwB;AACtD,MAAMklB,qBAAqBf,0BAAyB;AAEpD,aACEr8B,4BAAAA,OAAAA;IACEuC,WAAWkiB,GAAGC,SAAO2Y,GAAG;IACxBV,MAAK;kBACM;IACX/yB,IAAG;IACHub,KAAK9N;KAEJ8lB,iBAAiBl+B,IAAI,SAAAy0B,gBAAc;AAClC,QAAM1vB,WAAWK,2BAA2BqvB,cAAc;AAC1D,QAAM8I,mBAAmBx4B,aAAak5B;AAEtC,QAAI3P,iBAAiBmG,cAAc,KAAK0J,oBAAoB;AAC1D,aAAO;;AAGT,QAAMX,kBAAkB,CAAC/Z,gBAAgB,CAAC8Z;AAE1C,eACEx8B,4BAACu8B,gBAAc;MACbx7B,KAAKiD;MACLA;MACAw4B;MACAC;MACA/I;MACA3F,SAAS,SAAAA,UAAAA;AACP+M,0BAAkB92B,QAAQ;AAC1Bm4B,+BAAuBn4B,QAAQ;;;GAItC,CAAC;AAGR;AAEA,IAAM0gB,WAAStlB,WAAWS,OAAO;EAC/Bw9B,KAAK;IACH,KAAK;IACL99B,SAAS;IACTgmB,eAAe;IACfoK,gBAAgB;IAChByB,SAAS;;EAEX,sBAAsB;IACpBiM,KAAK;MACH79B,SAAS;MACTsvB,QAAQ;MACRrvB,eAAe;;;EAGnB,gDAAgD;IAC9C49B,KAAK;MACH79B,SAAS;MACTsvB,QAAQ;MACRrvB,eAAe;;;CAGpB;;SCvEe69B,iBAAcA;AAC5B,MAAM1f,cAAcJ,eAAc;AAElC,aACExd,4BAAC2uB,QAAM;IACLpsB,WAAWkiB,GACTC,SAAO6Y,gBACPl9B,wBAAwBI,mBAAmB;IAE7CstB,SAASnQ;kBACE;IACXuU,OAAM;SAENnyB,4BAAAA,OAAAA;IAAKuC,WAAWkiB,GAAGC,SAAO8Y,eAAe;IAAK;AAGpD;AAEA,IAAMC,YAAY;EAChB,UAAU;IACR,2BAA2B;MACzBl9B,qBAAqB;;;;AAK3B,IAAMmkB,WAAStlB,WAAWS,OAAMC,SAAA;EAC9By9B,gBAAgB;IACd,KAAK;IACLjY,UAAU;IACVyK,OAAO;IACPnvB,QAAQ;IACRC,OAAO;IACPtB,SAAS;IACTmwB,YAAY;IACZC,gBAAgB;IAChBrX,KAAK;IACL4X,WAAW;IACXkB,SAAS;IACT1L,cAAc;IACd,UAAU;MACRsJ,YAAY;;IAEd,UAAU;MACRA,YAAY;;;EAGhBwO,iBAAiB;IACf,KAAK;IACL5X,iBAAiB;IACjB6M,kBAAkB;IAClBC,gBAAgB;IAChB9xB,QAAQ;IACRC,OAAO;IACP0xB,iBAAe,SAASmL,WAAQ;IAChC,UAAU;MACRn9B,qBAAqB;;IAEvB,UAAU;MACRA,qBAAqB;;;AAExB,GACEO,SAAS,mBAAmB;EAC7BP,qBAAqB;CACtB,GACEO,SAAS,kBAAkB28B,SAAS,CAAC,CACzC;AC1ED,IAAME,QAAWh/B,YAAYD,WAAW2mB,WAAW,IAAC,MAAI1mB,YACtDD,WAAWi2B,SAAS;AAGtB,IAAMiJ,eAAe,CAAC,UAAUj/B,YAAYD,WAAWsI,KAAK,CAAC,EAAE7H,KAAK,EAAE;AACtE,IAAM0+B,WAAWl/B,YAAYD,WAAWsF,QAAQ;AAEhD,SAAgB85B,UAASx3B,MAAA;MAAGtF,QAAKsF,KAALtF;AAC1B,MAAI,CAACA,OAAO;AACV,WAAO;;AAGT,MAAM+8B,IAAIC,SAASh9B,KAAK;AAExB,aACEhB,4BAAAA,SAAAA,MAAAA,WACE29B,QAAK,MAAIC,eAAY,8CAKrBD,QAAK,MAAII,IAAC,4CAIVJ,QAAK,MAAIE,WAAQ,eAAaE,IAAC,uCAGlC;AAEH;AAEA,SAASC,SAASh9B,OAAa;AAC7B,SAAO,CACL48B,cACA,sBACA5f,wBAAwBhd,KAAK,GAC7B,IAAI,EACJ7B,KAAK,EAAE;AACX;;SCrCgB8+B,YAASA;AACvB,aAAOj+B,4BAAAA,OAAAA;IAAKuC,WAAWkiB,GAAGC,SAAOwZ,SAAS;;AAC5C;AAEA,IAAMxZ,WAAStlB,WAAWS,OAAMC,SAAA;EAC9Bo+B,WAAW;IACT,KAAK;IACLpO,SAAS;IACTxK,UAAU;IACVhN,KAAK;IACLiO,MAAM;IACN2J,WAAW;IACXrvB,OAAO;IACPD,QAAQ;IACR6xB,kBAAkB;IAClB6D,oBAAoB;IACpB5D,gBAAgB;IAChBH,iBAAe,SAAS4L,eAAY;;AACrC,GACEr9B,SAAS,aAAa;EACvBP,qBAAqB;CACtB,CAAC,CACH;SCNe69B,kBAAeA;AAC7B,MAAM37B,iBAAiB2N,wBAAuB;AAE9C,MAAMoR,qBAAqBlB,sBAAqB;AAEhD,MAAI7d,gBAAgB;AAClB,WAAO;;AAGT,aACEzC,4BAACk4B,MAAI;IAAC31B,WAAWkiB,GAAGC,SAAO2Z,OAAO;SAChCr+B,4BAACs+B,QAAM,IAAA,GAEN9c,yBAAqBxhB,4BAACk5B,gBAAc,IAAA,IAAM,IAAI;AAGrD;AAEA,SAAgBoF,SAAMA;AACpB,MAAA/sB,gBAAsBC,uBAAS,CAAC,GAAzB+sB,MAAGhtB,UAAA,CAAA,GAAEitB,SAAMjtB,UAAA,CAAA;AAClB,MAAMyK,sBAAsBN,uBAAsB;AAClD,MAAMvE,iBAAiBa,kBAAiB;AACxC,MAAMymB,cAAcjxB,2BAA0B;AAC9C,MAAMkxB,YAAYtwB,yBAAwB;AAC1C,MAAAuwB,aAAsD1gB,UAAS,GAAvDE,sBAAmBwgB,WAAnBxgB,qBAAqBxK,aAAUgrB,WAAVhrB,YAAY0K,YAAQsgB,WAARtgB;AAEzC,MAAMugB,QAAQznB,kBAAc,OAAA,SAAdA,eAAgB7J;AAC9B,MAAMtM,QAAQ49B,SAAK,OAAA,SAALA,MAAO59B;AAErB,aACEhB,4BAACy4B,UAAQ;IAACl2B,WAAWkiB,GAAGC,SAAOma,eAAe;SAC5C7+B,4BAAC89B,WAAS;IAAC98B;UACXhB,4BAAAA,SAAAA;;IAEE0+B;kBACY;IACZ5a,SAAS9H;IACTzZ,WAAWkiB,GAAGC,SAAOoa,MAAM;IAC3BjQ,MAAK;qBACS;IACd4P;IACApgB,UAAU,SAAAA,SAAA4C,OAAK;AACbud,aAAOD,MAAM,CAAC;AACd13B,iBAAW,WAAA;;AACTwX,mBAAQ0gB,sBAAC9d,SAAK,OAAA,UAAA+d,gBAAL/d,MAAOpJ,WAAM,OAAA,SAAbmnB,cAAeh+B,UAAK,OAAA+9B,sBAAI/9B,KAAK;OACvC;;IAEHmkB,KAAKhO;MAENxD,iBACC3T,4BAAAA,OAAAA;IACE28B,MAAK;IACLp6B,WAAWkiB,GAAG,6BAA6BC,SAAOua,cAAc;iBACtD;IACVr1B,IAAG;mBACS;KAEXuU,mBAAmB,IAEpB,UACJne,4BAACi+B,WAAS,IAAA,OACVj+B,4BAACs9B,gBAAc,IAAA,CAAG;AAGxB;AAEA,IAAM5Y,WAAStlB,WAAWS,OAAMC,SAAA;EAC9Bu+B,SAAS;IACPjN,SAAS;IACThB,QAAQ;;EAEVyO,iBAAiB;IACf,KAAK;IACLlI,MAAM;IACNp3B,SAAS;IACT2xB,UAAU;;EAEZ+N,gBAAgB;IACdC,MAAM;IACNC,UAAU;IACVv+B,QAAQ;IACRjB,UAAU;IACV2lB,UAAU;IACV8Z,YAAY;IACZv+B,OAAO;;EAETi+B,QAAQ;IACN7P,SAAS;IACTtuB,YAAY;IACZ2xB,OAAO;IACP5M,cAAc;IACd0L,SAAS;IACTxwB,QAAQ;IACRglB,iBAAiB;IACjBmJ,QAAQ;IACRluB,OAAO;IACP,UAAU;MACR+kB,iBAAiB;MACjBmJ,QAAQ;;IAEV,iBAAiB;MACfuD,OAAO;;;EAIXiL,gBAAgB;IACd,KAAK;IACLjY,UAAU;IACVyK,OAAO;IACPnvB,QAAQ;IACRC,OAAO;IACPtB,SAAS;IACTmwB,YAAY;IACZC,gBAAgB;IAChBrX,KAAK;IACL4X,WAAW;IACXkB,SAAS;IACT1L,cAAc;IACd,UAAU;MACRsJ,YAAY;;IAEd,UAAU;MACRA,YAAY;;;EAGhBwO,iBAAiB;IACf,KAAK;IACL5X,iBAAiB;IACjB6M,kBAAkB;IAClBC,gBAAgB;IAChB9xB,QAAQ;IACRC,OAAO;IACP0xB,iBAAe,SAASmL,WAAQ;IAChC,UAAU;MACRn9B,qBAAqB;;IAEvB,UAAU;MACRA,qBAAqB;;;AAExB,GACEO,SAAS,mBAAmB;EAC7BP,qBAAqB;CACtB,GACEO,SAAS,kBAAkB;EAC5B,kCAAkC;IAChCP,qBAAqB;;CAExB,CAAC,CACH;SClKe8+B,SAAMA;AACpB,aACEr/B,4BAACy4B,UAAQ;IACPl2B,WAAWkiB,GAAG,cAAcpkB,wBAAwBK,iBAAiB;SAErEV,4BAACo+B,iBAAe,IAAA,OAChBp+B,4BAACi9B,oBAAkB,IAAA,CAAG;AAG5B;ACFA,SAASqC,YAAY1Q,OAAkB;AACrC,aACE5uB,4BAAC+W,2BAAyB,UACxB/W,4BAACD,gBAAc,IAAA,OACfC,4BAAC+L,sBAAoB,OAAA,OAAA,CAAA,GAAK6iB,KAAK,OAC7B5uB,4BAACu/B,gBAAc,IAAA,CAAG,CACG;AAG7B;AAEA,SAASA,iBAAcA;AACrB,MAAAnb,wBAA+BxP,sBAAqB,GAA7C/S,uBAAoBuiB,sBAAA,CAAA;AAC3B,MAAM1Y,uBAAuBoC,wBAAuB;AAEpD,MAAAxB,sBAAkCtM,uBAAe,CAAC6B,oBAAoB,GAA/D29B,YAASlzB,gBAAA,CAAA,GAAEmzB,eAAYnzB,gBAAA,CAAA;AAC9B,MAAMsV,SAASlT,cAAa;AAE5B1O,8BAAgB,WAAA;AACd,QAAI6B,wBAAwB,CAAC6J,sBAAsB;AACjD;;AAGF,QAAI,CAAC8zB,WAAW;AACdC,mBAAa,IAAI;;KAElB,CAACD,WAAW9zB,sBAAsB7J,oBAAoB,CAAC;AAE1D,MAAI,CAAC+f,QAAQ;AACX,WAAO;;AAGT,aACE5hB,4BAACikB,YAAU,UACTjkB,4BAAC2yB,WAAS,IAAA,OACV3yB,4BAAC0/B,uBAAqB;IAACF;IAAwB;AAGrD;AAEA,SAASE,sBAAqBp5B,MAAA;MAAGk5B,YAASl5B,KAATk5B;AAC/B,MAAI,CAACA,WAAW;AACd,WAAO;;AAGT,aACEx/B,4BAAAA,uBAAAA,UACEA,4BAACq/B,QAAM,IAAA,OACPr/B,4BAACw2B,MAAI,IAAA,OACLx2B,4BAAC+5B,SAAO,IAAA,CAAG;AAGjB;AAGA,IAAA,uBAAe/5B,mBAAWs/B,aAAan+B,aAAa;ACvErB,IAEVw+B,gBAAc,SAAAC,kBAAA;AAAAC,iBAAAF,gBAAAC,gBAAA;AAIjC,WAAAD,eAAY/Q,OAAoC;;AAC9CkR,YAAAF,iBAAAG,KAAA,MAAMnR,KAAK,KAAC;AACZkR,UAAKruB,QAAQ;MAAEuuB,UAAU;;AAAQ,WAAAF;;AAClCH,EAAAA,eAEMM,2BAAP,SAAAA,2BAAAA;AACE,WAAO;MAAED,UAAU;;;AACpB,MAAAE,SAAAP,eAAAQ;AAAAD,SAEDE,oBAAA,SAAAA,kBAAkBC,OAAcC,WAAc;AAE5CC,YAAQF,MAAM,wCAAwCA,OAAOC,SAAS;;AACvEJ,SAEDM,SAAA,SAAAA,SAAAA;AACE,QAAI,KAAK/uB,MAAMuuB,UAAU;AACvB,aAAO;;AAGT,WAAO,KAAKpR,MAAM5iB;;AACnB,SAAA2zB;AAAA,EAxBwC3/B,sBAG1C;SCEeygC,cAAan6B,MAAA;MAC3B4B,UAAO5B,KAAP4B,SAAOw4B,YAAAp6B,KACPqrB,MAAAA,OAAI+O,cAAA,SAAG,KAAEA,WAAAC,kBAAAr6B,KACTnE,YAAAA,aAAUw+B,oBAAA,SAAG79B,WAAW4C,QAAKi7B,iBAAA/P,gBAAAtqB,KAC7BuqB,UAAAA,WAAQD,kBAAA,SAAG,QAAKA,eAChBxlB,cAAW9E,KAAX8E,aACAmY,WAAQjd,KAARid;AASA,MAAI,CAACrb,WAAW,CAACqb,YAAY,CAACnY,aAAa;AACzC,WAAO;;AAGT,aACEpL,4BAAC0xB,eAAa;IACZxpB;IACAypB;IACAxvB;IACA0uB;IACAzlB,aAAamY,WAAW,WAAA;AAAA,aAAMA;QAAWnY;;AAG/C;SCXwBk0B,cAAY1Q,OAAkB;AACpD,MAAM7hB,mBAAmBD,uBAAuB;IAC9CG,cAAc2hB,MAAM3hB;IACpBE,iBAAiByhB,MAAMzhB;IACvBC,kBAAkBwhB,MAAMxhB;GACzB;AAED,aACEpN,4BAAC2/B,eAAa,UACZ3/B,4BAACyM,qBAAqBJ,UAAQ;IAACrL,OAAO+L;SACpC/M,4BAAC4gC,kBAAgB,OAAA,OAAA,CAAA,GAAKhS,KAAK,CAAA,CAAI,CACD;AAGtC;;", "names": ["selectors", "classes", "styles", "name", "ClassNames", "asSelectors", "classNames", "Array", "_len", "_key", "arguments", "map", "c", "join", "stylesheet", "createSheet", "hidden", "display", "opacity", "pointerEvents", "visibility", "overflow", "commonStyles", "create", "_extends", "PickerStyleTag", "React", "suppressHydrationWarning", "dangerouslySetInnerHTML", "__html", "getStyle", "commonInteractionStyles", "categoryBtn", "backgroundPositionY", "hiddenOnSearch", "visibleOnSearchOnly", "hiddenOnReactions", "transition", "height", "width", "darkMode", "key", "value", "_eprDarkTheme", "_eprAutoTheme", "compareConfig", "prev", "next", "prevCustomEmojis", "_prev$customEmojis", "customEmojis", "nextCustomEmojis", "_next$customEmojis", "open", "emojiVersion", "reactionsDefaultOpen", "searchPlaceHolder", "searchPlaceholder", "defaultSkinTone", "skinTonesDisabled", "autoFocusSearch", "emojiStyle", "theme", "suggestedEmojisMode", "lazyLoadEmojis", "className", "style", "searchDisabled", "skinTonePickerLocation", "length", "DEFAULT_REACTIONS", "SuggestionMode", "EmojiStyle", "Theme", "SkinTones", "Categories", "SkinTonePickerLocation", "categoriesOrdered", "SUGGESTED", "CUSTOM", "SMILEYS_PEOPLE", "ANIMALS_NATURE", "FOOD_DRINK", "TRAVEL_PLACES", "ACTIVITIES", "OBJECTS", "SYMBOLS", "FLAGS", "SuggestedRecent", "name", "category", "configByCategory", "_configByCategory", "baseCategoriesConfig", "modifiers", "categoryFromCategoryConfig", "categoryNameFromCategoryConfig", "mergeCategoriesConfig", "userCategoriesConfig", "extra", "suggestionMode", "RECENT", "base", "_userCategoriesConfig", "getBaseConfigByCategory", "modifier", "Object", "assign", "CDN_URL_APPLE", "CDN_URL_FACEBOOK", "CDN_URL_TWITTER", "CDN_URL_GOOGLE", "cdnUrl", "TWITTER", "GOOGLE", "FACEBOOK", "APPLE", "skinToneVariations", "NEUTRAL", "LIGHT", "MEDIUM_LIGHT", "MEDIUM", "MEDIUM_DARK", "DARK", "skinTonesNamed", "entries", "reduce", "acc", "_ref", "skinTonesMapped", "mapped", "skinTone", "_Object$assign", "EmojiProperties", "alphaNumericEmojiIndex", "setTimeout", "allEmojis", "searchIndex", "emoji", "indexEmoji", "joinedNameString", "emojiNames", "flat", "toLowerCase", "replace", "split", "for<PERSON>ach", "char", "_alphaNumericEmojiInd", "emojiUnified", "_emoji$EmojiPropertie", "addedIn", "parseFloat", "added_in", "emojiName", "unifiedWithoutSkinTone", "unified", "splat", "_splat$splice", "splice", "emojiHasVariations", "_emojiVariationUnifie", "emojiVariationUnified", "emojisByCategory", "_emojis$category", "emojis", "emojiUrlByUnified", "emojiVariations", "_emoji$EmojiPropertie2", "variations", "find", "variation", "includes", "emojiByUnified", "allEmojisByUnified", "withoutSkinTone", "values", "setCustomEmojis", "emojiData", "customToRegularEmoji", "push", "names", "id", "imgUrl", "<PERSON><PERSON><PERSON>", "activeVariationFromUnified", "_unified$split", "suspectedSkinTone", "KNOWN_FAILING_EMOJIS", "DEFAULT_SEARCH_PLACEHOLDER", "SEARCH_RESULTS_NO_RESULTS_FOUND", "SEARCH_RESULTS_SUFFIX", "SEARCH_RESULTS_ONE_RESULT_FOUND", "SEARCH_RESULTS_MULTIPLE_RESULTS_FOUND", "mergeConfig", "userConfig", "basePickerConfig", "previewConfig", "_userConfig$previewCo", "config", "categories", "hidden<PERSON><PERSON><PERSON><PERSON>", "unicodeToHide", "add", "_config$customEmojis", "PREVIEW", "getEmojiUrl", "basePreviewConfig", "SEARCH", "FREQUENT", "Set", "reactions", "allowExpandReactions", "defaultEmoji", "defaultCaption", "showPreview", "ConfigContext", "PickerConfigProvider", "children", "_objectWithoutPropertiesLoose", "_excluded", "mergedConfig", "useSetConfig", "Provider", "_React$useState", "setMergedConfig", "usePickerConfig", "MutableConfigContext", "createContext", "useMutableConfig", "mutableConfig", "useContext", "useDefineMutableConfig", "MutableConfigRef", "useRef", "onEmojiClick", "emptyFunc", "onReactionClick", "onSkinToneChange", "useEffect", "current", "MOUSE_EVENT_SOURCE", "useSearchPlaceHolderConfig", "_usePickerConfig", "_find", "p", "useDefaultSkinToneConfig", "_usePickerConfig2", "useAllowExpandReactions", "_usePickerConfig3", "useSkinTonesDisabledConfig", "_usePickerConfig4", "useEmojiStyleConfig", "_usePickerConfig5", "useAutoFocusSearchConfig", "_usePickerConfig6", "useCategoriesConfig", "_usePickerConfig7", "useCustomEmojisConfig", "_usePickerConfig8", "useOpenConfig", "_usePickerConfig9", "useOnEmojiClickConfig", "mouseEventSource", "_useMutableConfig", "handler", "REACTIONS", "useOnSkinToneChangeConfig", "_useMutableConfig2", "usePreviewConfig", "_usePickerConfig10", "useThemeConfig", "_usePickerConfig11", "useSuggestedEmojisModeConfig", "_usePickerConfig12", "useLazyLoadEmojisConfig", "_usePickerConfig13", "useClassNameConfig", "_usePickerConfig14", "useStyleConfig", "_usePickerConfig15", "getDimension", "useReactionsOpenConfig", "_usePickerConfig16", "useEmojiVersionConfig", "_usePickerConfig17", "useSearchDisabledConfig", "_usePickerConfig18", "useSkinTonePickerLocationConfig", "_usePickerConfig19", "useUnicodeToHide", "_usePickerConfig20", "useReactionsConfig", "_usePickerConfig21", "useGetEmojiUrlConfig", "_usePickerConfig22", "dimensionConfig", "useSearchResultsConfig", "searchResultsCount", "hasResults", "isPlural", "toString", "useDebouncedState", "initialValue", "delay", "_useState", "useState", "state", "setState", "timer", "debouncedSetState", "Promise", "resolve", "clearTimeout", "_window", "window", "useIsUnicodeHidden", "has", "useDisallowedEmojis", "DisallowedEmojisRef", "emojiVersionConfig", "useMemo", "Number", "isNaN", "disallowed<PERSON><PERSON><PERSON><PERSON>", "addedInNewerVersion", "useIsEmojiDisallowed", "isUnicodeHidden", "isEmojiDisallowed", "Boolean", "supportedLevel", "useMarkInitialLoad", "dispatch", "PickerContextProvider", "filterRef", "disallowClickRef", "disallowMouseRef", "disallowedEmojisRef", "suggestedUpdateState", "Date", "now", "searchTerm", "skinToneFanOpenState", "activeSkinTone", "activeCategoryState", "emojisThatFailedToLoadState", "emojiVariationPickerState", "reactionsModeState", "isPastInitialLoad", "setIsPastInitialLoad", "<PERSON>er<PERSON>ontext", "undefined", "useFilterRef", "_React$useContext", "useDisallowClickRef", "_React$useContext2", "useDisallowMouseRef", "_React$useContext3", "useReactionsModeState", "_React$useContext4", "useSearchTermState", "_React$useContext5", "useActiveSkinToneState", "_React$useContext6", "useEmojisThatFailedToLoadState", "_React$useContext7", "useIsPastInitialLoad", "_React$useContext8", "useEmojiVariationPickerState", "_React$useContext9", "useSkinToneFanOpenState", "_React$useContext10", "useUpdateSuggested", "_React$useContext12", "suggestedUpdated", "setsuggestedUpdate", "updateSuggested", "useIsSearchMode", "_useSearchTermState", "focusElement", "element", "requestAnimationFrame", "focus", "focusPrevElementSibling", "previousElementSibling", "focusNextElementSibling", "nextElement<PERSON><PERSON>ling", "focusFirstElementChild", "first", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getActiveElement", "document", "activeElement", "ElementRefContextProvider", "Picker<PERSON>ain<PERSON><PERSON>", "AnchoredEmojiRef", "BodyRef", "SearchInputRef", "SkinTonePickerRef", "CategoryNavigationRef", "VariationPickerRef", "ReactionsRef", "ElementRefContext", "useElementRef", "usePickerMainRef", "useAnchoredEmojiRef", "useSetAnchoredEmojiRef", "target", "useBodyRef", "useReactionsRef", "useSearchInputRef", "useSkinTonePickerRef", "useCategoryNavigationRef", "useVariationPickerRef", "scrollTo", "root", "top", "$eprBody", "queryScrollBody", "scrollTop", "scrollBy", "by", "useScrollTo", "useCallback", "scrollEmojiAboveLabel", "isEmojiBehindLabel", "closest", "variationPicker", "scrollBody", "closestScrollBody", "emojiDistanceFromScrollTop", "categoryLabelHeight", "closestCategory", "focusFirstVisibleEmoji", "parent", "firstVisibleEmoji", "focusAndClickFirstVisibleEmoji", "first<PERSON><PERSON><PERSON>", "click", "focusLastVisibleEmoji", "lastVisibleEmoji", "focusNextVisibleEmoji", "nextVisibleEmoji", "nextCategory", "focusPrevVisibleEmoji", "prevVisibleEmoji", "prevCategory", "focusVisibleEmojiOneRowUp", "exitUp", "visibleEmojiOneRowUp", "focusVisibleEmojiOneRowDown", "visibleEmojiOneRowDown", "categoryContent", "closestCategoryContent", "indexInRow", "elementIndexInRow", "row", "rowNumber", "countInRow", "elementCountInRow", "prevVisibleCategory", "getElementInRow", "allVisibleEmojis", "getElementInPrevRow", "hasNextRow", "nextVisibleCategory", "itemInNextRow", "getElementInNextRow", "useCloseAllOpenToggles", "_useEmojiVariationPic", "setVariationPicker", "_useSkinToneFanOpenSt", "skinToneFanOpen", "setSkinToneFanOpen", "closeAllOpenToggles", "useHasOpenToggles", "_useEmojiVariationPic2", "_useSkinToneFanOpenSt2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useDisallowMouseMove", "DisallowMouseRef", "disallowMouseMove", "useAllowMouseMove", "allowMouseMove", "useIsMouseDisallowed", "isMouseDisallowed", "useOnMouseMove", "bodyRef", "addEventListener", "onMouseMove", "passive", "removeEventListener", "useFocusSearchInput", "useFocusSkinTonePicker", "useFocusCategoryNavigation", "useSetFilterRef", "setFilter", "setter", "useClearSearch", "applySearch", "useApplySearch", "focusSearchInput", "clearSearch", "useAppendSearch", "appendSearch", "str", "getNormalizedSearchTerm", "useFilter", "setFilterRef", "statusSearchResults", "getStatusSearchResults", "onChange", "inputValue", "filter", "nextValue", "longestMatch", "findLongestMatch", "filterEmojiObjectByKeyword", "_useSearchTermState2", "setSearchTerm", "then", "keyword", "filtered", "hasMatch", "some", "useIsEmojiFiltered", "_useFilterRef", "_useSearchTermState3", "isEmojiFilteredBySearchTerm", "_filter$searchTerm", "dict", "longestMatchingKey", "keys", "sort", "a", "b", "trim", "filterState", "_Object$entries", "useSetVariationPicker", "setAnchoredEmojiRef", "setEmojiVariationPicker", "_emojiFromElement", "emojiFromElement", "useIsSkinToneInSearch", "skinTonePickerLocationConfig", "useIsSkinToneInPreview", "KeyboardEvents", "useKeyboardNavigation", "usePickerMainKeyboardEvents", "useSearchInputKeyboardEvents", "useSkinTonePickerKeyboardEvents", "useCategoryNavigationKeyboardEvents", "useBodyKeyboardEvents", "onKeyDown", "event", "Escape", "preventDefault", "focusSkinTonePicker", "setSkinToneFanOpenState", "goDownFromSearchInput", "useGoDownFromSearchInput", "isSkinToneInSearch", "ArrowRight", "ArrowDown", "Enter", "isOpen", "setIsOpen", "isSkinToneInPreview", "onType", "useOnType", "ArrowLeft", "focusNextSkinTone", "focusPrevSkinTone", "ArrowUp", "goUpFromBody", "useGoUpFromBody", "buttonFromTarget", "Space", "focusCategoryNavigation", "isSearchMode", "goUpFromEmoji", "exitLeft", "currentSkinTone", "hasNextElementSibling", "hasModifier", "match", "metaKey", "ctrl<PERSON>ey", "altKey", "preloadEmoji", "NATIVE", "preloadedEmojs", "emojiUrl", "preloadImage", "url", "image", "Image", "src", "useOnFocus", "onFocus", "button", "DEFAULT_LABEL_HEIGHT", "<PERSON><PERSON><PERSON><PERSON>", "PickerRootElement", "_ref2", "_useReactionsModeStat", "reactionsMode", "searchModeActive", "_ref3", "styleProps", "cx", "styles", "main", "baseVariables", "darkTheme", "AUTO", "autoThemeDark", "_cx", "searchActive", "reactionsMenu", "ref", "DarkTheme", "emojiPicker", "position", "flexDirection", "borderWidth", "borderStyle", "borderRadius", "borderColor", "backgroundColor", "boxSizing", "fontFamily", "autoTheme", "<PERSON><PERSON>ilter", "parentWidth", "getBoundingClientRect", "elementWidth", "Math", "floor", "elementLeft", "left", "parentLeft", "elementHeight", "elementTop", "parentTop", "round", "parentHeight", "getRowElements", "elements", "elementsInRow", "lastRow", "firstElementIndex", "lastElementIndex", "slice", "getNextRowElements", "allElements", "currentRow", "nextRow", "rowElements", "index", "nextRowElements", "prevRowElements", "firstVisibleElementInContainer", "maxVisibilityDiffThreshold", "parentBottom", "bottom", "parentTopWithLabel", "getLabelHeight", "visibleElements", "elementBottom", "maxVisibilityDiffPixels", "clientHeight", "elementTopWithAllowedDiff", "elementBottomWithAllowedDiff", "parentNode", "labels", "from", "querySelectorAll", "label", "_i", "_labels", "EmojiButtonSelector", "VisibleEmojiSelector", "visible", "emojiElement", "_emojiElement$closest", "originalUnified", "originalUnifiedFromEmojiElement", "unifiedFromEmojiElement", "isEmojiElement", "matches", "_element$parentElemen", "parentElement", "_element$clientHeight", "emojiTrueOffsetTop", "labelHeight", "elementOffsetTop", "categoryWithoutLabel", "querySelector", "_category$clientHeigh", "_categoryWithoutLabel", "_closestScrollBody$sc", "_closestScrollBody", "_element$closest", "emojiTruOffsetLeft", "elementOffsetLeft", "_element$offsetTop", "offsetTop", "_element$offsetLeft", "offsetLeft", "_elementDataSetKey", "elementDataSetKey", "allUnifiedFromEmojiElement", "_elementDataSet$key", "elementDataSet", "_element$dataset", "dataset", "isVisibleEmoji", "classList", "contains", "isHidden", "_allEmojis$slice", "last", "parseNative<PERSON><PERSON><PERSON>", "hex", "String", "fromCodePoint", "parseInt", "SUGGESTED_LS_KEY", "getSuggested", "mode", "_window$localStorage$", "_window2", "localStorage", "recent", "JSON", "parse", "getItem", "count", "_unused", "setSuggested", "existing", "u", "nextList", "concat", "i", "original", "min", "_window3", "setItem", "stringify", "_unused2", "isCustomCategory", "isCustomEmoji", "useMouseDownHandlers", "ContainerRef", "mouseDownTimerRef", "_useActiveSkinToneSta", "_useUpdateSuggested", "activeEmojiStyle", "onClick", "_emojiFromEvent", "emojiFromEvent", "skinToneToUse", "emojiClickOutput", "onMouseDown", "_emojiFromEvent2", "onMouseUp", "confainerRef", "getImageUrl", "imageUrl", "isCustom", "<PERSON><PERSON>", "props", "type", "cursor", "border", "background", "outline", "ClickableEmojiButton", "_ref$showVariations", "showVariations", "hasVariations", "_ref$noBackground", "noBackground", "getAriaLabel", "_emojiNames$", "alignItems", "justifyContent", "max<PERSON><PERSON><PERSON>", "maxHeight", "content", "right", "borderLeft", "borderRight", "transform", "borderBottom", "zIndex", "emojiStyles", "external", "fontSize", "common", "alignSelf", "justifySelf", "EmojiImg", "_ref$lazyLoad", "lazyLoad", "onError", "alt", "emojiImag", "loading", "min<PERSON><PERSON><PERSON>", "minHeight", "padding", "NativeEmoji", "native<PERSON><PERSON>ji", "lineHeight", "textAlign", "letterSpacing", "View<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "size", "_ref$getEmojiUrl", "_useEmojisThatFailedT", "setEmojisThatFailedToLoad", "emojiToRender", "ClickableEmoji", "BtnPlus", "setReactionsMode", "title", "tabIndex", "plusSign", "color", "backgroundImage", "Plus", "backgroundRepeat", "backgroundSize", "Reactions", "reactionsOpen", "list", "reaction", "emojiButton", "listStyle", "margin", "useOnScroll", "onScroll", "useIsEmojiHidden", "emojisThatFailedToLoad", "isEmojiFiltered", "failedToLoad", "filteredOut", "EmojiCategory", "categoryConfig", "categoryName", "gridGap", "gridTemplateColumns", "fontWeight", "textTransform", "isEverMounted", "useIsEverMounted", "isMounted", "setIsMounted", "Suggested", "suggestedEmojisModeConfig", "suggested", "_getSuggested", "suggestedItem", "EmojiList", "renderdCategoriesCountRef", "emojiList", "RenderCategory", "isEmojiHidden", "emojisToPush", "hiddenCounter", "_isEmojiHidden", "isDisallowed", "Direction", "EmojiVariationPicker", "_useVariationPickerTo", "useVariationPickerTop", "getTop", "getMenuDirection", "getPointerStyle", "usePointerStyle", "pointerStyle", "Down", "pointingUp", "pointer", "clientWidth", "direction", "Up", "emojiOffsetTop", "_bodyRef$scrollTop", "buttonHeight", "boxShadow", "transform<PERSON><PERSON>in", "backgroundPosition", "SVGTriangle", "Body", "PICKER", "body", "flex", "overflowY", "overflowX", "detectEmojyPartiallyBelowFold", "buttonRect", "bodyRect", "y", "useEmojiPreviewEvents", "allow", "setPreviewEmoji", "onEscape", "onMouseOver", "onEnter", "onLeave", "e", "_allUnifiedFromEmojiE", "relatedTarget", "belowFoldByPx", "handlePartiallyVisibleElementFocus", "_allUnifiedFromEmojiE2", "_document$activeEleme", "blur", "FlexDirection", "Flex", "_ref$style", "_ref$direction", "ROW", "_stylesheet$create", "COLUMN", "Absolute", "Relative", "BtnSkinToneVariation", "isActive", "skinToneVariation", "tone", "closedTone", "active", "ITEM_SIZE", "SkinTonePickerMenu", "SkinTonePicker", "SkinTonePickerDirection", "VERTICAL", "HORIZONTAL", "isDisabled", "setActiveSkinTone", "fullWidth", "expandedSize", "vertical", "skinTones", "verticalShadow", "flexBasis", "select", "Preview", "preview", "PreviewBody", "previewEmoji", "variationPickerEmoji", "_previewEmoji$unified", "show", "PreviewContent", "defaultText", "borderTop", "categoryNameFromDom", "$category", "_$category$getAttribu", "getAttribute", "useActiveCategoryScrollDetection", "setActiveCategory", "visibleCategories", "Map", "observer", "IntersectionObserver", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "entry", "set", "intersectionRatio", "ratios", "lastCategory", "_ratios", "_ratios$_i", "ratio", "threshold", "el", "observe", "useScrollCategoryIntoView", "scrollCategoryIntoView", "_BodyRef$current", "useShouldHideCustomEmojis", "customCategoryConfig", "CategoryButton", "isActiveCategory", "allowNavigation", "catBtn", "role", "DarkActivePositionY", "DarkPositionY", "DarkInactivePosition", "SVGNavigation", "backgroundPositionX", "CategoryNavigation", "activeCategory", "categoriesConfig", "hideCustomCategory", "nav", "BtnClearSearch", "btnClearSearch", "icnClearnSearch", "HoverDark", "SVGTimes", "SCOPE", "EMOJI_BUTTON", "CATEGORY", "CssSearch", "q", "gen<PERSON><PERSON><PERSON>", "IcnSearch", "icnSearch", "SVGMagnifier", "SearchContainer", "overlay", "Search", "inc", "setInc", "placeholder", "autoFocus", "_useFilter", "input", "searchContainer", "search", "_event$target$value", "_event$target", "visuallyHidden", "clip", "clipPath", "whiteSpace", "Header", "EmojiPicker", "ContentControl", "renderAll", "setRenderAll", "ExpandedPickerContent", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_inherits<PERSON><PERSON>e", "_this", "call", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "_proto", "prototype", "componentDidCatch", "error", "errorInfo", "console", "render", "ExportedEmoji", "_ref$size", "_ref$emojiStyle", "EmojiPickerReact"]}